import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { ToolsService } from "../services/toolsService";
import { CategoriesService } from "../services/categoriesService";
import type { Tool, Category } from "../types";

export const useToolsStore = defineStore("tools", () => {
  // 状态
  const searchQuery = ref("");
  const selectedCategory = ref("all");
  const showFavoritesOnly = ref(false);
  const sidebarCollapsed = ref(false);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const initialized = ref(false);

  // 数据
  const tools = ref<Tool[]>([
    // 开发工具
    {
      id: "850e8400-e29b-41d4-a716-446655440001",
      name: "GitHub",
      description: "全球最大的代码托管平台，支持Git版本控制和团队协作开发",
      url: "https://github.com",
      icon: "💻",
      category_id: "550e8400-e29b-41d4-a716-446655440001",
      tags: ["代码托管", "Git", "协作", "开源"],
      is_featured: true,
      click_count: 156,
      is_favorite: false,
      status: "active",
      sort_order: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      meta_title: "GitHub - 代码托管平台",
      meta_description: "全球最大的代码托管平台，支持Git版本控制和开源协作",
    },
    {
      id: "850e8400-e29b-41d4-a716-446655440002",
      name: "Visual Studio Code",
      description: "微软开发的免费代码编辑器，支持多种编程语言和丰富的扩展",
      url: "https://code.visualstudio.com",
      icon: "💻",
      category_id: "550e8400-e29b-41d4-a716-446655440001",
      tags: ["编辑器", "开发", "免费", "微软"],
      is_featured: true,
      click_count: 234,
      is_favorite: false,
      status: "active",
      sort_order: 2,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      meta_title: "Visual Studio Code - 强大的代码编辑器",
      meta_description:
        "微软出品的轻量级但功能强大的代码编辑器，支持多种编程语言和丰富的插件生态",
    },
    {
      id: "850e8400-e29b-41d4-a716-446655440013",
      name: "Vercel",
      description: "现代化的前端部署平台，支持静态网站和Serverless函数",
      url: "https://vercel.com",
      icon: "💻",
      category_id: "550e8400-e29b-41d4-a716-446655440001",
      tags: ["部署", "前端", "Serverless", "静态网站"],
      is_featured: true,
      click_count: 128,
      is_favorite: false,
      status: "active",
      sort_order: 13,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      meta_title: "Vercel - 现代化部署平台",
      meta_description: "现代化的前端部署平台，支持静态网站和Serverless函数",
    },
    {
      id: "850e8400-e29b-41d4-a716-446655440014",
      name: "Netlify",
      description: "静态网站托管和部署平台，支持持续集成和CDN加速",
      url: "https://netlify.com",
      icon: "💻",
      category_id: "550e8400-e29b-41d4-a716-446655440001",
      tags: ["托管", "部署", "CDN", "静态网站"],
      is_featured: false,
      click_count: 95,
      is_favorite: false,
      status: "active",
      sort_order: 14,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      meta_title: "Netlify - 静态网站托管",
      meta_description: "静态网站托管和部署平台，支持持续集成和CDN加速",
    },
    {
      id: "850e8400-e29b-41d4-a716-446655440015",
      name: "Docker",
      description: "容器化平台，简化应用程序的打包、分发和部署",
      url: "https://docker.com",
      icon: "💻",
      category_id: "550e8400-e29b-41d4-a716-446655440001",
      tags: ["容器", "部署", "DevOps", "虚拟化"],
      is_featured: false,
      click_count: 87,
      is_favorite: false,
      status: "active",
      sort_order: 15,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      meta_title: "Docker - 容器化平台",
      meta_description: "容器化平台，简化应用程序的打包、分发和部署",
    },
    {
      id: "850e8400-e29b-41d4-a716-446655440016",
      name: "Stack Overflow",
      description: "程序员问答社区，解决编程问题的最佳平台",
      url: "https://stackoverflow.com",
      icon: "💻",
      category_id: "550e8400-e29b-41d4-a716-446655440001",
      tags: ["问答", "编程", "社区", "学习"],
      is_featured: true,
      click_count: 203,
      is_favorite: false,
      status: "active",
      sort_order: 16,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      meta_title: "Stack Overflow - 程序员问答社区",
      meta_description: "程序员问答社区，解决编程问题的最佳平台",
    },
    // 设计工具
    {
      id: "850e8400-e29b-41d4-a716-446655440003",
      name: "Figma",
      description: "协作式界面设计工具，支持实时协作和原型制作，设计师必备",
      url: "https://figma.com",
      icon: "🎨",
      category_id: "550e8400-e29b-41d4-a716-446655440002",
      tags: ["设计", "协作", "原型", "UI/UX"],
      is_featured: true,
      click_count: 189,
      is_favorite: false,
      status: "active",
      sort_order: 3,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      meta_title: "Figma - 协作设计工具",
      meta_description:
        "协作式界面设计工具，支持实时协作和原型制作，设计师必备",
    },
    {
      id: "850e8400-e29b-41d4-a716-446655440004",
      name: "Adobe Photoshop",
      description: "专业的图像编辑和设计软件，创意设计的行业标准",
      url: "https://www.adobe.com/products/photoshop.html",
      icon: "🎨",
      category_id: "550e8400-e29b-41d4-a716-446655440002",
      tags: ["图像编辑", "设计", "Adobe", "专业"],
      is_featured: false,
      click_count: 145,
      is_favorite: false,
      status: "active",
      sort_order: 4,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      meta_title: "Adobe Photoshop - 专业图像编辑",
      meta_description: "专业的图像编辑和设计软件，创意设计的行业标准",
    },
  ]);

  const categories = ref<Category[]>([
    {
      id: "1",
      name: "开发工具",
      description: "编程开发相关工具",
      icon: "💻",
      color: "#3498db",
      count: 5,
      sort_order: 1,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: "2",
      name: "设计工具",
      description: "UI/UX设计工具",
      icon: "🎨",
      color: "#e74c3c",
      count: 3,
      sort_order: 2,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ]);

  // 计算属性
  const filteredTools = computed(() => {
    let result = tools.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (tool) =>
          tool.name.toLowerCase().includes(query) ||
          tool.description.toLowerCase().includes(query) ||
          tool.tags.some((tag) => tag.toLowerCase().includes(query))
      );
    }

    if (selectedCategory.value !== "all") {
      result = result.filter(
        (tool) => tool.category_id === selectedCategory.value
      );
    }

    if (showFavoritesOnly.value) {
      result = result.filter((tool) => tool.isFavorite);
    }

    return result;
  });

  const favoriteTools = computed(() =>
    tools.value.filter((tool) => tool.isFavorite)
  );
  const popularTools = computed(() =>
    [...tools.value].sort((a, b) => b.click_count - a.click_count).slice(0, 5)
  );
  const featuredTools = computed(() =>
    tools.value.filter((tool) => tool.is_featured)
  );

  // 方法
  const initialize = async () => {
    if (initialized.value) return;

    try {
      loading.value = true;
      error.value = null;

      // 并行加载数据
      await Promise.all([loadTools(), loadCategories()]);

      initialized.value = true;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "初始化失败";
      console.error("Error initializing state:", err);
    } finally {
      loading.value = false;
    }
  };

  const loadTools = async () => {
    try {
      const toolsData = await ToolsService.getTools();
      tools.value = (toolsData as any).items
        ? (toolsData as any).items
        : toolsData;
    } catch (err) {
      throw new Error("加载工具失败");
    }
  };

  const loadCategories = async () => {
    try {
      const categoriesData = await CategoriesService.getCategories();
      categories.value = categoriesData;
    } catch (err) {
      throw new Error("加载分类失败");
    }
  };

  const toggleFavorite = async (toolId: string) => {
    const tool = tools.value.find((t) => t.id === toolId);
    if (tool) {
      tool.isFavorite = !tool.isFavorite;
      // TODO: 同步到后端
    }
  };

  const incrementClickCount = async (toolId: string) => {
    const tool = tools.value.find((t) => t.id === toolId);
    if (tool) {
      tool.click_count++;
      // TODO: 同步到后端
    }
  };

  const clearError = () => {
    error.value = null;
  };

  return {
    // 状态
    searchQuery,
    selectedCategory,
    showFavoritesOnly,
    sidebarCollapsed,
    loading,
    error,
    initialized,

    // 数据
    tools,
    categories,
    filteredTools,
    favoriteTools,
    popularTools,
    featuredTools,

    // 方法
    initialize,
    toggleFavorite,
    incrementClickCount,
    clearError,

    // Setter
    setSearchQuery: (query: string) => (searchQuery.value = query),
    setSelectedCategory: (category: string) =>
      (selectedCategory.value = category),
    setSidebarCollapsed: (collapsed: boolean) =>
      (sidebarCollapsed.value = collapsed),
    toggleSidebar: () => (sidebarCollapsed.value = !sidebarCollapsed.value),
  };
});
