const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-DjfxigXP.js","./vendor-DhoxJlSg.js","./EnhancedHomeView-BUwsZrKp.js","./categories-DbHgCJSf.js","./EnhancedHomeView-D92HhBEJ.css","./HomeView-BBHWoDcI.js","./HomeView-D2koxKt0.css","./ComparisonView-Botp5qEA.js","./ComparisonView-BBLNMYPy.css","./TestStatusBar-vfffXSNd.js","./TestStatusBar-k2A8_0Fe.css","./ToolsView-D5Tb4Zb4.js","./ToolsView-BBAZPdaa.css","./ProductsView-cbqokVhT.js","./ProductsView-DT_bfOFu.css","./ProductDetailView-DBRwGTHH.js","./ProductDetailView-B1YLHNwr.css","./UserView-Y3OJXVxS.js","./UserView-BGzqa1ua.css","./ProfileView-BsCeIPVZ.js","./ProfileView-By2k2COC.css","./FavoritesView-BHom3aRX.js","./FavoritesView-TeBpPaYE.css","./OrdersView-DxUfmtIo.js","./OrdersView-6R_zH4iJ.css","./AuthView-C5uDIFBe.js","./AuthView-kJTw_t9R.css","./LoginView-CDpJmh0U.js","./LoginView-DqUiEiAL.css","./RegisterView-DUzN9dY-.js","./RegisterView-BCFuTnS9.css","./ForgotPasswordView-GqlmKlR3.js","./ForgotPasswordView-CsrSL9dm.css","./AdminView-B2LVh3S-.js","./AdminView-D-c4a1ji.css","./DashboardView-XliPLf3G.js","./DashboardView-CsQ6cj5h.css","./AdminToolsView-29aLt44O.js","./AdminToolsView-BSH3NJ9N.css","./ProductsManageView-CFrNTjAK.js","./ProductsManageView-BMAVYlPv.css","./LocalManagementView-7tcF9oGw.js","./LocalManagementView-D0n_luqR.css","./LocalManagementTestView-PqG-fA7f.js","./LocalManagementTestView-I9ivud3V.css","./PaymentView-CvAFmR1Y.js","./PaymentView-Do9HTHpK.css","./PaymentSuccessView-BAAK5jOU.js","./PaymentSuccessView-BYCqjQvv.css","./PaymentCancelView-CFlX4BUb.js","./PaymentCancelView-5StAoE7K.css","./NotFoundView-CNLIsKc_.js","./NotFoundView-J7iPsJwS.css"])))=>i.map(i=>d[i]);
import{d as e,c as t,a as r,b as s,e as n,u as o,M as a,T as i,G as l,f as c,r as u,w as h,P as d,g as f,C as p,h as g,o as m,i as v,j as y,k as w,t as _,l as b,m as k,n as E,p as S,q as T,X as O,F as P,s as j,v as A,x as I,y as C,z as R,A as $,B as L,D as x,E as U,H as D,I as q,J as N,K as M,L as B,N as V,O as F,Q as G,R as z,S as W,U as J,V as H}from"./vendor-DhoxJlSg.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const K={class:"app-footer"},Q={class:"footer-content"},Y={class:"footer-main"},X={class:"footer-section company-info"},Z={class:"social-links"},ee={href:"#",class:"social-link",title:"微信"},te={href:"#",class:"social-link",title:"微博"},re={href:"#",class:"social-link",title:"GitHub"},se={href:"#",class:"social-link",title:"邮箱"},ne={class:"footer-section"},oe={class:"footer-links"},ae={class:"footer-section contact-info"},ie={class:"contact-item"},le={class:"contact-item"},ce={class:"contact-item"},ue={class:"contact-item"},he=(e,t)=>{const r=e.__vccOpts||e;for(const[s,n]of t)r[s]=n;return r},de=he(e({__name:"AppFooter",setup:e=>(e,v)=>{const y=u("router-link");return m(),t("footer",K,[r("div",Q,[r("div",Y,[r("div",X,[v[0]||(v[0]=r("div",{class:"company-logo"},[r("div",{class:"logo-icon"},"🚀"),r("div",{class:"logo-text"},[r("h3",null,"工具导航站"),r("p",null,"让工作更高效")])],-1)),v[1]||(v[1]=r("p",{class:"company-description"}," 专注于为用户提供优质的工具导航和产品展示服务，致力于提升工作效率，让每个人都能找到最适合的工具和产品。 ",-1)),r("div",Z,[r("a",ee,[n(o(a),{class:"icon"})]),r("a",te,[n(o(i),{class:"icon"})]),r("a",re,[n(o(l),{class:"icon"})]),r("a",se,[n(o(c),{class:"icon"})])])]),r("div",ne,[v[7]||(v[7]=r("h4",null,"快速导航",-1)),r("ul",oe,[r("li",null,[n(y,{to:"/"},{default:h(()=>v[2]||(v[2]=[g("首页")])),_:1,__:[2]})]),r("li",null,[n(y,{to:"/tools"},{default:h(()=>v[3]||(v[3]=[g("工具导航")])),_:1,__:[3]})]),r("li",null,[n(y,{to:"/products"},{default:h(()=>v[4]||(v[4]=[g("产品展示")])),_:1,__:[4]})]),r("li",null,[n(y,{to:"/user/favorites"},{default:h(()=>v[5]||(v[5]=[g("我的收藏")])),_:1,__:[5]})]),r("li",null,[n(y,{to:"/admin"},{default:h(()=>v[6]||(v[6]=[g("管理后台")])),_:1,__:[6]})])])]),v[13]||(v[13]=s('<div class="footer-section" data-v-88f57f9e><h4 data-v-88f57f9e>产品服务</h4><ul class="footer-links" data-v-88f57f9e><li data-v-88f57f9e><a href="#" data-v-88f57f9e>开发工具</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>设计工具</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>效率工具</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>AI工具</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>学习资源</a></li></ul></div><div class="footer-section" data-v-88f57f9e><h4 data-v-88f57f9e>帮助支持</h4><ul class="footer-links" data-v-88f57f9e><li data-v-88f57f9e><a href="#" data-v-88f57f9e>使用指南</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>常见问题</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>意见反馈</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>联系我们</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>服务条款</a></li></ul></div>',2)),r("div",ae,[v[12]||(v[12]=r("h4",null,"联系我们",-1)),r("div",ie,[n(o(d),{class:"contact-icon"}),v[8]||(v[8]=r("span",null,"400-123-4567",-1))]),r("div",le,[n(o(c),{class:"contact-icon"}),v[9]||(v[9]=r("span",null,"<EMAIL>",-1))]),r("div",ce,[n(o(f),{class:"contact-icon"}),v[10]||(v[10]=r("span",null,"北京市朝阳区科技园区",-1))]),r("div",ue,[n(o(p),{class:"contact-icon"}),v[11]||(v[11]=r("span",null,"工作时间：9:00-18:00",-1))])])]),v[14]||(v[14]=s('<div class="footer-bottom" data-v-88f57f9e><div class="footer-bottom-content" data-v-88f57f9e><div class="copyright" data-v-88f57f9e><p data-v-88f57f9e>© 2024 工具导航站. 保留所有权利.</p><p data-v-88f57f9e><a href="#" data-v-88f57f9e>隐私政策</a> | <a href="#" data-v-88f57f9e>服务条款</a> | <a href="#" data-v-88f57f9e>网站地图</a></p></div><div class="footer-stats" data-v-88f57f9e><div class="stat-item" data-v-88f57f9e><span class="stat-number" data-v-88f57f9e>1000+</span><span class="stat-label" data-v-88f57f9e>精选工具</span></div><div class="stat-item" data-v-88f57f9e><span class="stat-number" data-v-88f57f9e>50+</span><span class="stat-label" data-v-88f57f9e>工具分类</span></div><div class="stat-item" data-v-88f57f9e><span class="stat-number" data-v-88f57f9e>10000+</span><span class="stat-label" data-v-88f57f9e>用户使用</span></div></div></div></div>',1))])])}}),[["__scopeId","data-v-88f57f9e"]]),fe={class:"simple-status-bar"},pe={class:"simple-status-content"},ge=he(e({__name:"SimpleStatusBar",setup(e){const s=v(""),n=()=>{const e=new Date;s.value=e.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"})};let o=null;return y(()=>{n(),o=setInterval(n,1e3)}),w(()=>{o&&clearInterval(o)}),(e,n)=>(m(),t("div",fe,[r("div",pe,[n[0]||(n[0]=r("span",null,"🚀 工具导航站",-1)),r("span",null,_(s.value),1),n[1]||(n[1]=r("span",null,"状态栏测试",-1))])]))}}),[["__scopeId","data-v-a0f496da"]]),me={},ve=function(e,t,r){let s=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),n=document.querySelector("meta[property=csp-nonce]"),o=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));s=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,r),t in me)return;me[t]=!0;const s=t.endsWith(".css"),n=s?'[rel="stylesheet"]':"";if(!!r)for(let r=e.length-1;r>=0;r--){const n=e[r];if(n.href===t&&(!s||"stylesheet"===n.rel))return}else if(document.querySelector(`link[href="${t}"]${n}`))return;const a=document.createElement("link");return a.rel=s?"stylesheet":"modulepreload",s||(a.as="script"),a.crossOrigin="",a.href=t,o&&a.setAttribute("nonce",o),document.head.appendChild(a),s?new Promise((e,r)=>{a.addEventListener("load",e),a.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return s.then(t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)})};class ye extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class we extends ye{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class _e extends ye{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class be extends ye{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var ke,Ee;(Ee=ke||(ke={})).Any="any",Ee.ApNortheast1="ap-northeast-1",Ee.ApNortheast2="ap-northeast-2",Ee.ApSouth1="ap-south-1",Ee.ApSoutheast1="ap-southeast-1",Ee.ApSoutheast2="ap-southeast-2",Ee.CaCentral1="ca-central-1",Ee.EuCentral1="eu-central-1",Ee.EuWest1="eu-west-1",Ee.EuWest2="eu-west-2",Ee.EuWest3="eu-west-3",Ee.SaEast1="sa-east-1",Ee.UsEast1="us-east-1",Ee.UsWest1="us-west-1",Ee.UsWest2="us-west-2";var Se=function(e,t,r,s){return new(r||(r=Promise))(function(n,o){function a(e){try{l(s.next(e))}catch(t){o(t)}}function i(e){try{l(s.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,i)}l((s=s.apply(e,t||[])).next())})};class Te{constructor(e,{headers:t={},customFetch:r,region:s=ke.Any}={}){this.url=e,this.headers=t,this.region=s,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ve(async()=>{const{default:e}=await Promise.resolve().then(()=>Me);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)})(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return Se(this,void 0,void 0,function*(){try{const{headers:s,method:n,body:o}=t;let a,i={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(i["x-region"]=l),o&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(i["Content-Type"]="application/octet-stream",a=o):"string"==typeof o?(i["Content-Type"]="text/plain",a=o):"undefined"!=typeof FormData&&o instanceof FormData?a=o:(i["Content-Type"]="application/json",a=JSON.stringify(o)));const c=yield this.fetch(`${this.url}/${e}`,{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},i),this.headers),s),body:a}).catch(e=>{throw new we(e)}),u=c.headers.get("x-relay-error");if(u&&"true"===u)throw new _e(c);if(!c.ok)throw new be(c);let h,d=(null!==(r=c.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return h="application/json"===d?yield c.json():"application/octet-stream"===d?yield c.blob():"text/event-stream"===d?c:"multipart/form-data"===d?yield c.formData():yield c.text(),{data:h,error:null}}catch(s){return{data:null,error:s}}})}}var Oe="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Pe(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var s=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,s.get?s:{enumerable:!0,get:function(){return e[t]}})}),r}var je={},Ae={},Ie={},Ce={},Re={},$e={},Le=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const xe=Le.fetch,Ue=Le.fetch.bind(Le),De=Le.Headers,qe=Le.Request,Ne=Le.Response,Me=Object.freeze(Object.defineProperty({__proto__:null,Headers:De,Request:qe,Response:Ne,default:Ue,fetch:xe},Symbol.toStringTag,{value:"Module"})),Be=Pe(Me);var Ve={};Object.defineProperty(Ve,"__esModule",{value:!0});let Fe=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};Ve.default=Fe;var Ge=Oe&&Oe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty($e,"__esModule",{value:!0});const ze=Ge(Be),We=Ge(Ve);$e.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=ze.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,s;let n=null,o=null,a=null,i=e.status,l=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(o="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const s=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),c=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");s&&c&&c.length>1&&(a=parseInt(c[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(n={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,a=null,i=406,l="Not Acceptable"):o=1===o.length?o[0]:null)}else{const t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(o=[],n=null,i=200,l="OK")}catch(c){404===e.status&&""===t?(i=204,l="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null===(s=null==n?void 0:n.details)||void 0===s?void 0:s.includes("0 rows"))&&(n=null,i=200,l="OK"),n&&this.shouldThrowOnError)throw new We.default(n)}return{error:n,data:o,count:a,status:i,statusText:l}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,s;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(s=null==e?void 0:e.code)&&void 0!==s?s:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}};var Je=Oe&&Oe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Re,"__esModule",{value:!0});const He=Je($e);let Ke=class extends He.default{select(e){let t=!1;const r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:s,referencedTable:n=s}={}){const o=n?`${n}.order`:"order",a=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){const s=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:s=r}={}){const n=void 0===s?"offset":`${s}.offset`,o=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(o,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:s=!1,wal:n=!1,format:o="text"}={}){var a;const i=[e?"analyze":null,t?"verbose":null,r?"settings":null,s?"buffers":null,n?"wal":null].filter(Boolean).join("|"),l=null!==(a=this.headers.Accept)&&void 0!==a?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${l}"; options=${i};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};Re.default=Ke;var Qe=Oe&&Oe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ce,"__esModule",{value:!0});const Ye=Qe(Re);let Xe=class extends Ye.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const r=Array.from(new Set(t)).map(e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s}={}){let n="";"plain"===s?n="pl":"phrase"===s?n="ph":"websearch"===s&&(n="w");const o=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${n}fts${o}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}};Ce.default=Xe;var Ze=Oe&&Oe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ie,"__esModule",{value:!0});const et=Ze(Ce);Ie.default=class{constructor(e,{headers:t={},schema:r,fetch:s}){this.url=e,this.headers=t,this.schema=r,this.fetch=s}select(e,{head:t=!1,count:r}={}){const s=t?"HEAD":"GET";let n=!1;const o=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!n?"":('"'===e&&(n=!n),e)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new et.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){const s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new et.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:s,defaultToNull:n=!0}={}){const o=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),s&&o.push(`count=${s}`),n||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new et.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new et.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new et.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var tt={},rt={};Object.defineProperty(rt,"__esModule",{value:!0}),rt.version=void 0,rt.version="0.0.0-automated",Object.defineProperty(tt,"__esModule",{value:!0}),tt.DEFAULT_HEADERS=void 0;const st=rt;tt.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${st.version}`};var nt=Oe&&Oe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ae,"__esModule",{value:!0});const ot=nt(Ie),at=nt(Ce),it=tt;Ae.default=class e{constructor(e,{headers:t={},schema:r,fetch:s}={}){this.url=e,this.headers=Object.assign(Object.assign({},it.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=s}from(e){const t=new URL(`${this.url}/${e}`);return new ot.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:s=!1,count:n}={}){let o;const a=new URL(`${this.url}/rpc/${e}`);let i;r||s?(o=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{a.searchParams.append(e,t)})):(o="POST",i=t);const l=Object.assign({},this.headers);return n&&(l.Prefer=`count=${n}`),new at.default({method:o,url:a,headers:l,schema:this.schemaName,body:i,fetch:this.fetch,allowEmpty:!1})}};var lt=Oe&&Oe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(je,"__esModule",{value:!0}),je.PostgrestError=je.PostgrestBuilder=je.PostgrestTransformBuilder=je.PostgrestFilterBuilder=je.PostgrestQueryBuilder=je.PostgrestClient=void 0;const ct=lt(Ae);je.PostgrestClient=ct.default;const ut=lt(Ie);je.PostgrestQueryBuilder=ut.default;const ht=lt(Ce);je.PostgrestFilterBuilder=ht.default;const dt=lt(Re);je.PostgrestTransformBuilder=dt.default;const ft=lt($e);je.PostgrestBuilder=ft.default;const pt=lt(Ve);je.PostgrestError=pt.default;var gt=je.default={PostgrestClient:ct.default,PostgrestQueryBuilder:ut.default,PostgrestFilterBuilder:ht.default,PostgrestTransformBuilder:dt.default,PostgrestBuilder:ft.default,PostgrestError:pt.default};const{PostgrestClient:mt,PostgrestQueryBuilder:vt,PostgrestFilterBuilder:yt,PostgrestTransformBuilder:wt,PostgrestBuilder:_t,PostgrestError:bt}=gt;const kt=function(){if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}();var Et,St,Tt,Ot,Pt,jt,At,It,Ct,Rt,$t;(St=Et||(Et={}))[St.connecting=0]="connecting",St[St.open=1]="open",St[St.closing=2]="closing",St[St.closed=3]="closed",(Ot=Tt||(Tt={})).closed="closed",Ot.errored="errored",Ot.joined="joined",Ot.joining="joining",Ot.leaving="leaving",(jt=Pt||(Pt={})).close="phx_close",jt.error="phx_error",jt.join="phx_join",jt.reply="phx_reply",jt.leave="phx_leave",jt.access_token="access_token",(At||(At={})).websocket="websocket",(Ct=It||(It={})).Connecting="connecting",Ct.Open="open",Ct.Closing="closing",Ct.Closed="closed";class Lt{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const s=t.getUint8(1),n=t.getUint8(2);let o=this.HEADER_LENGTH+2;const a=r.decode(e.slice(o,o+s));o+=s;const i=r.decode(e.slice(o,o+n));o+=n;return{ref:null,topic:a,event:i,payload:JSON.parse(r.decode(e.slice(o,e.byteLength)))}}}class xt{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}($t=Rt||(Rt={})).abstime="abstime",$t.bool="bool",$t.date="date",$t.daterange="daterange",$t.float4="float4",$t.float8="float8",$t.int2="int2",$t.int4="int4",$t.int4range="int4range",$t.int8="int8",$t.int8range="int8range",$t.json="json",$t.jsonb="jsonb",$t.money="money",$t.numeric="numeric",$t.oid="oid",$t.reltime="reltime",$t.text="text",$t.time="time",$t.timestamp="timestamp",$t.timestamptz="timestamptz",$t.timetz="timetz",$t.tsrange="tsrange",$t.tstzrange="tstzrange";const Ut=(e,t,r={})=>{var s;const n=null!==(s=r.skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce((r,s)=>(r[s]=Dt(s,e,t,n),r),{})},Dt=(e,t,r,s)=>{const n=t.find(t=>t.name===e),o=null==n?void 0:n.type,a=r[e];return o&&!s.includes(o)?qt(o,a):Nt(a)},qt=(e,t)=>{if("_"===e.charAt(0)){const r=e.slice(1,e.length);return Ft(t,r)}switch(e){case Rt.bool:return Mt(t);case Rt.float4:case Rt.float8:case Rt.int2:case Rt.int4:case Rt.int8:case Rt.numeric:case Rt.oid:return Bt(t);case Rt.json:case Rt.jsonb:return Vt(t);case Rt.timestamp:return Gt(t);case Rt.abstime:case Rt.date:case Rt.daterange:case Rt.int4range:case Rt.int8range:case Rt.money:case Rt.reltime:case Rt.text:case Rt.time:case Rt.timestamptz:case Rt.timetz:case Rt.tsrange:case Rt.tstzrange:default:return Nt(t)}},Nt=e=>e,Mt=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Bt=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Vt=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Ft=(e,t)=>{if("string"!=typeof e)return e;const r=e.length-1,s=e[r];if("{"===e[0]&&"}"===s){let s;const o=e.slice(1,r);try{s=JSON.parse("["+o+"]")}catch(n){s=o?o.split(","):[]}return s.map(e=>qt(t,e))}return e},Gt=e=>"string"==typeof e?e.replace(" ","T"):e,zt=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Wt{constructor(e,t,r={},s=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Jt,Ht,Kt,Qt,Yt,Xt,Zt,er;(Ht=Jt||(Jt={})).SYNC="sync",Ht.JOIN="join",Ht.LEAVE="leave";class tr{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=tr.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=tr.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=tr.syncDiff(this.state,e,t,r),s())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){const n=this.cloneDeep(e),o=this.transformState(t),a={},i={};return this.map(n,(e,t)=>{o[e]||(i[e]=t)}),this.map(o,(e,t)=>{const r=n[e];if(r){const s=t.map(e=>e.presence_ref),n=r.map(e=>e.presence_ref),o=t.filter(e=>n.indexOf(e.presence_ref)<0),l=r.filter(e=>s.indexOf(e.presence_ref)<0);o.length>0&&(a[e]=o),l.length>0&&(i[e]=l)}else a[e]=t}),this.syncDiff(n,{joins:a,leaves:i},r,s)}static syncDiff(e,t,r,s){const{joins:n,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(n,(t,s)=>{var n;const o=null!==(n=e[t])&&void 0!==n?n:[];if(e[t]=this.cloneDeep(s),o.length>0){const r=e[t].map(e=>e.presence_ref),s=o.filter(e=>r.indexOf(e.presence_ref)<0);e[t].unshift(...s)}r(t,o,s)}),this.map(o,(t,r)=>{let n=e[t];if(!n)return;const o=r.map(e=>e.presence_ref);n=n.filter(e=>o.indexOf(e.presence_ref)<0),e[t]=n,s(t,n,r),0===n.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const s=e[r];return t[r]="metas"in s?s.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(Qt=Kt||(Kt={})).ALL="*",Qt.INSERT="INSERT",Qt.UPDATE="UPDATE",Qt.DELETE="DELETE",(Xt=Yt||(Yt={})).BROADCAST="broadcast",Xt.PRESENCE="presence",Xt.POSTGRES_CHANGES="postgres_changes",Xt.SYSTEM="system",(er=Zt||(Zt={})).SUBSCRIBED="SUBSCRIBED",er.TIMED_OUT="TIMED_OUT",er.CLOSED="CLOSED",er.CHANNEL_ERROR="CHANNEL_ERROR";class rr{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=Tt.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new Wt(this,Pt.join,this.params,this.timeout),this.rejoinTimer=new xt(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=Tt.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=Tt.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=Tt.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=Tt.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Pt.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new tr(this),this.broadcastEndpointURL=zt(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.state==Tt.closed){const{config:{broadcast:n,presence:o,private:a}}=this.params;this._onError(t=>null==e?void 0:e(Zt.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(Zt.CLOSED));const i={},l={broadcast:n,presence:o,postgres_changes:null!==(s=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==s?s:[],private:a};this.socket.accessTokenValue&&(i.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},i)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0!==t){const s=this.bindings.postgres_changes,n=null!==(r=null==s?void 0:s.length)&&void 0!==r?r:0,o=[];for(let r=0;r<n;r++){const n=s[r],{filter:{event:a,schema:i,table:l,filter:c}}=n,u=t&&t[r];if(!u||u.event!==a||u.schema!==i||u.table!==l||u.filter!==c)return this.unsubscribe(),this.state=Tt.errored,void(null==e||e(Zt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));o.push(Object.assign(Object.assign({},n),{id:u.id}))}return this.bindings.postgres_changes=o,void(e&&e(Zt.SUBSCRIBED))}null==e||e(Zt.SUBSCRIBED)}).receive("error",t=>{this.state=Tt.errored,null==e||e(Zt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(Zt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,s;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var s,n,o;const a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(o=null===(n=null===(s=this.params)||void 0===s?void 0:s.config)||void 0===n?void 0:n.broadcast)||void 0===o?void 0:o.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{const{event:o,payload:a}=e,i={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:a,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,i,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await(null===(s=e.body)||void 0===s?void 0:s.cancel()),e.ok?"ok":"error"}catch(n){return"AbortError"===n.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=Tt.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Pt.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(s=>{r=new Wt(this,Pt.leave,{},e),r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{null==r||r.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){const s=new AbortController,n=setTimeout(()=>s.abort(),r),o=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:s.signal}));return clearTimeout(n),o}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new Wt(this,e,t,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,n;const o=e.toLocaleLowerCase(),{close:a,error:i,leave:l,join:c}=Pt;if(r&&[a,i,l,c].indexOf(o)>=0&&r!==this._joinRef())return;let u=this._onMessage(o,t,r);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?null===(s=this.bindings.postgres_changes)||void 0===s||s.filter(e=>{var t,r,s;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(s=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===s?void 0:s.toLocaleLowerCase())===o}).map(e=>e.callback(u,r)):null===(n=this.bindings[o])||void 0===n||n.filter(e=>{var r,s,n,a,i,l;if(["broadcast","presence","postgres_changes"].includes(o)){if("id"in e){const o=e.id,a=null===(r=e.filter)||void 0===r?void 0:r.event;return o&&(null===(s=t.ids)||void 0===s?void 0:s.includes(o))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null===(n=t.data)||void 0===n?void 0:n.type.toLocaleLowerCase()))}{const r=null===(i=null===(a=null==e?void 0:e.filter)||void 0===a?void 0:a.event)||void 0===i?void 0:i.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===o}).map(e=>{if("object"==typeof u&&"ids"in u){const e=u.data,{schema:t,table:r,commit_timestamp:s,type:n,errors:o}=e,a={schema:t,table:r,commit_timestamp:s,eventType:n,new:{},old:{},errors:o};u=Object.assign(Object.assign({},a),this._getPayloadRecords(e))}e.callback(u,r)})}_isClosed(){return this.state===Tt.closed}_isJoined(){return this.state===Tt.joined}_isJoining(){return this.state===Tt.joining}_isLeaving(){return this.state===Tt.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const s=e.toLocaleLowerCase(),n={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(n):this.bindings[s]=[n],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var s;return!((null===(s=e.type)||void 0===s?void 0:s.toLocaleLowerCase())===r&&rr.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(Pt.close,{},e)}_onError(e){this._on(Pt.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Tt.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=Ut(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=Ut(e.columns,e.old_record)),t}}const sr=()=>{};class nr{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=sr,this.ref=0,this.logger=sr,this.conn=null,this.sendBuffer=[],this.serializer=new Lt,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ve(async()=>{const{default:e}=await import("./index-DjfxigXP.js");return{default:e}},__vite__mapDeps([0,1]),import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${At.websocket}`,this.httpEndpoint=zt(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const s=null===(r=null==t?void 0:t.params)||void 0===r?void 0:r.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new xt(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=kt),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Et.connecting:return It.Connecting;case Et.open:return It.Open;case Et.closing:return It.Closing;default:return It.Closed}}isConnected(){return this.connectionState()===It.Open}channel(e,t={config:{}}){const r=`realtime:${e}`,s=this.getChannels().find(e=>e.topic===r);if(s)return s;{const r=new rr(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){const{topic:t,event:r,payload:s,ref:n}=e,o=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${n})`,s),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{const r={access_token:t,version:"realtime-js/2.11.15"};t&&e.updateJoinPayload(r),e.joinedOnce&&e._isJoined()&&e._push(Pt.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:s,ref:n}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),n&&n===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${t} ${r} ${n&&"("+n+")"||""}`,s),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,s,n)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(Pt.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const r=e.match(/\?/)?"&":"?";return`${e}${r}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class or extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function ar(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class ir extends or{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class lr extends or{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var cr=function(e,t,r,s){return new(r||(r=Promise))(function(n,o){function a(e){try{l(s.next(e))}catch(t){o(t)}}function i(e){try{l(s.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,i)}l((s=s.apply(e,t||[])).next())})};const ur=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ve(async()=>{const{default:e}=await Promise.resolve().then(()=>Me);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},hr=e=>{if(Array.isArray(e))return e.map(e=>hr(e));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([e,r])=>{const s=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[s]=hr(r)}),t};var dr=function(e,t,r,s){return new(r||(r=Promise))(function(n,o){function a(e){try{l(s.next(e))}catch(t){o(t)}}function i(e){try{l(s.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,i)}l((s=s.apply(e,t||[])).next())})};const fr=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),pr=(e,t,r)=>dr(void 0,void 0,void 0,function*(){const s=yield cr(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield ve(()=>Promise.resolve().then(()=>Me),void 0,import.meta.url)).Response:Response});e instanceof s&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new ir(fr(r),e.status||500))}).catch(e=>{t(new lr(fr(e),e))}):t(new lr(fr(e),e))});function gr(e,t,r,s,n,o){return dr(this,void 0,void 0,function*(){return new Promise((a,i)=>{e(r,((e,t,r,s)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),s&&(n.body=JSON.stringify(s)),Object.assign(Object.assign({},n),r))})(t,s,n,o)).then(e=>{if(!e.ok)throw e;return(null==s?void 0:s.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>pr(e,i,s))})})}function mr(e,t,r,s){return dr(this,void 0,void 0,function*(){return gr(e,"GET",t,r,s)})}function vr(e,t,r,s,n){return dr(this,void 0,void 0,function*(){return gr(e,"POST",t,s,n,r)})}function yr(e,t,r,s,n){return dr(this,void 0,void 0,function*(){return gr(e,"DELETE",t,s,n,r)})}var wr=function(e,t,r,s){return new(r||(r=Promise))(function(n,o){function a(e){try{l(s.next(e))}catch(t){o(t)}}function i(e){try{l(s.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,i)}l((s=s.apply(e,t||[])).next())})};const _r={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},br={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class kr{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=ur(s)}uploadOrUpdate(e,t,r,s){return wr(this,void 0,void 0,function*(){try{let n;const o=Object.assign(Object.assign({},br),s);let a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(o.upsert)});const i=o.metadata;"undefined"!=typeof Blob&&r instanceof Blob?(n=new FormData,n.append("cacheControl",o.cacheControl),i&&n.append("metadata",this.encodeMetadata(i)),n.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(n=r,n.append("cacheControl",o.cacheControl),i&&n.append("metadata",this.encodeMetadata(i))):(n=r,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,i&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(i)))),(null==s?void 0:s.headers)&&(a=Object.assign(Object.assign({},a),s.headers));const l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:n,headers:a},(null==o?void 0:o.duplex)?{duplex:o.duplex}:{})),h=yield u.json();if(u.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(n){if(ar(n))return{data:null,error:n};throw n}})}upload(e,t,r){return wr(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,s){return wr(this,void 0,void 0,function*(){const n=this._removeEmptyFolders(e),o=this._getFinalPath(n),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:br.upsert},s),o=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r,e.append("cacheControl",t.cacheControl)):(e=r,o["cache-control"]=`max-age=${t.cacheControl}`,o["content-type"]=t.contentType);const i=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:o}),l=yield i.json();if(i.ok)return{data:{path:n,fullPath:l.Key},error:null};return{data:null,error:l}}catch(i){if(ar(i))return{data:null,error:i};throw i}})}createSignedUploadUrl(e,t){return wr(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e);const s=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(s["x-upsert"]="true");const n=yield vr(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),o=new URL(this.url+n.url),a=o.searchParams.get("token");if(!a)throw new or("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:a},error:null}}catch(r){if(ar(r))return{data:null,error:r};throw r}})}update(e,t,r){return wr(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return wr(this,void 0,void 0,function*(){try{return{data:yield vr(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(ar(s))return{data:null,error:s};throw s}})}copy(e,t,r){return wr(this,void 0,void 0,function*(){try{return{data:{path:(yield vr(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(ar(s))return{data:null,error:s};throw s}})}createSignedUrl(e,t,r){return wr(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),n=yield vr(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});const o=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${o}`)},{data:n,error:null}}catch(s){if(ar(s))return{data:null,error:s};throw s}})}createSignedUrls(e,t,r){return wr(this,void 0,void 0,function*(){try{const s=yield vr(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null})),error:null}}catch(s){if(ar(s))return{data:null,error:s};throw s}})}download(e,t){return wr(this,void 0,void 0,function*(){const r=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=s?`?${s}`:"";try{const t=this._getFinalPath(e),s=yield mr(this.fetch,`${this.url}/${r}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield s.blob(),error:null}}catch(o){if(ar(o))return{data:null,error:o};throw o}})}info(e){return wr(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield mr(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:hr(e),error:null}}catch(r){if(ar(r))return{data:null,error:r};throw r}})}exists(e){return wr(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,r,s){return dr(this,void 0,void 0,function*(){return gr(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),s)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(ar(r)&&r instanceof lr){const e=r.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:r}}throw r}})}getPublicUrl(e,t){const r=this._getFinalPath(e),s=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&s.push(n);const o=void 0!==(null==t?void 0:t.transform)?"render/image":"object",a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&s.push(a);let i=s.join("&");return""!==i&&(i=`?${i}`),{data:{publicUrl:encodeURI(`${this.url}/${o}/public/${r}${i}`)}}}remove(e){return wr(this,void 0,void 0,function*(){try{return{data:yield yr(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(ar(t))return{data:null,error:t};throw t}})}list(e,t,r){return wr(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},_r),t),{prefix:e||""});return{data:yield vr(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(ar(s))return{data:null,error:s};throw s}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Er={"X-Client-Info":"storage-js/2.7.1"};var Sr=function(e,t,r,s){return new(r||(r=Promise))(function(n,o){function a(e){try{l(s.next(e))}catch(t){o(t)}}function i(e){try{l(s.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,i)}l((s=s.apply(e,t||[])).next())})};class Tr{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},Er),t),this.fetch=ur(r)}listBuckets(){return Sr(this,void 0,void 0,function*(){try{return{data:yield mr(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(ar(e))return{data:null,error:e};throw e}})}getBucket(e){return Sr(this,void 0,void 0,function*(){try{return{data:yield mr(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(ar(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return Sr(this,void 0,void 0,function*(){try{return{data:yield vr(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(ar(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return Sr(this,void 0,void 0,function*(){try{const r=yield function(e,t,r,s,n){return dr(this,void 0,void 0,function*(){return gr(e,"PUT",t,s,n,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:r,error:null}}catch(r){if(ar(r))return{data:null,error:r};throw r}})}emptyBucket(e){return Sr(this,void 0,void 0,function*(){try{return{data:yield vr(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(ar(t))return{data:null,error:t};throw t}})}deleteBucket(e){return Sr(this,void 0,void 0,function*(){try{return{data:yield yr(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(ar(t))return{data:null,error:t};throw t}})}}class Or extends Tr{constructor(e,t={},r){super(e,t,r)}from(e){return new kr(this.url,this.headers,e,this.fetch)}}let Pr="";Pr="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const jr={headers:{"X-Client-Info":`supabase-js-${Pr}/2.50.2`}},Ar={schema:"public"},Ir={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Cr={};var Rr=function(e,t,r,s){return new(r||(r=Promise))(function(n,o){function a(e){try{l(s.next(e))}catch(t){o(t)}}function i(e){try{l(s.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,i)}l((s=s.apply(e,t||[])).next())})};const $r=(e,t,r)=>{const s=(e=>{let t;return t=e||("undefined"==typeof fetch?Ue:fetch),(...e)=>t(...e)})(r),n="undefined"==typeof Headers?De:Headers;return(r,o)=>Rr(void 0,void 0,void 0,function*(){var a;const i=null!==(a=yield t())&&void 0!==a?a:e;let l=new n(null==o?void 0:o.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${i}`),s(r,Object.assign(Object.assign({},o),{headers:l}))})};var Lr=function(e,t,r,s){return new(r||(r=Promise))(function(n,o){function a(e){try{l(s.next(e))}catch(t){o(t)}}function i(e){try{l(s.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,i)}l((s=s.apply(e,t||[])).next())})};const xr="2.70.0",Ur=3e4,Dr=9e4,qr={"X-Client-Info":`gotrue-js/${xr}`},Nr="X-Supabase-Api-Version",Mr={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},Br=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class Vr extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function Fr(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class Gr extends Vr{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class zr extends Vr{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Wr extends Vr{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}}class Jr extends Wr{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class Hr extends Wr{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Kr extends Wr{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Qr extends Wr{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Yr extends Wr{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Xr extends Wr{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Zr(e){return Fr(e)&&"AuthRetryableFetchError"===e.name}class es extends Wr{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class ts extends Wr{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const rs="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),ss=" \t\n\r=".split(""),ns=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<ss.length;t+=1)e[ss[t].charCodeAt(0)]=-2;for(let t=0;t<rs.length;t+=1)e[rs[t].charCodeAt(0)]=t;return e})();function os(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(rs[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(rs[e]),t.queuedBits-=6}}function as(e,t,r){const s=ns[e];if(!(s>-1)){if(-2===s)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function is(e){const t=[],r=e=>{t.push(String.fromCodePoint(e))},s={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},o=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return void r(e);for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,s,r)};for(let a=0;a<e.length;a+=1)as(e.charCodeAt(a),n,o);return t.join("")}function ls(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function cs(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};for(let n=0;n<e.length;n+=1)as(e.charCodeAt(n),r,s);return new Uint8Array(t)}function us(e){const t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){const t=1024*(s-55296)&65535;s=65536+(e.charCodeAt(r+1)-56320&65535|t),r+=1}ls(s,t)}}(e,e=>t.push(e)),new Uint8Array(t)}function hs(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};return e.forEach(e=>os(e,r,s)),os(null,r,s),t.join("")}const ds=()=>"undefined"!=typeof window&&"undefined"!=typeof document,fs={tested:!1,writable:!1},ps=()=>{if(!ds())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(fs.tested)return fs.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),fs.tested=!0,fs.writable=!0}catch(t){fs.tested=!0,fs.writable=!1}return fs.writable};const gs=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ve(async()=>{const{default:e}=await Promise.resolve().then(()=>Me);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},ms=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},vs=async(e,t)=>{const r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(s){return r}},ys=async(e,t)=>{await e.removeItem(t)};class ws{constructor(){this.promise=new ws.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function _s(e){const t=e.split(".");if(3!==t.length)throw new ts("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!Br.test(t[r]))throw new ts("JWT not in base64url format");return{header:JSON.parse(is(t[0])),payload:JSON.parse(is(t[1])),signature:cs(t[2]),raw:{header:t[0],payload:t[1]}}}function bs(e){return("0"+e.toString(16)).substr(-2)}async function ks(e){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),r=await crypto.subtle.digest("SHA-256",t),s=new Uint8Array(r);return Array.from(s).map(e=>String.fromCharCode(e)).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Es(e,t,r=!1){const s=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let r="";for(let s=0;s<56;s++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,bs).join("")}();let n=s;r&&(n+="/PASSWORD_RECOVERY"),await ms(e,`${t}-code-verifier`,n);const o=await ks(s);return[o,s===o?"plain":"s256"]}ws.promiseConstructor=Promise;const Ss=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const Ts=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function Os(e){if(!Ts.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}const Ps=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),js=[502,503,504];async function As(e){var t,r;if(!("object"==typeof(r=e)&&null!==r&&"status"in r&&"ok"in r&&"json"in r&&"function"==typeof r.json))throw new Xr(Ps(e),0);if(js.includes(e.status))throw new Xr(Ps(e),e.status);let s,n;try{s=await e.json()}catch(a){throw new zr(Ps(a),a)}const o=function(e){const t=e.headers.get(Nr);if(!t)return null;if(!t.match(Ss))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(a){return null}}(e);if(o&&o.getTime()>=Mr.timestamp&&"object"==typeof s&&s&&"string"==typeof s.code?n=s.code:"object"==typeof s&&s&&"string"==typeof s.error_code&&(n=s.error_code),n){if("weak_password"===n)throw new es(Ps(s),e.status,(null===(t=s.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===n)throw new Jr}else if("object"==typeof s&&s&&"object"==typeof s.weak_password&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new es(Ps(s),e.status,s.weak_password.reasons);throw new Gr(Ps(s),e.status||500,n)}async function Is(e,t,r,s){var n;const o=Object.assign({},null==s?void 0:s.headers);o[Nr]||(o[Nr]=Mr.name),(null==s?void 0:s.jwt)&&(o.Authorization=`Bearer ${s.jwt}`);const a=null!==(n=null==s?void 0:s.query)&&void 0!==n?n:{};(null==s?void 0:s.redirectTo)&&(a.redirect_to=s.redirectTo);const i=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await async function(e,t,r,s,n,o){const a=((e,t,r,s)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),n.body=JSON.stringify(s),Object.assign(Object.assign({},n),r))})(t,s,n,o);let i;try{i=await e(r,Object.assign({},a))}catch(l){throw console.error(l),new Xr(Ps(l),0)}i.ok||await As(i);if(null==s?void 0:s.noResolveJson)return i;try{return await i.json()}catch(l){await As(l)}}(e,t,r+i,{headers:o,noResolveJson:null==s?void 0:s.noResolveJson},{},null==s?void 0:s.body);return(null==s?void 0:s.xform)?null==s?void 0:s.xform(l):{data:Object.assign({},l),error:null}}function Cs(e){var t;let r=null;var s;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=(s=e.expires_in,Math.round(Date.now()/1e3)+s)));return{data:{session:r,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Rs(e){const t=Cs(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function $s(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Ls(e){return{data:e,error:null}}function xs(e){const{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:o}=e,a=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(e);n<s.length;n++)t.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]])}return r}(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:o},user:Object.assign({},a)},error:null}}function Us(e){return e}const Ds=["global","local","others"];class qs{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=gs(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=Ds[0]){if(Ds.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Ds.join(", ")}`);try{return await Is(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(r){if(Fr(r))return{data:null,error:r};throw r}}async inviteUserByEmail(e,t={}){try{return await Is(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:$s})}catch(r){if(Fr(r))return{data:{user:null},error:r};throw r}}async generateLink(e){try{const{options:t}=e,r=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(e);n<s.length;n++)t.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]])}return r}(e,["options"]),s=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(s.new_email=null==r?void 0:r.newEmail,delete s.newEmail),await Is(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:xs,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(Fr(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await Is(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:$s})}catch(t){if(Fr(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,r,s,n,o,a,i;try{const l={nextPage:null,lastPage:0,total:0},c=await Is(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(n=null===(s=null==e?void 0:e.perPage)||void 0===s?void 0:s.toString())&&void 0!==n?n:""},xform:Us});if(c.error)throw c.error;const u=await c.json(),h=null!==(o=c.headers.get("x-total-count"))&&void 0!==o?o:0,d=null!==(i=null===(a=c.headers.get("link"))||void 0===a?void 0:a.split(","))&&void 0!==i?i:[];return d.length>0&&(d.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(l){if(Fr(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){Os(e);try{return await Is(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:$s})}catch(t){if(Fr(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){Os(e);try{return await Is(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:$s})}catch(r){if(Fr(r))return{data:{user:null},error:r};throw r}}async deleteUser(e,t=!1){Os(e);try{return await Is(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:$s})}catch(r){if(Fr(r))return{data:{user:null},error:r};throw r}}async _listFactors(e){Os(e.userId);try{const{data:t,error:r}=await Is(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(t){if(Fr(t))return{data:null,error:t};throw t}}async _deleteFactor(e){Os(e.userId),Os(e.id);try{return{data:await Is(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(Fr(t))return{data:null,error:t};throw t}}}const Ns={getItem:e=>ps()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{ps()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{ps()&&globalThis.localStorage.removeItem(e)}};function Ms(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}const Bs=!!(globalThis&&ps()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class Vs extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class Fs extends Vs{}async function Gs(e,t,r){Bs&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const s=new globalThis.AbortController;return t>0&&setTimeout(()=>{s.abort(),Bs&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},async s=>{if(!s){if(0===t)throw Bs&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Fs(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Bs)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(n){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",n)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}Bs&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await r()}finally{Bs&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const zs={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:qr,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Ws(e,t,r){return await r()}class Js{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Js.nextInstanceID,Js.nextInstanceID+=1,this.instanceID>0&&ds()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},zs),e);if(this.logDebugMessages=!!s.debug,"function"==typeof s.debug&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new qs({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=gs(s.fetch),this.lock=s.lock||Ws,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:ds()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=Gs:this.lock=Ws,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:ps()?this.storage=Ns:(this.memoryStorage={},this.storage=Ms(this.memoryStorage)):(this.memoryStorage={},this.storage=Ms(this.memoryStorage)),ds()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(n){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",n)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${xr}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(s){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href);let r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),ds()&&this.detectSessionInUrl&&"none"!==r){const{data:s,error:n}=await this._getSessionFromURL(t,r);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),function(e){return Fr(e)&&"AuthImplicitGrantRedirectError"===e.name}(n)){const t=null===(e=n.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:n}}return await this._removeSession(),{error:n}}const{session:o,redirectType:a}=s;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),await this._saveSession(o),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return Fr(t)?{error:t}:{error:new zr("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,s;try{const n=await Is(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken}},xform:Cs}),{data:o,error:a}=n;if(a||!o)return{data:{user:null,session:null},error:a};const i=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",i)),{data:{user:l,session:i},error:null}}catch(n){if(Fr(n))return{data:{user:null,session:null},error:n};throw n}}async signUp(e){var t,r,s;try{let n;if("email"in e){const{email:r,password:s,options:o}=e;let a=null,i=null;"pkce"===this.flowType&&([a,i]=await Es(this.storage,this.storageKey)),n=await Is(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==o?void 0:o.emailRedirectTo,body:{email:r,password:s,data:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken},code_challenge:a,code_challenge_method:i},xform:Cs})}else{if(!("phone"in e))throw new Kr("You must provide either an email or phone number and a password");{const{phone:t,password:o,options:a}=e;n=await Is(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:o,data:null!==(r=null==a?void 0:a.data)&&void 0!==r?r:{},channel:null!==(s=null==a?void 0:a.channel)&&void 0!==s?s:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:Cs})}}const{data:o,error:a}=n;if(a||!o)return{data:{user:null,session:null},error:a};const i=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",i)),{data:{user:l,session:i},error:null}}catch(n){if(Fr(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithPassword(e){try{let t;if("email"in e){const{email:r,password:s,options:n}=e;t=await Is(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:Rs})}else{if(!("phone"in e))throw new Kr("You must provide either an email or phone number and a password");{const{phone:r,password:s,options:n}=e;t=await Is(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:Rs})}}const{data:r,error:s}=t;return s?{data:{user:null,session:null},error:s}:r&&r.session&&r.user?(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s}):{data:{user:null,session:null},error:new Hr}}catch(t){if(Fr(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,r,s,n;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:null===(n=e.options)||void 0===n?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,s,n,o,a,i,l,c,u,h,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{const{chain:h,wallet:d,statement:g,options:m}=e;let v;if(ds())if("object"==typeof d)v=d;else{const e=window;if(!("solana"in e)||"object"!=typeof e.solana||!("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");v=e.solana}else{if("object"!=typeof d||!(null==m?void 0:m.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");v=d}const y=new URL(null!==(t=null==m?void 0:m.url)&&void 0!==t?t:window.location.href);if("signIn"in v&&v.signIn){const e=await v.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null==m?void 0:m.signInWithSolana),{version:"1",domain:y.host,uri:y.href}),g?{statement:g}:null));let t;if(Array.isArray(e)&&e[0]&&"object"==typeof e[0])t=e[0];else{if(!(e&&"object"==typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"==typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"==typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in v&&"function"==typeof v.signMessage&&"publicKey"in v&&"object"==typeof v&&v.publicKey&&"toBase58"in v.publicKey&&"function"==typeof v.publicKey.toBase58))throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${y.host} wants you to sign in with your Solana account:`,v.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1",`URI: ${y.href}`,`Issued At: ${null!==(s=null===(r=null==m?void 0:m.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==s?s:(new Date).toISOString()}`,...(null===(n=null==m?void 0:m.signInWithSolana)||void 0===n?void 0:n.notBefore)?[`Not Before: ${m.signInWithSolana.notBefore}`]:[],...(null===(o=null==m?void 0:m.signInWithSolana)||void 0===o?void 0:o.expirationTime)?[`Expiration Time: ${m.signInWithSolana.expirationTime}`]:[],...(null===(a=null==m?void 0:m.signInWithSolana)||void 0===a?void 0:a.chainId)?[`Chain ID: ${m.signInWithSolana.chainId}`]:[],...(null===(i=null==m?void 0:m.signInWithSolana)||void 0===i?void 0:i.nonce)?[`Nonce: ${m.signInWithSolana.nonce}`]:[],...(null===(l=null==m?void 0:m.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${m.signInWithSolana.requestId}`]:[],...(null===(u=null===(c=null==m?void 0:m.signInWithSolana)||void 0===c?void 0:c.resources)||void 0===u?void 0:u.length)?["Resources",...m.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");const e=await v.signMessage((new TextEncoder).encode(f),"utf8");if(!(e&&e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:r}=await Is(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:hs(p)},(null===(h=e.options)||void 0===h?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:Cs});if(r)throw r;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}):{data:{user:null,session:null},error:new Hr}}catch(g){if(Fr(g))return{data:{user:null,session:null},error:g};throw g}}async _exchangeCodeForSession(e){const t=await vs(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(null!=t?t:"").split("/");try{const{data:t,error:n}=await Is(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:Cs});if(await ys(this.storage,`${this.storageKey}-code-verifier`),n)throw n;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=s?s:null}),error:n}):{data:{user:null,session:null,redirectType:null},error:new Hr}}catch(n){if(Fr(n))return{data:{user:null,session:null,redirectType:null},error:n};throw n}}async signInWithIdToken(e){try{const{options:t,provider:r,token:s,access_token:n,nonce:o}=e,a=await Is(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:n,nonce:o,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:Cs}),{data:i,error:l}=a;return l?{data:{user:null,session:null},error:l}:i&&i.session&&i.user?(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:i,error:l}):{data:{user:null,session:null},error:new Hr}}catch(t){if(Fr(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,r,s,n,o;try{if("email"in e){const{email:s,options:n}=e;let o=null,a=null;"pkce"===this.flowType&&([o,a]=await Es(this.storage,this.storageKey));const{error:i}=await Is(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},create_user:null===(r=null==n?void 0:n.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:o,code_challenge_method:a},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in e){const{phone:t,options:r}=e,{data:a,error:i}=await Is(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(s=null==r?void 0:r.data)&&void 0!==s?s:{},create_user:null===(n=null==r?void 0:r.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(o=null==r?void 0:r.channel)&&void 0!==o?o:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:i}}throw new Kr("You must provide either an email or phone number.")}catch(a){if(Fr(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(e){var t,r;try{let s,n;"options"in e&&(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo,n=null===(r=e.options)||void 0===r?void 0:r.captchaToken);const{data:o,error:a}=await Is(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:s,xform:Cs});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const i=o.session,l=o.user;return(null==i?void 0:i.access_token)&&(await this._saveSession(i),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",i)),{data:{user:l,session:i},error:null}}catch(s){if(Fr(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(e){var t,r,s;try{let n=null,o=null;return"pkce"===this.flowType&&([n,o]=await Es(this.storage,this.storageKey)),await Is(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:o}),headers:this.headers,xform:Ls})}catch(n){if(Fr(n))return{data:null,error:n};throw n}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new Jr;const{error:s}=await Is(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:s}})}catch(e){if(Fr(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:r,type:s,options:n}=e,{error:o}=await Is(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:r,type:s,options:n}=e,{data:o,error:a}=await Is(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new Kr("You must provide either an email or phone number and a type")}catch(t){if(Fr(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await vs(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const r=!!e.expires_at&&1e3*e.expires_at-Date.now()<Dr;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,s)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,s))})}return{data:{session:e},error:null}}const{session:s,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{session:null},error:n}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,async()=>await this._getUser())}async _getUser(e){try{return e?await Is(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:$s}):await this._useSession(async e=>{var t,r,s;const{data:n,error:o}=e;if(o)throw o;return(null===(t=n.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await Is(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(s=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0,xform:$s}):{data:{user:null},error:new Jr}})}catch(t){if(Fr(t))return function(e){return Fr(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await ys(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{const{data:s,error:n}=r;if(n)throw n;if(!s.session)throw new Jr;const o=s.session;let a=null,i=null;"pkce"===this.flowType&&null!=e.email&&([a,i]=await Es(this.storage,this.storageKey));const{data:l,error:c}=await Is(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:i}),jwt:o.access_token,xform:$s});if(c)throw c;return o.user=l.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(Fr(r))return{data:{user:null},error:r};throw r}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new Jr;const t=Date.now()/1e3;let r=t,s=!0,n=null;const{payload:o}=_s(e.access_token);if(o.exp&&(r=o.exp,s=r<=t),s){const{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};n=t}else{const{data:s,error:o}=await this._getUser(e.access_token);if(o)throw o;n={access_token:e.access_token,refresh_token:e.refresh_token,user:s.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(Fr(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){const{data:s,error:n}=t;if(n)throw n;e=null!==(r=s.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new Jr;const{session:s,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(Fr(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!ds())throw new Qr("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Qr(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new Yr("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new Qr("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Yr("No code detected.");const{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;const s=new URL(window.location.href);return s.searchParams.delete("code"),window.history.replaceState(window.history.state,"",s.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:s,access_token:n,refresh_token:o,expires_in:a,expires_at:i,token_type:l}=e;if(!(n&&a&&o&&l))throw new Qr("No session defined in URL");const c=Math.round(Date.now()/1e3),u=parseInt(a);let h=c+u;i&&(h=parseInt(i));const d=h-c;1e3*d<=Ur&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${u}s`);const f=h-u;c-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,h,c):c-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,h,c);const{data:p,error:g}=await this._getUser(n);if(g)throw g;const m={provider_token:r,provider_refresh_token:s,access_token:n,expires_in:u,expires_at:h,refresh_token:o,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(r){if(Fr(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await vs(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;const{data:s,error:n}=t;if(n)return{error:n};const o=null===(r=s.session)||void 0===r?void 0:r.access_token;if(o){const{error:t}=await this.admin.signOut(o,e);if(t&&(!function(e){return Fr(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await ys(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,s;try{const{data:{session:s},error:n}=t;if(n)throw n;await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",e,"session",s)}catch(n){await(null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",n),console.error(n)}})}async resetPasswordForEmail(e,t={}){let r=null,s=null;"pkce"===this.flowType&&([r,s]=await Es(this.storage,this.storageKey,!0));try{return await Is(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(n){if(Fr(n))return{data:null,error:n};throw n}}async getUserIdentities(){var e;try{const{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(Fr(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:r,error:s}=await this._useSession(async t=>{var r,s,n,o,a;const{data:i,error:l}=t;if(l)throw l;const c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(n=e.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:!0});return await Is(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(a=null===(o=i.session)||void 0===o?void 0:o.access_token)&&void 0!==a?a:void 0})});if(s)throw s;return ds()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(r){if(Fr(r))return{data:{provider:e.provider,url:null},error:r};throw r}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,s;const{data:n,error:o}=t;if(o)throw o;return await Is(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(s=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0})})}catch(t){if(Fr(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const n=Date.now();return await(r=async r=>(r>0&&await async function(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await Is(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:Cs})),s=(e,t)=>{const r=200*Math.pow(2,e);return t&&Zr(t)&&Date.now()+r-n<Ur},new Promise((e,t)=>{(async()=>{for(let o=0;o<1/0;o++)try{const t=await r(o);if(!s(o,null,t))return void e(t)}catch(n){if(!s(o,n))return void t(n)}})()}))}catch(n){if(this._debug(t,"error",n),Fr(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(t,"end")}var r,s}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),ds()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const r=await vs(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r))return this._debug(t,"session is not valid"),void(null!==r&&await this._removeSession());const s=1e3*(null!==(e=r.expires_at)&&void 0!==e?e:1/0)-Date.now()<Dr;if(this._debug(t,`session has${s?"":" not"} expired with margin of 90000s`),s){if(this.autoRefreshToken&&r.refresh_token){const{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),Zr(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){return this._debug(t,"error",r),void console.error(r)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new Jr;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new ws;const{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new Jr;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const s={session:t.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(n){if(this._debug(s,"error",n),Fr(n)){const e={session:null,error:n};return Zr(n)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(n),n}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(e,t,r=!0){const s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});const s=[],n=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(n){s.push(n)}});if(await Promise.all(n),s.length>0){for(let e=0;e<s.length;e+=1)console.error(s[e]);throw s[0]}}finally{this._debug(s,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await ms(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await ys(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&ds()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),Ur);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async e=>{const{data:{session:r}}=e;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const s=Math.floor((1e3*r.expires_at-t)/Ur);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),s<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(!(e.isAcquireTimeout||e instanceof Vs))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!ds()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){const s=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){const[e,t]=await Es(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});s.push(r.toString())}if(null==r?void 0:r.queryParams){const e=new URLSearchParams(r.queryParams);s.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${s.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;return n?{data:null,error:n}:await Is(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(t){if(Fr(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var r,s;const{data:n,error:o}=t;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:i,error:l}=await Is(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null===(r=null==n?void 0:n.session)||void 0===r?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(s=null==i?void 0:i.totp)||void 0===s?void 0:s.qr_code)&&(i.totp.qr_code=`data:image/svg+xml;utf-8,${i.totp.qr_code}`),{data:i,error:null})})}catch(t){if(Fr(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;if(n)return{data:null,error:n};const{data:o,error:a}=await Is(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})})}catch(t){if(Fr(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;return n?{data:null,error:n}:await Is(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(t){if(Fr(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const r=(null==e?void 0:e.factors)||[],s=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),n=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:s,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;const{data:{session:s},error:n}=e;if(n)return{data:null,error:n};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=_s(s.access_token);let a=null;o.aal&&(a=o.aal);let i=a;(null!==(r=null===(t=s.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(i="aal2");return{data:{currentLevel:a,nextLevel:i,currentAuthenticationMethods:o.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r)return r;if(r=this.jwks.keys.find(t=>t.kid===e),r&&this.jwks_cached_at+6e5>Date.now())return r;const{data:s,error:n}=await Is(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(n)throw n;if(!s.keys||0===s.keys.length)throw new ts("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),r=s.keys.find(t=>t.kid===e),!r)throw new ts("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}const{header:s,payload:n,signature:o,raw:{header:a,payload:i}}=_s(r);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(n.exp),!s.kid||"HS256"===s.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:n,header:s,signature:o},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(s.alg),c=await this.fetchJwk(s.kid,t),u=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(await crypto.subtle.verify(l,u,o,us(`${a}.${i}`))))throw new ts("Invalid JWT signature");return{data:{claims:n,header:s,signature:o},error:null}}catch(r){if(Fr(r))return{data:null,error:r};throw r}}}Js.nextInstanceID=0;const Hs=Js;class Ks extends Hs{constructor(e){super(e)}}var Qs=function(e,t,r,s){return new(r||(r=Promise))(function(n,o){function a(e){try{l(s.next(e))}catch(t){o(t)}}function i(e){try{l(s.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,i)}l((s=s.apply(e,t||[])).next())})};class Ys{constructor(e,t,r){var s,n,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const a=(i=e).endsWith("/")?i:i+"/";var i;const l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,u=function(e,t){var r,s;const{db:n,auth:o,realtime:a,global:i}=e,{db:l,auth:c,realtime:u,global:h}=t,d={db:Object.assign(Object.assign({},l),n),auth:Object.assign(Object.assign({},c),o),realtime:Object.assign(Object.assign({},u),a),global:Object.assign(Object.assign(Object.assign({},h),i),{headers:Object.assign(Object.assign({},null!==(r=null==h?void 0:h.headers)&&void 0!==r?r:{}),null!==(s=null==i?void 0:i.headers)&&void 0!==s?s:{})}),accessToken:()=>Lr(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:Ar,realtime:Cr,auth:Object.assign(Object.assign({},Ir),{storageKey:c}),global:jr});this.storageKey=null!==(s=u.auth.storageKey)&&void 0!==s?s:"",this.headers=null!==(n=u.global.headers)&&void 0!==n?n:{},u.accessToken?(this.accessToken=u.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(o=u.auth)&&void 0!==o?o:{},this.headers,u.global.fetch),this.fetch=$r(t,this._getAccessToken.bind(this),u.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},u.realtime)),this.rest=new mt(new URL("rest/v1",l).href,{headers:this.headers,schema:u.db.schema,fetch:this.fetch}),u.accessToken||this._listenForAuthEvents()}get functions(){return new Te(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Or(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return Qs(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:n,flowType:o,lock:a,debug:i},l,c){const u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Ks({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:o,lock:a,debug:i,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new nr(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===r?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=r}}const Xs=new Ys("https://fytiwsutzgmygfxnqoft.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ5dGl3c3V0emdteWdmeG5xb2Z0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MDM1ODcsImV4cCI6MjA2NjM3OTU4N30.LM9vazR9QCZ4vLC_Q1lJmtCj3pEVqM6vpW4TKzntAQA",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}});const Zs={TOOLS:"tools",CATEGORIES:"categories",PRODUCTS:"products",PRODUCT_CATEGORIES:"product_categories",USERS:"users",USER_PROFILES:"user_profiles",FAVORITES:"favorites",ORDERS:"orders",ORDER_ITEMS:"order_items",PAYMENTS:"payments",REVIEWS:"reviews",TAGS:"tags",TOOL_TAGS:"tool_tags",ANALYTICS:"analytics"},en=e=>(console.error("Supabase Error:",e),(null==e?void 0:e.message)?e.message:(null==e?void 0:e.error_description)?e.error_description:"操作失败，请稍后重试");class tn{static async getCurrentUser(){try{const{data:{user:e}}=await Xs.auth.getUser();if(!e)return null;const{data:t,error:r}=await Xs.from("user_profiles").select("*").eq("id",e.id).single();if(r){if("PGRST116"===r.code)return this.createUserProfile(e.id,e.email);throw r}return this.transformUser(t)}catch(e){return console.error("获取用户信息失败:",e),null}}static async getUserProfile(e){try{const{data:t,error:r}=await Xs.from("user_profiles").select("*").eq("id",e).single();if(r)throw r;return t?this.transformUser(t):null}catch(t){return console.error("获取用户资料失败:",t),null}}static async updateProfile(e,t){try{const r={full_name:t.full_name,username:t.username,bio:t.bio,website:t.website,location:t.location,updated_at:(new Date).toISOString()};if(t.avatar){const s=await this.uploadAvatar(e,t.avatar);r.avatar_url=s}const{data:s,error:n}=await Xs.from("user_profiles").update(r).eq("id",e).select("*").single();if(n)throw n;if(!s)throw new Error("更新用户资料失败");return this.transformUser(s)}catch(r){throw console.error("更新用户资料失败:",r),new Error("更新用户资料失败")}}static async createUserProfile(e,t){try{const r={id:e,email:t,role:"user",is_active:!0,email_verified:!1},{data:s,error:n}=await Xs.from("user_profiles").insert(r).select("*").single();if(n)throw n;if(!s)throw new Error("创建用户资料失败");return this.transformUser(s)}catch(r){throw console.error("创建用户资料失败:",r),new Error("创建用户资料失败")}}static async uploadAvatar(e,t){try{const r=t.name.split(".").pop(),s=`avatars/${`${e}-${Date.now()}.${r}`}`,{data:n}=await Xs.storage.from("avatars").list("",{search:e});if(n&&n.length>0){const e=n.map(e=>e.name);await Xs.storage.from("avatars").remove(e)}const{error:o}=await Xs.storage.from("avatars").upload(s,t);if(o)throw o;const{data:a}=Xs.storage.from("avatars").getPublicUrl(s);return a.publicUrl}catch(r){throw console.error("上传头像失败:",r),new Error("上传头像失败")}}static async updateLastLogin(e){try{const{error:t}=await Xs.from("user_profiles").update({last_login_at:(new Date).toISOString()}).eq("id",e);if(t)throw t}catch(t){console.error("更新最后登录时间失败:",t)}}static async checkUsernameAvailability(e,t){try{let r=Xs.from("user_profiles").select("id").eq("username",e);t&&(r=r.neq("id",t));const{data:s,error:n}=await r;if(n)throw n;return!s||0===s.length}catch(r){return console.error("检查用户名可用性失败:",r),!1}}static async getUserStats(e){try{const[t,r]=await Promise.all([Xs.from("favorites").select("tool_id, product_id").eq("user_id",e),Xs.from("orders").select("total_amount, status").eq("user_id",e)]),s=t.data||[],n=r.data||[],o=s.filter(e=>e.tool_id).length,a=s.filter(e=>e.product_id).length,i=n.length;return{favoriteToolsCount:o,favoriteProductsCount:a,ordersCount:i,totalSpent:n.filter(e=>"paid"===e.status).reduce((e,t)=>e+t.total_amount,0)}}catch(t){return console.error("获取用户统计信息失败:",t),{favoriteToolsCount:0,favoriteProductsCount:0,ordersCount:0,totalSpent:0}}}static async deleteAccount(e){try{await Promise.all([Xs.from("favorites").delete().eq("user_id",e),Xs.from("orders").delete().eq("user_id",e),Xs.from("user_profiles").delete().eq("id",e)]);const{error:t}=await Xs.auth.admin.deleteUser(e);if(t)throw t}catch(t){throw console.error("删除用户账户失败:",t),new Error("删除用户账户失败")}}static transformUser(e){return{id:e.id||void 0,email:e.email||void 0,username:e.username||void 0,full_name:e.full_name||void 0,avatar_url:e.avatar_url||void 0,bio:e.bio||void 0,website:e.website||void 0,location:e.location||void 0,role:e.role||void 0,is_active:e.is_active||void 0,emailVerified:e.email_verified||void 0,created_at:e.created_at||void 0,updated_at:e.updated_at||void 0,last_login_at:e.last_login_at||void 0}}}class rn{static async login(e){try{const{data:t,error:r}=await Xs.auth.signInWithPassword({email:e.email,password:e.password});if(r)throw r;if(!t.user)throw new Error("登录失败");await tn.updateLastLogin(t.user.id);const s=await tn.getCurrentUser();if(!s)throw new Error("获取用户信息失败");return{user:s,session:t.session}}catch(t){throw console.error("登录失败:",t),new Error(t instanceof Error?t.message:"登录失败")}}static async register(e){try{if(e.username){if(!(await tn.checkUsernameAvailability(e.username)))throw new Error("用户名已被使用")}const{data:t,error:r}=await Xs.auth.signUp({email:e.email,password:e.password,options:{data:{full_name:e.full_name,username:e.username}}});if(r)throw r;if(!t.user)throw new Error("注册失败");const s=await tn.createUserProfile(t.user.id,e.email);if(e.full_name||e.username){return{user:await tn.updateProfile(t.user.id,{full_name:e.full_name,username:e.username}),session:t.session}}return{user:s,session:t.session}}catch(t){throw console.error("注册失败:",t),new Error(t instanceof Error?t.message:"注册失败")}}static async logout(){try{const{error:e}=await Xs.auth.signOut();if(e)throw e}catch(e){throw console.error("登出失败:",e),new Error("登出失败")}}static async forgotPassword(e){try{const{error:t}=await Xs.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/auth/reset-password`});if(t)throw t}catch(t){throw console.error("发送重置密码邮件失败:",t),new Error("发送重置密码邮件失败")}}static async resetPassword(e){try{const{error:t}=await Xs.auth.updateUser({password:e});if(t)throw t}catch(t){throw console.error("重置密码失败:",t),new Error("重置密码失败")}}static async changePassword(e){try{const{error:t}=await Xs.auth.updateUser({password:e});if(t)throw t}catch(t){throw console.error("更改密码失败:",t),new Error("更改密码失败")}}static async updateEmail(e){try{const{error:t}=await Xs.auth.updateUser({email:e});if(t)throw t}catch(t){throw console.error("更新邮箱失败:",t),new Error("更新邮箱失败")}}static async verifyEmail(e,t){try{const{error:r}=await Xs.auth.verifyOtp({token_hash:e,type:t});if(r)throw r}catch(r){throw console.error("验证邮箱失败:",r),new Error("验证邮箱失败")}}static async resendVerificationEmail(){try{const{data:{user:e}}=await Xs.auth.getUser();if(!e)throw new Error("用户未登录");const{error:t}=await Xs.auth.resend({type:"signup",email:e.email});if(t)throw t}catch(e){throw console.error("重新发送验证邮件失败:",e),new Error("重新发送验证邮件失败")}}static async getSession(){try{const{data:{session:e}}=await Xs.auth.getSession();return e}catch(e){return console.error("获取会话失败:",e),null}}static async refreshSession(){try{const{data:e,error:t}=await Xs.auth.refreshSession();if(t)throw t;return e.session}catch(e){throw console.error("刷新会话失败:",e),new Error("刷新会话失败")}}static async isAuthenticated(){try{const{data:{user:e}}=await Xs.auth.getUser();return!!e}catch(e){return!1}}static onAuthStateChange(e){return Xs.auth.onAuthStateChange(e)}static async signInWithGoogle(){try{const{error:e}=await Xs.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(e)throw e}catch(e){throw console.error("Google 登录失败:",e),new Error("Google 登录失败")}}static async signInWithGitHub(){try{const{error:e}=await Xs.auth.signInWithOAuth({provider:"github",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(e)throw e}catch(e){throw console.error("GitHub 登录失败:",e),new Error("GitHub 登录失败")}}static async deleteAccount(){try{const{data:{user:e}}=await Xs.auth.getUser();if(!e)throw new Error("用户未登录");await tn.deleteAccount(e.id)}catch(e){throw console.error("删除账户失败:",e),new Error("删除账户失败")}}}const sn=b("auth",()=>{const e=v(null),t=v(null),r=v(!1),s=v(null),n=v(!1),o=k(()=>!!e.value&&!!t.value),a=k(()=>{var t,r;return"admin"===(null==(t=e.value)?void 0:t.role)||"super_admin"===(null==(r=e.value)?void 0:r.role)}),i=k(()=>{var t;return(null==(t=e.value)?void 0:t.full_name)?e.value.full_name.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2):"U"}),l=async()=>{try{const r=await tn.getCurrentUser(),s=await rn.getSession();return r&&s?(e.value=r,t.value=s,!0):(e.value=null,t.value=null,!1)}catch(r){return console.error("检查认证状态失败:",r),e.value=null,t.value=null,!1}},c=()=>{if(!o.value)throw new Error("需要登录才能访问")};return{user:e,session:t,loading:r,error:s,initialized:n,isAuthenticated:o,isAdmin:a,userInitials:i,login:async n=>{try{r.value=!0,s.value=null;const o=await rn.login(n);return e.value=o.user,t.value=o.session,o}catch(o){throw s.value=o instanceof Error?o.message:"登录失败",o}finally{r.value=!1}},register:async n=>{try{r.value=!0,s.value=null;const o=await rn.register(n);return e.value=o.user,t.value=o.session,o}catch(o){throw s.value=o instanceof Error?o.message:"注册失败",o}finally{r.value=!1}},logout:async()=>{try{r.value=!0,s.value=null,await rn.logout(),e.value=null,t.value=null}catch(n){throw s.value=n instanceof Error?n.message:"登出失败",n}finally{r.value=!1}},forgotPassword:async e=>{try{r.value=!0,s.value=null,await rn.forgotPassword(e)}catch(t){throw s.value=t instanceof Error?t.message:"发送重置邮件失败",t}finally{r.value=!1}},resetPassword:async e=>{try{r.value=!0,s.value=null,await rn.resetPassword(e)}catch(t){throw s.value=t instanceof Error?t.message:"重置密码失败",t}finally{r.value=!1}},updateProfile:async t=>{try{if(r.value=!0,s.value=null,!e.value)throw new Error("用户未登录");const n=await tn.updateProfile(e.value.id,t);return e.value=n,n}catch(n){throw s.value=n instanceof Error?n.message:"更新资料失败",n}finally{r.value=!1}},refreshSession:async()=>{try{const e=await rn.refreshSession();return t.value=e,e}catch(r){throw console.error("刷新会话失败:",r),e.value=null,t.value=null,r}},checkAuth:l,initialize:async()=>{if(!n.value)try{r.value=!0,await l(),rn.onAuthStateChange((t,r)=>{"SIGNED_IN"===t?l():"SIGNED_OUT"===t?(e.value=null,r.value=null):"TOKEN_REFRESHED"===t&&(r.value=r)}),n.value=!0}catch(t){console.error("初始化认证状态失败:",t)}finally{r.value=!1}},clearError:()=>{s.value=null},requireAuth:c,requireAdmin:()=>{if(c(),!a.value)throw new Error("需要管理员权限")}}}),nn={class:"feedback-widget"},on={key:0,class:"feedback-badge"},an={key:1,class:"feedback-panel"},ln={class:"panel-header"},cn={class:"panel-tabs"},un=["onClick"],hn={class:"panel-content"},dn={key:0,class:"submit-feedback"},fn={class:"form-group"},pn={class:"form-group"},gn={class:"form-group"},mn={class:"form-group"},vn={class:"form-group"},yn={class:"form-label"},wn={class:"form-actions"},_n=["disabled"],bn={key:1,class:"feedback-history"},kn={key:0,class:"loading-state"},En={key:1,class:"empty-state"},Sn={key:2,class:"feedback-list"},Tn={class:"feedback-header"},On={class:"feedback-date"},Pn={class:"feedback-title"},jn={class:"feedback-content"},An={key:0,class:"feedback-response"},In={class:"response-date"},Cn={key:2,class:"feedback-stats"},Rn={class:"stats-grid"},$n={class:"stat-card"},Ln={class:"stat-number"},xn={class:"stat-card"},Un={class:"stat-number"},Dn={class:"stat-card"},qn={class:"stat-number"},Nn={class:"stat-card"},Mn={class:"stat-number"},Bn={class:"stats-charts"},Vn={class:"chart-section"},Fn={class:"type-distribution"},Gn={class:"type-label"},zn={class:"type-bar"},Wn={class:"type-count"},Jn={key:2,class:"success-message"},Hn=he(e({__name:"FeedbackWidget",setup(e){const i=sn(),l=v(!1),c=v("submit"),u=v(!1),h=v(!1),d=v(!1),f=E({type:"",title:"",content:"",priority:"medium",includeSystemInfo:!0}),p=v([]),w=v({total:0,pending:0,resolved:0,response_rate:0,by_type:{}}),b=[{id:"submit",label:"提交反馈"},{id:"history",label:"反馈历史"},{id:"stats",label:"反馈统计"}],U=k(()=>p.value.filter(e=>!e.is_read).length),D=()=>{l.value=!l.value,l.value&&"history"===c.value&&N()},q=async()=>{if(i.user)try{h.value=!0;const e={...f,user_id:i.user.id,system_info:f.includeSystemInfo?M():null};console.log("提交反馈:",e),await new Promise(e=>setTimeout(e,1e3)),Object.assign(f,{type:"",title:"",content:"",priority:"medium",includeSystemInfo:!0}),d.value=!0,setTimeout(()=>{d.value=!1},3e3),l.value=!1}catch(e){console.error("提交反馈失败:",e),alert("提交反馈失败，请稍后重试")}finally{h.value=!1}else alert("请先登录后再提交反馈")},N=async()=>{if(i.user)try{u.value=!0,console.log("加载反馈历史"),p.value=[{id:"1",type:"feature",title:"希望增加暗色主题",content:"建议增加暗色主题选项，方便夜间使用",priority:"medium",status:"resolved",response:"感谢您的建议！暗色主题功能已经在最新版本中上线。",response_at:"2024-12-25T10:00:00Z",is_read:!0,created_at:"2024-12-24T15:30:00Z"}],w.value={total:5,pending:2,resolved:3,response_rate:80,by_type:{bug:2,feature:2,improvement:1}}}catch(e){console.error("加载反馈历史失败:",e)}finally{u.value=!1}},M=()=>({userAgent:navigator.userAgent,platform:navigator.platform,language:navigator.language,screenResolution:`${screen.width}x${screen.height}`,timestamp:(new Date).toISOString()}),B=e=>({bug:"Bug 报告",feature:"功能建议",improvement:"改进建议",question:"问题咨询",other:"其他"}[e]||e),V=e=>new Date(e).toLocaleString("zh-CN");return y(()=>{i.user&&N()}),(e,i)=>(m(),t("div",nn,[l.value?S("",!0):(m(),t("button",{key:0,onClick:D,class:T(["feedback-button",{"has-feedback":U.value>0}]),title:"用户反馈"},[n(o(a),{class:"icon"}),U.value>0?(m(),t("span",on,_(U.value),1)):S("",!0)],2)),l.value?(m(),t("div",an,[r("div",ln,[i[5]||(i[5]=r("h3",{class:"panel-title"},"用户反馈",-1)),r("button",{onClick:D,class:"close-button"},[n(o(O),{class:"icon"})])]),r("div",cn,[(m(),t(P,null,j(b,e=>r("button",{key:e.id,onClick:t=>c.value=e.id,class:T(["tab-button",{active:c.value===e.id}])},_(e.label),11,un)),64))]),r("div",hn,["submit"===c.value?(m(),t("div",dn,[r("form",{onSubmit:A(q,["prevent"]),class:"feedback-form"},[r("div",fn,[i[7]||(i[7]=r("label",{for:"feedback-type",class:"form-label"},"反馈类型",-1)),I(r("select",{id:"feedback-type","onUpdate:modelValue":i[0]||(i[0]=e=>f.type=e),class:"form-select",required:""},i[6]||(i[6]=[s('<option value="" data-v-5862e757>请选择反馈类型</option><option value="bug" data-v-5862e757>Bug 报告</option><option value="feature" data-v-5862e757>功能建议</option><option value="improvement" data-v-5862e757>改进建议</option><option value="question" data-v-5862e757>问题咨询</option><option value="other" data-v-5862e757>其他</option>',6)]),512),[[C,f.type]])]),r("div",pn,[i[8]||(i[8]=r("label",{for:"feedback-title",class:"form-label"},"标题",-1)),I(r("input",{id:"feedback-title","onUpdate:modelValue":i[1]||(i[1]=e=>f.title=e),type:"text",class:"form-input",placeholder:"简要描述您的反馈",required:""},null,512),[[R,f.title]])]),r("div",gn,[i[9]||(i[9]=r("label",{for:"feedback-content",class:"form-label"},"详细描述",-1)),I(r("textarea",{id:"feedback-content","onUpdate:modelValue":i[2]||(i[2]=e=>f.content=e),class:"form-textarea",rows:"4",placeholder:"请详细描述您的反馈内容",required:""},null,512),[[R,f.content]])]),r("div",mn,[i[11]||(i[11]=r("label",{for:"feedback-priority",class:"form-label"},"优先级",-1)),I(r("select",{id:"feedback-priority","onUpdate:modelValue":i[3]||(i[3]=e=>f.priority=e),class:"form-select"},i[10]||(i[10]=[r("option",{value:"low"},"低",-1),r("option",{value:"medium"},"中",-1),r("option",{value:"high"},"高",-1),r("option",{value:"urgent"},"紧急",-1)]),512),[[C,f.priority]])]),r("div",vn,[r("label",yn,[I(r("input",{"onUpdate:modelValue":i[4]||(i[4]=e=>f.includeSystemInfo=e),type:"checkbox",class:"form-checkbox"},null,512),[[$,f.includeSystemInfo]]),i[12]||(i[12]=g(" 包含系统信息（浏览器、设备等） "))])]),r("div",wn,[r("button",{type:"submit",disabled:h.value,class:"submit-button"},_(h.value?"提交中...":"提交反馈"),9,_n)])],32)])):"history"===c.value?(m(),t("div",bn,[u.value?(m(),t("div",kn,i[13]||(i[13]=[r("div",{class:"loading-spinner"},null,-1),r("p",null,"加载反馈历史...",-1)]))):0===p.value.length?(m(),t("div",En,[n(o(a),{class:"empty-icon"}),i[14]||(i[14]=r("p",null,"暂无反馈记录",-1))])):(m(),t("div",Sn,[(m(!0),t(P,null,j(p.value,e=>{return m(),t("div",{key:e.id,class:T(["feedback-item",{unread:!e.is_read}])},[r("div",Tn,[r("span",{class:T(["feedback-type",e.type])},_(B(e.type)),3),r("span",{class:T(["feedback-priority",e.priority])},_((s=e.priority,{low:"低",medium:"中",high:"高",urgent:"紧急"}[s]||s)),3),r("span",On,_(V(e.created_at)),1)]),r("h4",Pn,_(e.title),1),r("p",jn,_(e.content),1),e.response?(m(),t("div",An,[i[15]||(i[15]=r("h5",null,"开发者回复：",-1)),r("p",null,_(e.response),1),r("span",In,_(V(e.response_at)),1)])):S("",!0)],2);var s}),128))]))])):"stats"===c.value?(m(),t("div",Cn,[r("div",Rn,[r("div",$n,[r("div",Ln,_(w.value.total),1),i[16]||(i[16]=r("div",{class:"stat-label"},"总反馈数",-1))]),r("div",xn,[r("div",Un,_(w.value.pending),1),i[17]||(i[17]=r("div",{class:"stat-label"},"待处理",-1))]),r("div",Dn,[r("div",qn,_(w.value.resolved),1),i[18]||(i[18]=r("div",{class:"stat-label"},"已解决",-1))]),r("div",Nn,[r("div",Mn,_(w.value.response_rate)+"%",1),i[19]||(i[19]=r("div",{class:"stat-label"},"回复率",-1))])]),r("div",Bn,[r("div",Vn,[i[20]||(i[20]=r("h4",null,"反馈类型分布",-1)),r("div",Fn,[(m(!0),t(P,null,j(w.value.by_type,(e,s)=>(m(),t("div",{key:s,class:"type-item"},[r("span",Gn,_(B(s)),1),r("div",zn,[r("div",{class:"type-fill",style:x({width:e/w.value.total*100+"%"})},null,4)]),r("span",Wn,_(e),1)]))),128))])])])])):S("",!0)])])):S("",!0),d.value?(m(),t("div",Jn,[n(o(L),{class:"success-icon"}),i[21]||(i[21]=r("span",null,"反馈提交成功！我们会尽快处理您的反馈。",-1))])):S("",!0)]))}}),[["__scopeId","data-v-5862e757"]]),Kn={id:"app"},Qn={key:0,class:"global-error"},Yn={class:"error-content"},Xn={class:"error-message"},Zn=he(e({__name:"App",setup(e){const s=v(null),a=()=>{s.value=null};return U((e,t,r)=>(console.error("全局错误:",e,r),s.value=e instanceof Error?e.message:"发生未知错误",!1)),y(()=>{window.addEventListener("error",e=>{var t;console.error("页面加载错误:",e.error),s.value=(null==(t=e.error)?void 0:t.message)||"页面加载失败"}),window.addEventListener("offline",()=>{s.value="网络连接已断开"})}),(e,i)=>{const l=u("RouterView");return m(),t("div",Kn,[s.value?(m(),t("div",Qn,[r("div",Yn,[r("span",Xn,_(s.value),1),r("button",{class:"error-close",onClick:a},"×")])])):S("",!0),n(l,null,{default:h(({Component:e})=>[n(q,{name:"fade",mode:"out-in"},{default:h(()=>[(m(),D(N(e)))]),_:2},1024)]),_:1}),n(de),o(false)?(m(),D(ge,{key:1})):S("",!0),n(Hn)])}}}),[["__scopeId","data-v-6914bece"]]),eo="undefined"!=typeof document;function to(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const ro=Object.assign;function so(e,t){const r={};for(const s in t){const n=t[s];r[s]=oo(n)?n.map(e):e(n)}return r}const no=()=>{},oo=Array.isArray,ao=/#/g,io=/&/g,lo=/\//g,co=/=/g,uo=/\?/g,ho=/\+/g,fo=/%5B/g,po=/%5D/g,go=/%5E/g,mo=/%60/g,vo=/%7B/g,yo=/%7C/g,wo=/%7D/g,_o=/%20/g;function bo(e){return encodeURI(""+e).replace(yo,"|").replace(fo,"[").replace(po,"]")}function ko(e){return bo(e).replace(ho,"%2B").replace(_o,"+").replace(ao,"%23").replace(io,"%26").replace(mo,"`").replace(vo,"{").replace(wo,"}").replace(go,"^")}function Eo(e){return ko(e).replace(co,"%3D")}function So(e){return null==e?"":function(e){return bo(e).replace(ao,"%23").replace(uo,"%3F")}(e).replace(lo,"%2F")}function To(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Oo=/\/$/;function Po(e,t,r="/"){let s,n={},o="",a="";const i=t.indexOf("#");let l=t.indexOf("?");return i<l&&i>=0&&(l=-1),l>-1&&(s=t.slice(0,l),o=t.slice(l+1,i>-1?i:t.length),n=e(o)),i>-1&&(s=s||t.slice(0,i),a=t.slice(i,t.length)),s=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),s=e.split("/"),n=s[s.length-1];".."!==n&&"."!==n||s.push("");let o,a,i=r.length-1;for(o=0;o<s.length;o++)if(a=s[o],"."!==a){if(".."!==a)break;i>1&&i--}return r.slice(0,i).join("/")+"/"+s.slice(o).join("/")}(null!=s?s:t,r),{fullPath:s+(o&&"?")+o+a,path:s,query:n,hash:To(a)}}function jo(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Ao(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Io(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!Co(e[r],t[r]))return!1;return!0}function Co(e,t){return oo(e)?Ro(e,t):oo(t)?Ro(t,e):e===t}function Ro(e,t){return oo(t)?e.length===t.length&&e.every((e,r)=>e===t[r]):1===e.length&&e[0]===t}const $o={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Lo,xo,Uo,Do;function qo(e){if(!e)if(eo){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Oo,"")}(xo=Lo||(Lo={})).pop="pop",xo.push="push",(Do=Uo||(Uo={})).back="back",Do.forward="forward",Do.unknown="";const No=/^[^#]+#/;function Mo(e,t){return e.replace(No,"#")+t}const Bo=()=>({left:window.scrollX,top:window.scrollY});function Vo(e){let t;if("el"in e){const r=e.el,s="string"==typeof r&&r.startsWith("#"),n="string"==typeof r?s?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!n)return;t=function(e,t){const r=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-r.left-(t.left||0),top:s.top-r.top-(t.top||0)}}(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function Fo(e,t){return(history.state?history.state.position-t:-1)+e}const Go=new Map;function zo(e,t){const{pathname:r,search:s,hash:n}=t,o=e.indexOf("#");if(o>-1){let t=n.includes(e.slice(o))?e.slice(o).length:1,r=n.slice(t);return"/"!==r[0]&&(r="/"+r),jo(r,"")}return jo(r,e)+s+n}function Wo(e,t,r,s=!1,n=!1){return{back:e,current:t,forward:r,replaced:s,position:window.history.length,scroll:n?Bo():null}}function Jo(e){const{history:t,location:r}=window,s={value:zo(e,r)},n={value:t.state};function o(s,o,a){const i=e.indexOf("#"),l=i>-1?(r.host&&document.querySelector("base")?e:e.slice(i))+s:location.protocol+"//"+location.host+e+s;try{t[a?"replaceState":"pushState"](o,"",l),n.value=o}catch(c){console.error(c),r[a?"replace":"assign"](l)}}return n.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:s,state:n,push:function(e,r){const a=ro({},n.value,t.state,{forward:e,scroll:Bo()});o(a.current,a,!0),o(e,ro({},Wo(s.value,e,null),{position:a.position+1},r),!1),s.value=e},replace:function(e,r){o(e,ro({},t.state,Wo(n.value.back,e,n.value.forward,!0),r,{position:n.value.position}),!0),s.value=e}}}function Ho(e){return"string"==typeof e||"symbol"==typeof e}const Ko=Symbol("");var Qo,Yo;function Xo(e,t){return ro(new Error,{type:e,[Ko]:!0},t)}function Zo(e,t){return e instanceof Error&&Ko in e&&(null==t||!!(e.type&t))}(Yo=Qo||(Qo={}))[Yo.aborted=4]="aborted",Yo[Yo.cancelled=8]="cancelled",Yo[Yo.duplicated=16]="duplicated";const ea="[^/]+?",ta={sensitive:!1,strict:!1,start:!0,end:!0},ra=/[.+*?^${}()[\]/\\]/g;function sa(e,t){let r=0;for(;r<e.length&&r<t.length;){const s=t[r]-e[r];if(s)return s;r++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function na(e,t){let r=0;const s=e.score,n=t.score;for(;r<s.length&&r<n.length;){const e=sa(s[r],n[r]);if(e)return e;r++}if(1===Math.abs(n.length-s.length)){if(oa(s))return 1;if(oa(n))return-1}return n.length-s.length}function oa(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const aa={type:0,value:""},ia=/[a-zA-Z0-9_]/;function la(e,t,r){const s=function(e,t){const r=ro({},ta,t),s=[];let n=r.start?"^":"";const o=[];for(const l of e){const e=l.length?[]:[90];r.strict&&!l.length&&(n+="/");for(let t=0;t<l.length;t++){const s=l[t];let a=40+(r.sensitive?.25:0);if(0===s.type)t||(n+="/"),n+=s.value.replace(ra,"\\$&"),a+=40;else if(1===s.type){const{value:e,repeatable:r,optional:c,regexp:u}=s;o.push({name:e,repeatable:r,optional:c});const h=u||ea;if(h!==ea){a+=10;try{new RegExp(`(${h})`)}catch(i){throw new Error(`Invalid custom RegExp for param "${e}" (${h}): `+i.message)}}let d=r?`((?:${h})(?:/(?:${h}))*)`:`(${h})`;t||(d=c&&l.length<2?`(?:/${d})`:"/"+d),c&&(d+="?"),n+=d,a+=20,c&&(a+=-8),r&&(a+=-20),".*"===h&&(a+=-50)}e.push(a)}s.push(e)}if(r.strict&&r.end){const e=s.length-1;s[e][s[e].length-1]+=.7000000000000001}r.strict||(n+="/?"),r.end?n+="$":r.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const a=new RegExp(n,r.sensitive?"":"i");return{re:a,score:s,keys:o,parse:function(e){const t=e.match(a),r={};if(!t)return null;for(let s=1;s<t.length;s++){const e=t[s]||"",n=o[s-1];r[n.name]=e&&n.repeatable?e.split("/"):e}return r},stringify:function(t){let r="",s=!1;for(const n of e){s&&r.endsWith("/")||(r+="/"),s=!1;for(const e of n)if(0===e.type)r+=e.value;else if(1===e.type){const{value:o,repeatable:a,optional:i}=e,l=o in t?t[o]:"";if(oo(l)&&!a)throw new Error(`Provided param "${o}" is an array but it is not repeatable (* or + modifiers)`);const c=oo(l)?l.join("/"):l;if(!c){if(!i)throw new Error(`Missing required param "${o}"`);n.length<2&&(r.endsWith("/")?r=r.slice(0,-1):s=!0)}r+=c}}return r||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[aa]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${r})/"${c}": ${e}`)}let r=0,s=r;const n=[];let o;function a(){o&&n.push(o),o=[]}let i,l=0,c="",u="";function h(){c&&(0===r?o.push({type:0,value:c}):1===r||2===r||3===r?(o.length>1&&("*"===i||"+"===i)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:c,regexp:u,repeatable:"*"===i||"+"===i,optional:"*"===i||"?"===i})):t("Invalid state to consume buffer"),c="")}function d(){c+=i}for(;l<e.length;)if(i=e[l++],"\\"!==i||2===r)switch(r){case 0:"/"===i?(c&&h(),a()):":"===i?(h(),r=1):d();break;case 4:d(),r=s;break;case 1:"("===i?r=2:ia.test(i)?d():(h(),r=0,"*"!==i&&"?"!==i&&"+"!==i&&l--);break;case 2:")"===i?"\\"==u[u.length-1]?u=u.slice(0,-1)+i:r=3:u+=i;break;case 3:h(),r=0,"*"!==i&&"?"!==i&&"+"!==i&&l--,u="";break;default:t("Unknown state")}else s=r,r=4;return 2===r&&t(`Unfinished custom RegExp for param "${c}"`),h(),a(),n}(e.path),r),n=ro(s,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function ca(e,t){const r=[],s=new Map;function n(e,r,s){const i=!s,l=ha(e);l.aliasOf=s&&s.record;const c=ga(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(ha(ro({},l,{components:s?s.record.components:l.components,path:e,aliasOf:s?s.record:l})))}let h,d;for(const t of u){const{path:u}=t;if(r&&"/"!==u[0]){const e=r.record.path,s="/"===e[e.length-1]?"":"/";t.path=r.record.path+(u&&s+u)}if(h=la(t,r,c),s?s.alias.push(h):(d=d||h,d!==h&&d.alias.push(h),i&&e.name&&!fa(h)&&o(e.name)),ma(h)&&a(h),l.children){const e=l.children;for(let t=0;t<e.length;t++)n(e[t],h,s&&s.children[t])}s=s||h}return d?()=>{o(d)}:no}function o(e){if(Ho(e)){const t=s.get(e);t&&(s.delete(e),r.splice(r.indexOf(t),1),t.children.forEach(o),t.alias.forEach(o))}else{const t=r.indexOf(e);t>-1&&(r.splice(t,1),e.record.name&&s.delete(e.record.name),e.children.forEach(o),e.alias.forEach(o))}}function a(e){const t=function(e,t){let r=0,s=t.length;for(;r!==s;){const n=r+s>>1;na(e,t[n])<0?s=n:r=n+1}const n=function(e){let t=e;for(;t=t.parent;)if(ma(t)&&0===na(e,t))return t;return}(e);n&&(s=t.lastIndexOf(n,s-1));return s}(e,r);r.splice(t,0,e),e.record.name&&!fa(e)&&s.set(e.record.name,e)}return t=ga({strict:!1,end:!0,sensitive:!1},t),e.forEach(e=>n(e)),{addRoute:n,resolve:function(e,t){let n,o,a,i={};if("name"in e&&e.name){if(n=s.get(e.name),!n)throw Xo(1,{location:e});a=n.record.name,i=ro(ua(t.params,n.keys.filter(e=>!e.optional).concat(n.parent?n.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&ua(e.params,n.keys.map(e=>e.name))),o=n.stringify(i)}else if(null!=e.path)o=e.path,n=r.find(e=>e.re.test(o)),n&&(i=n.parse(o),a=n.record.name);else{if(n=t.name?s.get(t.name):r.find(e=>e.re.test(t.path)),!n)throw Xo(1,{location:e,currentLocation:t});a=n.record.name,i=ro({},t.params,e.params),o=n.stringify(i)}const l=[];let c=n;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:o,params:i,matched:l,meta:pa(l)}},removeRoute:o,clearRoutes:function(){r.length=0,s.clear()},getRoutes:function(){return r},getRecordMatcher:function(e){return s.get(e)}}}function ua(e,t){const r={};for(const s of t)s in e&&(r[s]=e[s]);return r}function ha(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:da(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function da(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const s in e.components)t[s]="object"==typeof r?r[s]:r;return t}function fa(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function pa(e){return e.reduce((e,t)=>ro(e,t.meta),{})}function ga(e,t){const r={};for(const s in e)r[s]=s in t?t[s]:e[s];return r}function ma({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function va(e){const t={};if(""===e||"?"===e)return t;const r=("?"===e[0]?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const e=r[s].replace(ho," "),n=e.indexOf("="),o=To(n<0?e:e.slice(0,n)),a=n<0?null:To(e.slice(n+1));if(o in t){let e=t[o];oo(e)||(e=t[o]=[e]),e.push(a)}else t[o]=a}return t}function ya(e){let t="";for(let r in e){const s=e[r];if(r=Eo(r),null==s){void 0!==s&&(t+=(t.length?"&":"")+r);continue}(oo(s)?s.map(e=>e&&ko(e)):[s&&ko(s)]).forEach(e=>{void 0!==e&&(t+=(t.length?"&":"")+r,null!=e&&(t+="="+e))})}return t}function wa(e){const t={};for(const r in e){const s=e[r];void 0!==s&&(t[r]=oo(s)?s.map(e=>null==e?null:""+e):null==s?s:""+s)}return t}const _a=Symbol(""),ba=Symbol(""),ka=Symbol(""),Ea=Symbol(""),Sa=Symbol("");function Ta(){let e=[];return{add:function(t){return e.push(t),()=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Oa(e,t,r,s,n,o=e=>e()){const a=s&&(s.enterCallbacks[n]=s.enterCallbacks[n]||[]);return()=>new Promise((i,l)=>{const c=e=>{var o;!1===e?l(Xo(4,{from:r,to:t})):e instanceof Error?l(e):"string"==typeof(o=e)||o&&"object"==typeof o?l(Xo(2,{from:t,to:e})):(a&&s.enterCallbacks[n]===a&&"function"==typeof e&&a.push(e),i())},u=o(()=>e.call(s&&s.instances[n],t,r,c));let h=Promise.resolve(u);e.length<3&&(h=h.then(c)),h.catch(e=>l(e))})}function Pa(e,t,r,s,n=e=>e()){const o=[];for(const a of e)for(const e in a.components){let i=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if(to(i)){const l=(i.__vccOpts||i)[t];l&&o.push(Oa(l,r,s,a,e,n))}else{let l=i();o.push(()=>l.then(o=>{if(!o)throw new Error(`Couldn't resolve component "${e}" at "${a.path}"`);const i=(l=o).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&to(l.default)?o.default:o;var l;a.mods[e]=o,a.components[e]=i;const c=(i.__vccOpts||i)[t];return c&&Oa(c,r,s,a,e,n)()}))}}return o}function ja(e){const t=W(ka),r=W(Ea),s=k(()=>{const r=o(e.to);return t.resolve(r)}),n=k(()=>{const{matched:e}=s.value,{length:t}=e,n=e[t-1],o=r.matched;if(!n||!o.length)return-1;const a=o.findIndex(Ao.bind(null,n));if(a>-1)return a;const i=Ca(e[t-2]);return t>1&&Ca(n)===i&&o[o.length-1].path!==i?o.findIndex(Ao.bind(null,e[t-2])):a}),a=k(()=>n.value>-1&&function(e,t){for(const r in t){const s=t[r],n=e[r];if("string"==typeof s){if(s!==n)return!1}else if(!oo(n)||n.length!==s.length||s.some((e,t)=>e!==n[t]))return!1}return!0}(r.params,s.value.params)),i=k(()=>n.value>-1&&n.value===r.matched.length-1&&Io(r.params,s.value.params));return{route:s,href:k(()=>s.value.href),isActive:a,isExactActive:i,navigate:function(r={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(r)){const r=t[o(e.replace)?"replace":"push"](o(e.to)).catch(no);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition(()=>r),r}return Promise.resolve()}}}const Aa=e({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:ja,setup(e,{slots:t}){const r=E(ja(e)),{options:s}=W(ka),n=k(()=>({[Ra(e.activeClass,s.linkActiveClass,"router-link-active")]:r.isActive,[Ra(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const s=t.default&&(1===(o=t.default(r)).length?o[0]:o);var o;return e.custom?s:G("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:n.value},s)}}}),Ia=Aa;function Ca(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ra=(e,t,r)=>null!=e?e:null!=t?t:r;function $a(e,t){if(!e)return null;const r=e(t);return 1===r.length?r[0]:r}const La=e({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const s=W(Sa),n=k(()=>e.route||s.value),a=W(ba,0),i=k(()=>{let e=o(a);const{matched:t}=n.value;let r;for(;(r=t[e])&&!r.components;)e++;return e}),l=k(()=>n.value.matched[i.value]);V(ba,k(()=>i.value+1)),V(_a,l),V(Sa,n);const c=v();return F(()=>[c.value,l.value,e.name],([e,t,r],[s,n,o])=>{t&&(t.instances[r]=e,n&&n!==t&&e&&e===s&&(t.leaveGuards.size||(t.leaveGuards=n.leaveGuards),t.updateGuards.size||(t.updateGuards=n.updateGuards))),!e||!t||n&&Ao(t,n)&&s||(t.enterCallbacks[r]||[]).forEach(t=>t(e))},{flush:"post"}),()=>{const s=n.value,o=e.name,a=l.value,i=a&&a.components[o];if(!i)return $a(r.default,{Component:i,route:s});const u=a.props[o],h=u?!0===u?s.params:"function"==typeof u?u(s):u:null,d=G(i,ro({},h,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[o]=null)},ref:c}));return $a(r.default,{Component:d,route:s})||d}}});function xa(){return W(ka)}function Ua(e){return W(Ea)}const Da=[{path:"/",name:"Home",component:()=>ve(()=>import("./EnhancedHomeView-BUwsZrKp.js"),__vite__mapDeps([2,1,3,4]),import.meta.url),meta:{title:"工具导航站",description:"高效的工具导航和产品展示平台"}},{path:"/legacy",name:"LegacyHome",component:()=>ve(()=>import("./HomeView-BBHWoDcI.js"),__vite__mapDeps([5,1,6]),import.meta.url),meta:{title:"传统版工具导航站",description:"传统版本的工具导航和产品展示平台"}},{path:"/comparison",name:"Comparison",component:()=>ve(()=>import("./ComparisonView-Botp5qEA.js"),__vite__mapDeps([7,1,8]),import.meta.url),meta:{title:"代码优化对比",description:"展示原HTML代码与Vue组件化改造后的效果对比"}},{path:"/test-statusbar",name:"TestStatusBar",component:()=>ve(()=>import("./TestStatusBar-vfffXSNd.js"),__vite__mapDeps([9,1,10]),import.meta.url),meta:{title:"状态栏测试",description:"测试底部状态栏组件功能"}},{path:"/tools",name:"Tools",component:()=>ve(()=>import("./ToolsView-D5Tb4Zb4.js"),__vite__mapDeps([11,1,12]),import.meta.url),meta:{title:"工具导航",description:"发现和管理您的常用工具"}},{path:"/products",name:"Products",component:()=>ve(()=>import("./ProductsView-cbqokVhT.js"),__vite__mapDeps([13,1,14]),import.meta.url),meta:{title:"产品展示",description:"浏览和购买优质产品"}},{path:"/product/:id",name:"ProductDetail",component:()=>ve(()=>import("./ProductDetailView-DBRwGTHH.js"),__vite__mapDeps([15,1,16]),import.meta.url),meta:{title:"产品详情",description:"查看产品详细信息"}},{path:"/user",name:"User",component:()=>ve(()=>import("./UserView-Y3OJXVxS.js"),__vite__mapDeps([17,1,18]),import.meta.url),meta:{title:"个人中心",description:"管理您的账户和偏好设置",requiresAuth:!0},children:[{path:"profile",name:"UserProfile",component:()=>ve(()=>import("./ProfileView-BsCeIPVZ.js"),__vite__mapDeps([19,1,20]),import.meta.url),meta:{title:"个人资料",requiresAuth:!0}},{path:"favorites",name:"UserFavorites",component:()=>ve(()=>import("./FavoritesView-BHom3aRX.js"),__vite__mapDeps([21,1,22]),import.meta.url),meta:{title:"我的收藏",requiresAuth:!0}},{path:"orders",name:"UserOrders",component:()=>ve(()=>import("./OrdersView-DxUfmtIo.js"),__vite__mapDeps([23,1,24]),import.meta.url),meta:{title:"我的订单",requiresAuth:!0}}]},{path:"/auth",name:"Auth",component:()=>ve(()=>import("./AuthView-C5uDIFBe.js"),__vite__mapDeps([25,1,26]),import.meta.url),meta:{title:"登录注册",description:"登录或注册您的账户"},children:[{path:"login",name:"Login",component:()=>ve(()=>import("./LoginView-CDpJmh0U.js"),__vite__mapDeps([27,1,28]),import.meta.url),meta:{title:"登录"}},{path:"register",name:"Register",component:()=>ve(()=>import("./RegisterView-DUzN9dY-.js"),__vite__mapDeps([29,1,30]),import.meta.url),meta:{title:"注册"}},{path:"forgot-password",name:"ForgotPassword",component:()=>ve(()=>import("./ForgotPasswordView-GqlmKlR3.js"),__vite__mapDeps([31,1,32]),import.meta.url),meta:{title:"忘记密码"}}]},{path:"/admin",name:"Admin",component:()=>ve(()=>import("./AdminView-B2LVh3S-.js"),__vite__mapDeps([33,1,34]),import.meta.url),meta:{title:"管理后台",description:"系统管理和数据统计",requiresAuth:!0,requiresAdmin:!0},children:[{path:"dashboard",name:"AdminDashboard",component:()=>ve(()=>import("./DashboardView-XliPLf3G.js"),__vite__mapDeps([35,1,36]),import.meta.url),meta:{title:"仪表盘",requiresAuth:!0,requiresAdmin:!0}},{path:"tools",name:"AdminTools",component:()=>ve(()=>import("./AdminToolsView-29aLt44O.js"),__vite__mapDeps([37,3,1,38]),import.meta.url),meta:{title:"工具管理",requiresAuth:!0,requiresAdmin:!0}},{path:"products",name:"AdminProducts",component:()=>ve(()=>import("./ProductsManageView-CFrNTjAK.js"),__vite__mapDeps([39,1,40]),import.meta.url),meta:{title:"产品管理",requiresAuth:!0,requiresAdmin:!0}},{path:"local",name:"AdminLocal",component:()=>ve(()=>import("./LocalManagementView-7tcF9oGw.js"),__vite__mapDeps([41,1,42]),import.meta.url),meta:{title:"本地管理",requiresAuth:!0,requiresAdmin:!0}},{path:"local-test",name:"AdminLocalTest",component:()=>ve(()=>import("./LocalManagementTestView-PqG-fA7f.js"),__vite__mapDeps([43,1,44]),import.meta.url),meta:{title:"本地管理测试",requiresAuth:!0,requiresAdmin:!0}}]},{path:"/payment",name:"Payment",component:()=>ve(()=>import("./PaymentView-CvAFmR1Y.js"),__vite__mapDeps([45,1,46]),import.meta.url),meta:{title:"支付页面",description:"安全的支付处理",requiresAuth:!0}},{path:"/payment/success",name:"PaymentSuccess",component:()=>ve(()=>import("./PaymentSuccessView-BAAK5jOU.js"),__vite__mapDeps([47,1,48]),import.meta.url),meta:{title:"支付成功",description:"支付完成确认"}},{path:"/payment/cancel",name:"PaymentCancel",component:()=>ve(()=>import("./PaymentCancelView-CFlX4BUb.js"),__vite__mapDeps([49,1,50]),import.meta.url),meta:{title:"支付取消",description:"支付已取消"}},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>ve(()=>import("./NotFoundView-CNLIsKc_.js"),__vite__mapDeps([51,1,52]),import.meta.url),meta:{title:"页面未找到",description:"您访问的页面不存在"}}],qa=function(e){const t=ca(e.routes,e),r=e.parseQuery||va,s=e.stringifyQuery||ya,n=e.history,a=Ta(),i=Ta(),l=Ta(),c=M($o);let u=$o;eo&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const h=so.bind(null,e=>""+e),d=so.bind(null,So),f=so.bind(null,To);function p(e,o){if(o=ro({},o||c.value),"string"==typeof e){const s=Po(r,e,o.path),a=t.resolve({path:s.path},o),i=n.createHref(s.fullPath);return ro(s,a,{params:f(a.params),hash:To(s.hash),redirectedFrom:void 0,href:i})}let a;if(null!=e.path)a=ro({},e,{path:Po(r,e.path,o.path).path});else{const t=ro({},e.params);for(const e in t)null==t[e]&&delete t[e];a=ro({},e,{params:d(t)}),o.params=d(o.params)}const i=t.resolve(a,o),l=e.hash||"";i.params=h(f(i.params));const u=function(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}(s,ro({},e,{hash:(p=l,bo(p).replace(vo,"{").replace(wo,"}").replace(go,"^")),path:i.path}));var p;const g=n.createHref(u);return ro({fullPath:u,hash:l,query:s===ya?wa(e.query):e.query||{}},i,{redirectedFrom:void 0,href:g})}function g(e){return"string"==typeof e?Po(r,e,c.value.path):ro({},e)}function m(e,t){if(u!==e)return Xo(8,{from:t,to:e})}function v(e){return w(e)}function y(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:r}=t;let s="function"==typeof r?r(e):r;return"string"==typeof s&&(s=s.includes("?")||s.includes("#")?s=g(s):{path:s},s.params={}),ro({query:e.query,hash:e.hash,params:null!=s.path?{}:e.params},s)}}function w(e,t){const r=u=p(e),n=c.value,o=e.state,a=e.force,i=!0===e.replace,l=y(r);if(l)return w(ro(g(l),{state:"object"==typeof l?ro({},o,l.state):o,force:a,replace:i}),t||r);const h=r;let d;return h.redirectedFrom=t,!a&&function(e,t,r){const s=t.matched.length-1,n=r.matched.length-1;return s>-1&&s===n&&Ao(t.matched[s],r.matched[n])&&Io(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}(s,n,r)&&(d=Xo(16,{to:h,from:n}),R(n,n,!0,!1)),(d?Promise.resolve(d):k(h,n)).catch(e=>Zo(e)?Zo(e,2)?e:C(e):I(e,h,n)).then(e=>{if(e){if(Zo(e,2))return w(ro({replace:i},g(e.to),{state:"object"==typeof e.to?ro({},o,e.to.state):o,force:a}),t||h)}else e=S(h,n,!0,i,o);return E(h,n,e),e})}function _(e,t){const r=m(e,t);return r?Promise.reject(r):Promise.resolve()}function b(e){const t=x.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function k(e,t){let r;const[s,n,o]=function(e,t){const r=[],s=[],n=[],o=Math.max(t.matched.length,e.matched.length);for(let a=0;a<o;a++){const o=t.matched[a];o&&(e.matched.find(e=>Ao(e,o))?s.push(o):r.push(o));const i=e.matched[a];i&&(t.matched.find(e=>Ao(e,i))||n.push(i))}return[r,s,n]}(e,t);r=Pa(s.reverse(),"beforeRouteLeave",e,t);for(const a of s)a.leaveGuards.forEach(s=>{r.push(Oa(s,e,t))});const l=_.bind(null,e,t);return r.push(l),D(r).then(()=>{r=[];for(const s of a.list())r.push(Oa(s,e,t));return r.push(l),D(r)}).then(()=>{r=Pa(n,"beforeRouteUpdate",e,t);for(const s of n)s.updateGuards.forEach(s=>{r.push(Oa(s,e,t))});return r.push(l),D(r)}).then(()=>{r=[];for(const s of o)if(s.beforeEnter)if(oo(s.beforeEnter))for(const n of s.beforeEnter)r.push(Oa(n,e,t));else r.push(Oa(s.beforeEnter,e,t));return r.push(l),D(r)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),r=Pa(o,"beforeRouteEnter",e,t,b),r.push(l),D(r))).then(()=>{r=[];for(const s of i.list())r.push(Oa(s,e,t));return r.push(l),D(r)}).catch(e=>Zo(e,8)?e:Promise.reject(e))}function E(e,t,r){l.list().forEach(s=>b(()=>s(e,t,r)))}function S(e,t,r,s,o){const a=m(e,t);if(a)return a;const i=t===$o,l=eo?history.state:{};r&&(s||i?n.replace(e.fullPath,ro({scroll:i&&l&&l.scroll},o)):n.push(e.fullPath,o)),c.value=e,R(e,t,r,i),C()}let T;function O(){T||(T=n.listen((e,t,r)=>{if(!U.listening)return;const s=p(e),o=y(s);if(o)return void w(ro(o,{replace:!0,force:!0}),s).catch(no);u=s;const a=c.value;var i,l;eo&&(i=Fo(a.fullPath,r.delta),l=Bo(),Go.set(i,l)),k(s,a).catch(e=>Zo(e,12)?e:Zo(e,2)?(w(ro(g(e.to),{force:!0}),s).then(e=>{Zo(e,20)&&!r.delta&&r.type===Lo.pop&&n.go(-1,!1)}).catch(no),Promise.reject()):(r.delta&&n.go(-r.delta,!1),I(e,s,a))).then(e=>{(e=e||S(s,a,!1))&&(r.delta&&!Zo(e,8)?n.go(-r.delta,!1):r.type===Lo.pop&&Zo(e,20)&&n.go(-1,!1)),E(s,a,e)}).catch(no)}))}let P,j=Ta(),A=Ta();function I(e,t,r){C(e);const s=A.list();return s.length?s.forEach(s=>s(e,t,r)):console.error(e),Promise.reject(e)}function C(e){return P||(P=!e,O(),j.list().forEach(([t,r])=>e?r(e):t()),j.reset()),e}function R(t,r,s,n){const{scrollBehavior:o}=e;if(!eo||!o)return Promise.resolve();const a=!s&&function(e){const t=Go.get(e);return Go.delete(e),t}(Fo(t.fullPath,0))||(n||!s)&&history.state&&history.state.scroll||null;return z().then(()=>o(t,r,a)).then(e=>e&&Vo(e)).catch(e=>I(e,t,r))}const $=e=>n.go(e);let L;const x=new Set,U={currentRoute:c,listening:!0,addRoute:function(e,r){let s,n;return Ho(e)?(s=t.getRecordMatcher(e),n=r):n=e,t.addRoute(n,s)},removeRoute:function(e){const r=t.getRecordMatcher(e);r&&t.removeRoute(r)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map(e=>e.record)},resolve:p,options:e,push:v,replace:function(e){return v(ro(g(e),{replace:!0}))},go:$,back:()=>$(-1),forward:()=>$(1),beforeEach:a.add,beforeResolve:i.add,afterEach:l.add,onError:A.add,isReady:function(){return P&&c.value!==$o?Promise.resolve():new Promise((e,t)=>{j.add([e,t])})},install(e){e.component("RouterLink",Ia),e.component("RouterView",La),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>o(c)}),eo&&!L&&c.value===$o&&(L=!0,v(n.location).catch(e=>{}));const t={};for(const s in $o)Object.defineProperty(t,s,{get:()=>c.value[s],enumerable:!0});e.provide(ka,this),e.provide(Ea,B(t)),e.provide(Sa,c);const r=e.unmount;x.add(e),e.unmount=function(){x.delete(e),x.size<1&&(u=$o,T&&T(),T=null,c.value=$o,L=!1,P=!1),r()}}};function D(e){return e.reduce((e,t)=>e.then(()=>b(t)),Promise.resolve())}return U}({history:function(e){const t=Jo(e=qo(e)),r=function(e,t,r,s){let n=[],o=[],a=null;const i=({state:o})=>{const i=zo(e,location),l=r.value,c=t.value;let u=0;if(o){if(r.value=i,t.value=o,a&&a===l)return void(a=null);u=c?o.position-c.position:0}else s(i);n.forEach(e=>{e(r.value,l,{delta:u,type:Lo.pop,direction:u?u>0?Uo.forward:Uo.back:Uo.unknown})})};function l(){const{history:e}=window;e.state&&e.replaceState(ro({},e.state,{scroll:Bo()}),"")}return window.addEventListener("popstate",i),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){a=r.value},listen:function(e){n.push(e);const t=()=>{const t=n.indexOf(e);t>-1&&n.splice(t,1)};return o.push(t),t},destroy:function(){for(const e of o)e();o=[],window.removeEventListener("popstate",i),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace),s=ro({location:"",base:e,go:function(e,t=!0){t||r.pauseListeners(),history.go(e)},createHref:Mo.bind(null,e)},t,r);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}(),routes:Da});function Na(e){var t;return(null==(t=e.category)?void 0:t.id)?e.category.id:e.category_id||e.category_id?e.category_id:null}class Ma{static async getTools(e){try{let t=Xs.from(Zs.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status","active");(null==e?void 0:e.query)&&(t=t.or(`name.ilike.%${e.query}%,description.ilike.%${e.query}%`)),(null==e?void 0:e.category)&&"all"!==e.category&&(t=t.eq("category_id",e.category));const r=(null==e?void 0:e.sortBy)||"sort_order",s=(null==e?void 0:e.sort_order)||"asc";t=t.order(r,{ascending:"asc"===s});const n=(null==e?void 0:e.page)||1,o=(null==e?void 0:e.limit)||20,a=(n-1)*o;t=t.range(a,a+o-1);const{data:i,error:l,count:c}=await t;if(l)throw new Error(en(l));return{items:(i||[]).map(this.transformToolRow),total:c||0,page:n,limit:o,hasMore:(c||0)>a+o}}catch(t){throw console.error("Error fetching tools:",t),t}}static async getTool(e){try{const{data:t,error:r}=await Xs.from(Zs.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("id",e).single();if(r)throw new Error(en(r));return this.transformToolRow(t)}catch(t){throw console.error("Error fetching tool:",t),t}}static async createTool(e){try{!function(e,t,r="Entity"){const s=[];for(const n of t)e[n]||s.push(n);if(s.length>0)throw new Error(`${r} validation failed: missing required fields: ${s.join(", ")}`)}(e,["name","description","url"],"Tool");const t=function(e,t="Category"){const r=Na(e);if(!r)throw new Error(`${t} is required`);return r}(e),{data:r,error:s}=await Xs.from(Zs.TOOLS).insert({name:e.name,description:e.description,url:e.url,category_id:t,icon:e.icon,is_featured:e.is_featured||!1,status:"active",meta_title:e.metaTitle,meta_description:e.metaDescription,sort_order:e.sort_order||0}).select("\n          *,\n          category:categories(*)\n        ").single();if(s)throw new Error(en(s));return this.transformToolRow(r)}catch(t){throw console.error("Error creating tool:",t),t}}static async updateTool(e,t){try{const r={};t.name&&(r.name=t.name),t.description&&(r.description=t.description),t.url&&(r.url=t.url);const s=Na(t);s&&(r.category_id=s),void 0!==t.icon&&(r.icon=t.icon),void 0!==t.is_featured&&(r.is_featured=t.is_featured),t.status&&(r.status=t.status),void 0!==t.metaTitle&&(r.meta_title=t.metaTitle),void 0!==t.metaDescription&&(r.meta_description=t.metaDescription),void 0!==t.sort_order&&(r.sort_order=t.sort_order),r.updated_at=(new Date).toISOString();const{data:n,error:o}=await Xs.from(Zs.TOOLS).update(r).eq("id",e).select("\n          *,\n          category:categories(*)\n        ").single();if(o)throw new Error(en(o));return this.transformToolRow(n)}catch(r){throw console.error("Error updating tool:",r),r}}static async deleteTool(e){try{const{error:t}=await Xs.from(Zs.TOOLS).delete().eq("id",e);if(t)throw new Error(en(t))}catch(t){throw console.error("Error deleting tool:",t),t}}static async incrementClickCount(e){try{const{data:t,error:r}=await Xs.from(Zs.TOOLS).select("click_count").eq("id",e).single();if(r)throw new Error(en(r));const{error:s}=await Xs.from(Zs.TOOLS).update({click_count:((null==t?void 0:t.click_count)||0)+1,updated_at:(new Date).toISOString()}).eq("id",e);if(s)throw new Error(en(s))}catch(t){throw console.error("Error incrementing click count:",t),t}}static async getPopularTools(e=10){try{const{data:t,error:r}=await Xs.from(Zs.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status","active").order("click_count",{ascending:!1}).limit(e);if(r)throw new Error(en(r));return(t||[]).map(this.transformToolRow)}catch(t){throw console.error("Error fetching popular tools:",t),t}}static async getFeaturedTools(e=6){try{const{data:t,error:r}=await Xs.from(Zs.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status","active").eq("is_featured",!0).order("sort_order",{ascending:!0}).limit(e);if(r)throw new Error(en(r));return(t||[]).map(this.transformToolRow)}catch(t){throw console.error("Error fetching featured tools:",t),t}}static async searchTools(e,t=20){try{const{data:r,error:s}=await Xs.from(Zs.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status","active").or(`name.ilike.%${e}%,description.ilike.%${e}%`).order("click_count",{ascending:!1}).limit(t);if(s)throw new Error(en(s));return(r||[]).map(this.transformToolRow)}catch(r){throw console.error("Error searching tools:",r),r}}static transformToolRow(e){return{id:e.id||void 0,name:e.name||void 0,description:e.description||void 0,url:e.url||void 0,icon:e.icon||void 0,category_id:e.category_id||void 0,tags:[],is_favorite:!1,click_count:e.click_count||void 0,is_featured:e.is_featured||void 0,status:e.status||void 0,created_at:e.created_at||void 0,updated_at:e.updated_at||void 0,created_by:e.created_by||void 0,meta_title:e.meta_title||void 0,meta_description:e.meta_description||void 0,sort_order:e.sort_order||void 0}}}class Ba{static async getCategories(){try{const{data:e,error:t}=await Xs.from(Zs.CATEGORIES).select("*").eq("is_active",!0).order("sort_order",{ascending:!0});if(t)throw new Error(en(t));const r=(e||[]).map(this.transformCategoryRow);return this.buildCategoryTree(r)}catch(e){throw console.error("Error fetching categories:",e),e}}static async getCategory(e){try{const{data:t,error:r}=await Xs.from(Zs.CATEGORIES).select("*").eq("id",e).single();if(r)throw new Error(en(r));return this.transformCategoryRow(t)}catch(t){throw console.error("Error fetching category:",t),t}}static async createCategory(e){try{const{data:t,error:r}=await Xs.from(Zs.CATEGORIES).insert({name:e.name,description:e.description,icon:e.icon,color:e.color,parent_id:e.parent_id,sort_order:e.sort_order||0,is_active:!0}).select().single();if(r)throw new Error(en(r));return this.transformCategoryRow(t)}catch(t){throw console.error("Error creating category:",t),t}}static async updateCategory(e,t){try{const r={};t.name&&(r.name=t.name),void 0!==t.description&&(r.description=t.description),t.icon&&(r.icon=t.icon),t.color&&(r.color=t.color),void 0!==t.parent_id&&(r.parent_id=t.parent_id),void 0!==t.sort_order&&(r.sort_order=t.sort_order),void 0!==t.is_active&&(r.is_active=t.is_active),r.updated_at=(new Date).toISOString();const{data:s,error:n}=await Xs.from(Zs.CATEGORIES).update(r).eq("id",e).select().single();if(n)throw new Error(en(n));return this.transformCategoryRow(s)}catch(r){throw console.error("Error updating category:",r),r}}static async deleteCategory(e){try{const{data:t,error:r}=await Xs.from(Zs.CATEGORIES).select("id").eq("parent_id",e);if(r)throw new Error(en(r));if(t&&t.length>0)throw new Error("无法删除包含子分类的分类，请先删除或移动子分类");const{data:s,error:n}=await Xs.from(Zs.TOOLS).select("id").eq("category_id",e);if(n)throw new Error(en(n));if(s&&s.length>0)throw new Error("无法删除包含工具的分类，请先删除或移动工具");const{error:o}=await Xs.from(Zs.CATEGORIES).delete().eq("id",e);if(o)throw new Error(en(o))}catch(t){throw console.error("Error deleting category:",t),t}}static async getCategoryStats(){try{const{data:e,error:t}=await Xs.from(Zs.TOOLS).select("category_id").eq("status","active");if(t)throw new Error(en(t));const r=new Map;return null==e||e.forEach(e=>{const t=r.get(e.category_id)||0;r.set(e.category_id,t+1)}),Array.from(r.entries()).map(([e,t])=>({categoryId:e,count:t}))}catch(e){throw console.error("Error fetching category stats:",e),e}}static async getCategoriesWithStats(){try{const[e,t]=await Promise.all([this.getCategories(),this.getCategoryStats()]),r=new Map(t.map(e=>[e.category_id,e.count])),s=e=>{var t;const n=r.get(e.id)||0,o=(null==(t=e.children)?void 0:t.map(s))||[],a=o.reduce((e,t)=>e+t.count,0);return{...e,count:n+a,children:o}};return e.map(s)}catch(e){throw console.error("Error fetching categories with stats:",e),e}}static buildCategoryTree(e){const t=new Map,r=[];return e.forEach(e=>{t.set(e.id,{...e,children:[]})}),e.forEach(e=>{const s=t.get(e.id);if(e.parent_id){const n=t.get(e.parent_id);n?(n.children=n.children||[],n.children.push(s)):r.push(s)}else r.push(s)}),r}static transformCategoryRow(e){return{id:e.id||void 0,name:e.name||void 0,description:e.description||void 0,icon:e.icon||void 0,color:e.color||void 0,parent_id:e.parent_id||void 0,count:0,sort_order:e.sort_order||void 0,is_active:e.is_active||void 0,created_at:e.created_at||void 0,updated_at:e.updated_at||void 0}}}const Va=b("tools",()=>{const e=v(""),t=v("all"),r=v(!1),s=v(!1),n=v(!1),o=v(null),a=v(!1),i=v([{id:"1",name:"Visual Studio Code",description:"微软开发的免费代码编辑器，支持多种编程语言和丰富的插件生态",url:"https://code.visualstudio.com/",icon:"📝",category_id:"1",tags:["编辑器","开发","免费","微软"],is_featured:!0,click_count:1250,is_favorite:!1,status:"active",sort_order:1,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),meta_title:"Visual Studio Code - 强大的代码编辑器",meta_description:"微软出品的轻量级但功能强大的代码编辑器，支持多种编程语言和丰富的插件生态"},{id:"2",name:"GitHub",description:"全球最大的代码托管平台，支持Git版本控制和协作开发",url:"https://github.com/",icon:"🐙",category_id:"1",tags:["代码托管","Git","协作","开源"],is_featured:!0,click_count:2100,is_favorite:!1,status:"active",sort_order:2,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),meta_title:"GitHub - 代码托管平台",meta_description:"全球最大的代码托管平台，支持Git版本控制和开源协作"}]),l=v([{id:"1",name:"开发工具",description:"编程开发相关工具",icon:"💻",color:"#3498db",count:5,sort_order:1,is_active:!0,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},{id:"2",name:"设计工具",description:"UI/UX设计工具",icon:"🎨",color:"#e74c3c",count:3,sort_order:2,is_active:!0,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}]),c=k(()=>{let s=i.value;if(e.value){const t=e.value.toLowerCase();s=s.filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.tags.some(e=>e.toLowerCase().includes(t)))}return"all"!==t.value&&(s=s.filter(e=>e.category_id===t.value)),r.value&&(s=s.filter(e=>e.is_favorite)),s}),u=k(()=>i.value.filter(e=>e.is_favorite)),h=k(()=>[...i.value].sort((e,t)=>t.click_count-e.click_count).slice(0,5)),d=k(()=>i.value.filter(e=>e.is_featured)),f=async()=>{try{const e=await Ma.getTools();i.value=e.items?e.items:e}catch(e){throw new Error("加载工具失败")}},p=async()=>{try{const e=await Ba.getCategories();l.value=e}catch(e){throw new Error("加载分类失败")}};return{searchQuery:e,selectedCategory:t,showFavoritesOnly:r,sidebarCollapsed:s,loading:n,error:o,initialized:a,tools:i,categories:l,filteredTools:c,favoriteTools:u,popularTools:h,featuredTools:d,initialize:async()=>{if(!a.value)try{n.value=!0,o.value=null,await Promise.all([f(),p()]),a.value=!0}catch(e){o.value=e instanceof Error?e.message:"初始化失败",console.error("Error initializing state:",e)}finally{n.value=!1}},toggleFavorite:async e=>{const t=i.value.find(t=>t.id===e);t&&(t.is_favorite=!t.is_favorite)},incrementClickCount:async e=>{const t=i.value.find(t=>t.id===e);t&&t.click_count++},clearError:()=>{o.value=null},setSearchQuery:t=>e.value=t,setSelectedCategory:e=>t.value=e,setSidebarCollapsed:e=>s.value=e,toggleSidebar:()=>s.value=!s.value}});const Fa={TOOLS:"local_tools",CATEGORIES:"local_categories",USER_PREFERENCES:"user_preferences",THEME_CONFIG:"theme_config",OFFLINE_QUEUE:"offline_queue",LAST_SYNC:"last_sync_time",APP_CONFIG:"app_config"};class Ga{static getLocalTools(){try{const e=localStorage.getItem(Fa.TOOLS);return e?JSON.parse(e):[]}catch(e){return console.error("获取本地工具数据失败:",e),[]}}static saveLocalTools(e){try{localStorage.setItem(Fa.TOOLS,JSON.stringify(e))}catch(t){console.error("保存本地工具数据失败:",t)}}static addLocalTool(e){const t={...e,localId:`local_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,isLocal:!0,lastModified:(new Date).toISOString(),syncStatus:"pending"},r=this.getLocalTools();return r.push(t),this.saveLocalTools(r),this.addOfflineAction({type:"create",entity:"tool",data:t}),t}static updateLocalTool(e,t){try{const r=this.getLocalTools(),s=r.findIndex(t=>t.id===e||t.localId===e);return-1!==s&&(r[s]={...r[s],...t,lastModified:(new Date).toISOString(),syncStatus:"pending"},this.saveLocalTools(r),this.addOfflineAction({type:"update",entity:"tool",data:{id:e,updates:t}}),!0)}catch(r){return console.error("更新本地工具失败:",r),!1}}static deleteLocalTool(e){try{const t=this.getLocalTools(),r=t.findIndex(t=>t.id===e||t.localId===e);if(-1===r)return!1;const s=t[r];return t.splice(r,1),this.saveLocalTools(t),this.addOfflineAction({type:"delete",entity:"tool",data:{id:s.id||s.localId}}),!0}catch(t){return console.error("删除本地工具失败:",t),!1}}static getLocalCategories(){try{const e=localStorage.getItem(Fa.CATEGORIES);return e?JSON.parse(e):[]}catch(e){return console.error("获取本地分类数据失败:",e),[]}}static saveLocalCategories(e){try{localStorage.setItem(Fa.CATEGORIES,JSON.stringify(e))}catch(t){console.error("保存本地分类数据失败:",t)}}static getUserPreferences(){try{const e=localStorage.getItem(Fa.USER_PREFERENCES),t={theme:"auto",language:"zh-CN",sidebarCollapsed:!1,defaultView:"grid",autoSync:!0,offlineMode:!1};return e?{...t,...JSON.parse(e)}:t}catch(e){return console.error("获取用户偏好设置失败:",e),{theme:"auto",language:"zh-CN",sidebarCollapsed:!1,defaultView:"grid",autoSync:!0,offlineMode:!1}}}static saveUserPreferences(e){try{const t={...this.getUserPreferences(),...e};localStorage.setItem(Fa.USER_PREFERENCES,JSON.stringify(t))}catch(t){console.error("保存用户偏好设置失败:",t)}}static getOfflineQueue(){try{const e=localStorage.getItem(Fa.OFFLINE_QUEUE);return e?JSON.parse(e):[]}catch(e){return console.error("获取离线队列失败:",e),[]}}static addOfflineAction(e){try{const t=this.getOfflineQueue(),r={...e,id:`action_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,timestamp:(new Date).toISOString()};t.push(r),localStorage.setItem(Fa.OFFLINE_QUEUE,JSON.stringify(t))}catch(t){console.error("添加离线操作失败:",t)}}static clearOfflineQueue(){try{localStorage.removeItem(Fa.OFFLINE_QUEUE)}catch(e){console.error("清空离线队列失败:",e)}}static getLastSyncTime(){return localStorage.getItem(Fa.LAST_SYNC)}static setLastSyncTime(e=(new Date).toISOString()){localStorage.setItem(Fa.LAST_SYNC,e)}static clearAllLocalData(){try{Object.values(Fa).forEach(e=>{localStorage.removeItem(e)})}catch(e){console.error("清空本地数据失败:",e)}}static getStorageInfo(){try{let e=0;for(const r in localStorage)localStorage.hasOwnProperty(r)&&(e+=localStorage[r].length);const t=5242880;return{used:e,total:t,percentage:e/t*100}}catch(e){return console.error("获取存储信息失败:",e),{used:0,total:0,percentage:0}}}}const za=b("localManagement",()=>{const e=v(!1),t=v(!1),r=v(null),s=v(null),n=v(Ga.getUserPreferences()),o=v([]),a=v([]),i=v([]),l=k(()=>i.value.length),c=k(()=>o.value.some(e=>"pending"===e.syncStatus)||a.value.some(e=>"pending"===e.syncStatus)),u=k(()=>Ga.getStorageInfo()),h=k(()=>navigator.onLine&&!e.value),d=async()=>{e.value=!1,n.value.autoSync&&l.value>0&&await g()},f=()=>{e.value=!0},p=e=>{try{Ga.saveUserPreferences(e),n.value=Ga.getUserPreferences()}catch(t){throw console.error("更新用户偏好失败:",t),t}},g=async()=>{if(!t.value&&h.value)try{t.value=!0,r.value=null;const e=Ga.getOfflineQueue();for(const t of e)await m(t);Ga.clearOfflineQueue(),i.value=[];const n=(new Date).toISOString();Ga.setLastSyncTime(n),s.value=n,_()}catch(e){throw console.error("数据同步失败:",e),r.value=e instanceof Error?e.message:"同步失败",e}finally{t.value=!1}},m=async e=>{try{switch(e.entity){case"tool":await y(e);break;case"category":await w(e);break;default:console.warn("未知的离线操作类型:",e)}}catch(t){throw console.error("处理离线操作失败:",e,t),t}},y=async e=>{switch(e.type){case"create":await Ma.createTool(e.data);break;case"update":await Ma.updateTool(e.data.id,e.data.updates);break;case"delete":await Ma.deleteTool(e.data.id)}},w=async e=>{switch(e.type){case"create":await Ba.createCategory(e.data);break;case"update":await Ba.updateCategory(e.data.id,e.data.updates);break;case"delete":await Ba.deleteCategory(e.data.id)}},_=()=>{o.value=o.value.map(e=>({...e,syncStatus:"synced"})),Ga.saveLocalTools(o.value),a.value=a.value.map(e=>({...e,syncStatus:"synced"})),Ga.saveLocalCategories(a.value)};return{isOfflineMode:e,isSyncing:t,syncError:r,lastSyncTime:s,userPreferences:n,localTools:o,localCategories:a,offlineQueue:i,pendingSyncCount:l,hasLocalChanges:c,storageInfo:u,isOnline:h,initialize:async()=>{try{o.value=Ga.getLocalTools(),a.value=Ga.getLocalCategories(),i.value=Ga.getOfflineQueue(),s.value=Ga.getLastSyncTime(),window.addEventListener("online",d),window.addEventListener("offline",f),h.value&&n.value.autoSync&&l.value>0&&await g()}catch(e){console.error("初始化本地管理失败:",e)}},addLocalTool:e=>{try{const t=Ga.addLocalTool(e);return o.value.push(t),i.value=Ga.getOfflineQueue(),t}catch(t){throw console.error("添加本地工具失败:",t),t}},updateLocalTool:(e,t)=>{try{const r=Ga.updateLocalTool(e,t);return r&&(o.value=Ga.getLocalTools(),i.value=Ga.getOfflineQueue()),r}catch(r){throw console.error("更新本地工具失败:",r),r}},deleteLocalTool:e=>{try{const t=Ga.deleteLocalTool(e);return t&&(o.value=Ga.getLocalTools(),i.value=Ga.getOfflineQueue()),t}catch(t){throw console.error("删除本地工具失败:",t),t}},updateUserPreferences:p,syncData:g,forceSyncData:async()=>{if(!h.value)throw new Error("网络不可用，无法同步数据");await g()},toggleOfflineMode:()=>{e.value=!e.value,p({offlineMode:e.value})},clearLocalData:()=>{try{Ga.clearAllLocalData(),o.value=[],a.value=[],i.value=[],s.value=null,n.value=Ga.getUserPreferences()}catch(e){throw console.error("清空本地数据失败:",e),e}},exportLocalData:()=>{try{const e={tools:o.value,categories:a.value,preferences:n.value,exportTime:(new Date).toISOString()},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),r=URL.createObjectURL(t),s=document.createElement("a");s.href=r,s.download=`local-data-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(r)}catch(e){throw console.error("导出本地数据失败:",e),e}},importLocalData:async e=>{try{const t=await e.text(),r=JSON.parse(t);r.tools&&(o.value=r.tools,Ga.saveLocalTools(r.tools)),r.categories&&(a.value=r.categories,Ga.saveLocalCategories(r.categories)),r.preferences&&p(r.preferences)}catch(t){throw console.error("导入本地数据失败:",t),t}}}}),Wa=J(Zn),Ja=H();Wa.use(Ja),Wa.use(qa),qa.beforeEach(async(e,t,r)=>{const s=sn(),n=Va(),o=["Home","EnhancedHome","Products","ProductDetail","NotFound"].includes(e.name);try{const t=e.meta.title;if(t&&(document.title=`${t} - 工具导航站`),o&&!e.meta.requiresAuth&&!e.meta.requiresAdmin)return r();if(e.meta.requiresAuth||e.meta.requiresAdmin){if(await s.initialize(),e.meta.requiresAuth&&!s.isAuthenticated)return r({name:"Login",query:{redirect:e.fullPath},replace:!0});if(e.meta.requiresAdmin&&!s.isAdmin)return r({name:"Home",replace:!0})}if(!n.initialized)try{await n.initialize()}catch(a){console.error("初始化工具数据失败:",a)}r()}catch(a){console.error("路由守卫错误:",a),r({name:"Home",replace:!0})}}),qa.afterEach(()=>{});za().initialize(),Wa.mount("#app");export{rn as A,Ba as C,Ga as L,Zs as T,tn as U,he as _,Va as a,sn as b,Ua as c,za as d,Pe as g,Xs as s,xa as u};
