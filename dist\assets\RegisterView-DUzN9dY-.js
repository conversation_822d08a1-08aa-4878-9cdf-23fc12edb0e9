import{d as e,i as a,m as s,c as l,a as r,p as t,x as o,z as i,ah as u,H as d,u as n,a7 as c,ai as v,D as p,q as m,t as w,h as f,A as g,v as h,e as b,w as y,r as k,o as x}from"./vendor-DhoxJlSg.js";import{u as _,_ as P}from"./index-BLSkTtYG.js";const T={class:"register-view"},V={class:"form-group"},q=["disabled"],U={class:"form-group"},z=["disabled"],A={class:"form-group"},j={class:"password-input"},N=["type","disabled"],Z={class:"password-strength"},C={class:"strength-bar"},D={class:"strength-text"},E={class:"form-group"},H=["disabled"],I={key:0,class:"error-hint"},R={class:"form-group"},S={class:"checkbox-label"},B=["disabled"],F={key:0,class:"loading-spinner"},G={key:0,class:"error-message"},J={class:"register-footer"},K=P(e({__name:"RegisterView",setup(e){const P=_(),K=a(!1),L=a(null),M=a(!1),O=a({full_name:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1}),Q=s(()=>{const e=O.value.password;if(!e)return{class:"",width:"0%",text:""};let a=0;return e.length>=8&&a++,/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^A-Za-z0-9]/.test(e)&&a++,a<2?{class:"weak",width:"20%",text:"弱"}:a<3?{class:"fair",width:"40%",text:"一般"}:a<4?{class:"good",width:"60%",text:"良好"}:a<5?{class:"strong",width:"80%",text:"强"}:{class:"very-strong",width:"100%",text:"很强"}}),W=s(()=>O.value.email&&O.value.password&&O.value.password===O.value.confirmPassword&&O.value.agreeToTerms&&O.value.password.length>=8),X=async()=>{try{K.value=!0,L.value=null,await new Promise(e=>setTimeout(e,1500)),console.log("注册成功:",O.value.email),P.push("/auth/login")}catch(e){L.value=e instanceof Error?e.message:"注册失败，请重试"}finally{K.value=!1}};return(e,a)=>{const s=k("router-link");return x(),l("div",T,[a[17]||(a[17]=r("div",{class:"register-header"},[r("h1",null,"注册"),r("p",null,"创建您的账户，开始使用工具导航站")],-1)),r("form",{class:"register-form",onSubmit:h(X,["prevent"])},[r("div",V,[a[6]||(a[6]=r("label",{for:"fullName"},"姓名",-1)),o(r("input",{id:"fullName","onUpdate:modelValue":a[0]||(a[0]=e=>O.value.full_name=e),type:"text",placeholder:"请输入您的姓名",disabled:K.value},null,8,q),[[i,O.value.full_name]])]),r("div",U,[a[7]||(a[7]=r("label",{for:"email"},"邮箱地址 *",-1)),o(r("input",{id:"email","onUpdate:modelValue":a[1]||(a[1]=e=>O.value.email=e),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:K.value},null,8,z),[[i,O.value.email]])]),r("div",A,[a[8]||(a[8]=r("label",{for:"password"},"密码 *",-1)),r("div",j,[o(r("input",{id:"password","onUpdate:modelValue":a[2]||(a[2]=e=>O.value.password=e),type:M.value?"text":"password",required:"",placeholder:"请输入密码（至少8位）",disabled:K.value},null,8,N),[[u,O.value.password]]),r("button",{type:"button",class:"password-toggle",onClick:a[3]||(a[3]=e=>M.value=!M.value)},[M.value?(x(),d(n(v),{key:1,class:"icon"})):(x(),d(n(c),{key:0,class:"icon"}))])]),r("div",Z,[r("div",C,[r("div",{class:m(["strength-fill",Q.value.class]),style:p({width:Q.value.width})},null,6)]),r("span",D,w(Q.value.text),1)])]),r("div",E,[a[9]||(a[9]=r("label",{for:"confirmPassword"},"确认密码 *",-1)),o(r("input",{id:"confirmPassword","onUpdate:modelValue":a[4]||(a[4]=e=>O.value.confirmPassword=e),type:"password",required:"",placeholder:"请再次输入密码",disabled:K.value},null,8,H),[[i,O.value.confirmPassword]]),O.value.confirmPassword&&O.value.password!==O.value.confirmPassword?(x(),l("div",I," 密码不匹配 ")):t("",!0)]),r("div",R,[r("label",S,[o(r("input",{"onUpdate:modelValue":a[5]||(a[5]=e=>O.value.agreeToTerms=e),type:"checkbox",required:""},null,512),[[g,O.value.agreeToTerms]]),a[10]||(a[10]=r("span",{class:"checkmark"},null,-1)),a[11]||(a[11]=f(" 我已阅读并同意 ")),a[12]||(a[12]=r("a",{href:"#",class:"terms-link"},"服务条款",-1)),a[13]||(a[13]=f(" 和 ")),a[14]||(a[14]=r("a",{href:"#",class:"terms-link"},"隐私政策",-1))])]),r("button",{type:"submit",class:"register-btn",disabled:K.value||!W.value},[K.value?(x(),l("div",F)):t("",!0),r("span",null,w(K.value?"注册中...":"注册"),1)],8,B),L.value?(x(),l("div",G,w(L.value),1)):t("",!0)],32),r("div",J,[r("p",null,[a[16]||(a[16]=f(" 已有账户？ ")),b(s,{to:"/auth/login",class:"login-link"},{default:y(()=>a[15]||(a[15]=[f("立即登录")])),_:1,__:[15]})])])])}}}),[["__scopeId","data-v-c9888b15"]]);export{K as default};
