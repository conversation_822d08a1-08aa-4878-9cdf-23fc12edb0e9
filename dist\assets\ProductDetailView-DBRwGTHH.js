import{c as a,u as s,_ as e}from"./index-BLSkTtYG.js";import{d as l,i,j as c,c as o,a as t,e as n,h as r,u,ac as d,t as p,p as v,F as g,s as m,ab as h,a7 as b,q as f,a3 as k,o as y}from"./vendor-DhoxJlSg.js";const w={class:"product-detail-view"},C={class:"container"},P={class:"back-section"},_={key:0,class:"product-detail"},I={class:"product-gallery"},j={class:"main-image"},x=["src","alt"],U={class:"product-info"},q={class:"product-header"},D={class:"product-title"},F={class:"product-price"},M={class:"current-price"},T={key:0,class:"original-price"},V={class:"product-description"},z={class:"product-tags"},A={class:"tags-list"},B={class:"product-actions"},E={key:1,class:"loading-state"},G={key:2,class:"error-state"},H=e(l({__name:"ProductDetailView",setup(e){const l=a(),H=s(),J=i(null),K=i(!0),L=i(!1),N=[{id:1,name:"高效办公套件",description:"提升办公效率的完整解决方案，包含文档处理、项目管理、时间管理、团队协作等多个模块。支持多平台同步，让您随时随地高效办公。",price:299,originalPrice:399,image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop",category:"office",tags:["办公","效率","文档","项目管理","团队协作"]},{id:2,name:"设计师工具包",description:"专业设计师必备工具集合，包含UI设计、图标制作、原型设计、色彩搭配等功能。提供丰富的设计素材和模板，助力创意实现。",price:199,image:"https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=800&h=600&fit=crop",category:"design",tags:["设计","UI","图标","创意","原型"]},{id:3,name:"开发者助手",description:"程序员开发必备工具，代码编辑、调试、部署一站式解决。支持多种编程语言，集成版本控制，提供智能代码补全和错误检测。",price:399,originalPrice:499,image:"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=600&fit=crop",category:"development",tags:["开发","编程","调试","部署","版本控制"]}],O=()=>{window.history.length>1?H.go(-1):H.push("/products")},Q=()=>{J.value&&(console.log("购买产品:",J.value.name),H.push("/payment"))},R=()=>{J.value&&console.log("预览产品:",J.value.name)},S=()=>{L.value=!L.value,console.log(L.value?"已添加到收藏":"已取消收藏")};return c(()=>{(()=>{const a=parseInt(l.params.id);setTimeout(()=>{const s=N.find(s=>s.id===a);s&&(J.value=s,L.value=Math.random()>.5),K.value=!1},300)})()}),(a,s)=>(y(),o("div",w,[t("div",C,[t("div",P,[t("button",{class:"back-btn",onClick:O},[n(u(d),{class:"icon"}),s[0]||(s[0]=r(" 返回产品列表 "))])]),J.value?(y(),o("div",_,[t("div",I,[t("div",j,[t("img",{src:J.value.image,alt:J.value.name},null,8,x)])]),t("div",U,[t("div",q,[t("h1",D,p(J.value.name),1),t("div",F,[t("span",M,"¥"+p(J.value.price),1),J.value.originalPrice?(y(),o("span",T," ¥"+p(J.value.originalPrice),1)):v("",!0)])]),t("div",V,[s[1]||(s[1]=t("h3",null,"产品描述",-1)),t("p",null,p(J.value.description),1)]),t("div",z,[s[2]||(s[2]=t("h3",null,"标签",-1)),t("div",A,[(y(!0),o(g,null,m(J.value.tags,a=>(y(),o("span",{key:a,class:"tag"},p(a),1))),128))])]),t("div",B,[t("button",{class:"buy-btn",onClick:Q},[n(u(h),{class:"icon"}),s[3]||(s[3]=r(" 立即购买 "))]),t("button",{class:"demo-btn",onClick:R},[n(u(b),{class:"icon"}),s[4]||(s[4]=r(" 在线预览 "))]),t("button",{class:"favorite-btn",onClick:S},[n(u(k),{class:f(["icon",{filled:L.value}])},null,8,["class"]),r(" "+p(L.value?"已收藏":"收藏"),1)])])])])):K.value?(y(),o("div",E,s[5]||(s[5]=[t("div",{class:"loading-spinner"},null,-1),t("p",null,"加载中...",-1)]))):(y(),o("div",G,[s[6]||(s[6]=t("div",{class:"error-icon"},"❌",-1)),s[7]||(s[7]=t("h3",null,"产品未找到",-1)),s[8]||(s[8]=t("p",null,"抱歉，您访问的产品不存在或已下架",-1)),t("button",{class:"error-action",onClick:O},"返回产品列表")]))])]))}}),[["__scopeId","data-v-eb9a8d5c"]]);export{H as default};
