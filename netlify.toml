[build]
  command = "yarn install --frozen-lockfile && yarn build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"
  # 允许安装可选依赖，包括平台特定的 Rollup 二进制文件
  NPM_CONFIG_OPTIONAL = "true"

# SPA重定向规则 - 所有请求重定向到index.html
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# 域名重定向规则
[[redirects]]
  from = "http://ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true

[[redirects]]
  from = "http://www.ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true

[[redirects]]
  from = "https://www.ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true
