[build]
  command = "rm -rf node_modules package-lock.json && npm install && npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "20"
  # 强制安装可选依赖，包括平台特定的 Rollup 二进制文件
  NPM_CONFIG_OPTIONAL = "true"
  # 确保安装所有平台依赖
  NPM_CONFIG_PLATFORM = "linux"
  NPM_CONFIG_ARCH = "x64"

# SPA重定向规则 - 所有请求重定向到index.html
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# 域名重定向规则
[[redirects]]
  from = "http://ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true

[[redirects]]
  from = "http://www.ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true

[[redirects]]
  from = "https://www.ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true
