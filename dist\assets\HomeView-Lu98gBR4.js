import{d as s,i as a,j as e,k as t,c as l,a as n,u as c,q as o,e as i,a4 as r,x as d,p as v,W as u,z as p,a5 as h,h as y,a2 as g,a6 as b,t as k,F as m,s as C,a1 as f,v as w,a7 as x,a8 as _,o as Q}from"./vendor-DhoxJlSg.js";import{a as S,_ as K}from"./index-Dhbj_B_-.js";const F={class:"fluent-app"},T={class:"app-header"},j={class:"header-content"},z={class:"header-left"},E={class:"header-center"},I={class:"search-container"},q={key:0,class:"search-shortcut"},L={class:"header-right"},U={class:"app-main"},V={class:"sidebar-content"},B={class:"category-nav"},D={class:"nav-section"},G={class:"nav-list"},H={class:"nav-count"},O={class:"nav-count"},R={class:"nav-section"},W={class:"nav-list"},$=["onClick"],A={class:"nav-icon"},J={class:"nav-text"},M={class:"nav-count"},N={key:0,class:"hero-section"},P={class:"hero-content"},X={class:"hero-stats"},Y={class:"stat-item"},Z={class:"stat-number"},ss={class:"stat-item"},as={class:"stat-number"},es={class:"stat-item"},ts={class:"stat-number"},ls={class:"content-header"},ns={class:"content-title"},cs={class:"content-count"},os={class:"content-actions"},is={class:"view-options"},rs={class:"view-button active"},ds={key:1,class:"tools-grid"},vs=["onClick"],us={class:"card-header"},ps={class:"tool-icon"},hs=["onClick"],ys={class:"card-content"},gs={class:"tool-name"},bs={class:"tool-description"},ks={class:"tool-tags"},ms={key:0,class:"tag more"},Cs={class:"card-footer"},fs={class:"tool-stats"},ws={class:"stat"},xs={key:2,class:"empty-state"},_s=K(s({__name:"HomeView",setup(s){const K=S(),_s=a(null),Qs=a(!1),Ss=()=>{if("all"===K.selectedCategory)return"全部工具";if("favorites"===K.selectedCategory)return"我的收藏";const s=K.categories.find(s=>s.id===K.selectedCategory);return s?s.name:"未知分类"},Ks=()=>{K.searchQuery.trim()&&Fs()},Fs=()=>{const s=K.searchQuery.trim();if(s){const a=`https://www.google.com/search?q=${encodeURIComponent(s+" 工具")}`;window.open(a,"_blank","noopener,noreferrer")}},Ts=()=>{K.setSelectedCategory("favorites")},js=()=>{window.open("/user/profile","_blank")},zs=()=>{window.open("/products","_blank")},Es=s=>{var a,e;(s.ctrlKey||s.metaKey)&&"k"===s.key&&(s.preventDefault(),null==(a=_s.value)||a.focus()),"Escape"===s.key&&(null==(e=_s.value)||e.blur(),K.setSearchQuery(""))};return e(async()=>{K.initialized||await K.initialize(),document.addEventListener("keydown",Es)}),t(()=>{document.removeEventListener("keydown",Es)}),(s,a)=>(Q(),l("div",F,[n("div",{tabindex:"-1",class:"app-container",onKeydown:Es},[n("header",T,[n("div",j,[n("div",z,[n("button",{class:o(["sidebar-toggle",{active:!c(K).sidebarCollapsed}]),onClick:a[0]||(a[0]=s=>c(K).toggleSidebar())},[i(c(r),{class:"icon"})],2),a[7]||(a[7]=n("div",{class:"app-title"},[n("div",{class:"title-icon"},"🚀"),n("div",{class:"title-text"},[n("h1",null,"工具导航站"),n("span",null,"让工作更高效")])],-1))]),n("div",E,[n("div",I,[i(c(u),{class:"search-icon"}),d(n("input",{ref_key:"searchInput",ref:_s,"onUpdate:modelValue":a[1]||(a[1]=s=>c(K).searchQuery=s),type:"text",placeholder:"搜索工具... (Ctrl+K)",class:"search-input",onFocus:a[2]||(a[2]=s=>Qs.value=!0),onBlur:a[3]||(a[3]=s=>Qs.value=!1),onKeydown:h(Ks,["enter"])},null,544),[[p,c(K).searchQuery]]),Qs.value||c(K).searchQuery?v("",!0):(Q(),l("div",q,a[8]||(a[8]=[n("kbd",null,"Ctrl",-1),y(" + "),n("kbd",null,"K",-1)]))),c(K).searchQuery.trim()?(Q(),l("button",{key:1,class:"external-search-btn",title:"在Google中搜索",onClick:Fs}," 🌐 ")):v("",!0)])]),n("div",L,[n("button",{class:o(["header-button",{active:c(K).showFavoritesOnly}]),onClick:Ts},[i(c(g),{class:"icon"}),a[9]||(a[9]=n("span",null,"收藏",-1))],2),n("button",{class:"user-avatar",onClick:js},[i(c(b),{class:"icon"})])])])]),n("div",U,[n("aside",{class:o(["sidebar",{collapsed:c(K).sidebarCollapsed}])},[n("div",V,[n("nav",B,[n("div",D,[a[15]||(a[15]=n("h3",{class:"nav-title"},"导航",-1)),n("ul",G,[n("li",null,[n("button",{class:o(["nav-item",{active:"all"===c(K).selectedCategory}]),onClick:a[4]||(a[4]=s=>c(K).setSelectedCategory("all"))},[a[10]||(a[10]=n("div",{class:"nav-icon"},"🏠",-1)),a[11]||(a[11]=n("span",{class:"nav-text"},"全部工具",-1)),n("span",H,k(c(K).tools.length),1)],2)]),n("li",null,[n("button",{class:o(["nav-item",{active:"favorites"===c(K).selectedCategory}]),onClick:a[5]||(a[5]=s=>c(K).setSelectedCategory("favorites"))},[a[12]||(a[12]=n("div",{class:"nav-icon"},"⭐",-1)),a[13]||(a[13]=n("span",{class:"nav-text"},"我的收藏",-1)),n("span",O,k(c(K).favoriteTools.length),1)],2)]),n("li",null,[n("button",{class:"nav-item",onClick:zs},a[14]||(a[14]=[n("div",{class:"nav-icon"},"🛍️",-1),n("span",{class:"nav-text"},"产品展示",-1),n("span",{class:"nav-count"},"12",-1)]))])])]),n("div",R,[a[16]||(a[16]=n("h3",{class:"nav-title"},"分类",-1)),n("ul",W,[(Q(!0),l(m,null,C(c(K).categories,s=>(Q(),l("li",{key:s.id},[n("button",{class:o(["nav-item",{active:c(K).selectedCategory===s.id}]),onClick:a=>c(K).setSelectedCategory(s.id)},[n("div",A,k(s.icon),1),n("span",J,k(s.name),1),n("span",M,k(s.count),1)],10,$)]))),128))])])])])],2),n("main",{class:o(["content",{"sidebar-collapsed":c(K).sidebarCollapsed}])},["all"===c(K).selectedCategory?(Q(),l("div",N,[n("div",P,[a[20]||(a[20]=n("h1",{class:"hero-title"},"发现优质工具",-1)),a[21]||(a[21]=n("p",{class:"hero-subtitle"},"精选高效工具，提升工作效率",-1)),n("div",X,[n("div",Y,[n("span",Z,k(c(K).tools.length),1),a[17]||(a[17]=n("span",{class:"stat-label"},"精选工具",-1))]),n("div",ss,[n("span",as,k(c(K).categories.length),1),a[18]||(a[18]=n("span",{class:"stat-label"},"工具分类",-1))]),n("div",es,[n("span",ts,k(c(K).favoriteTools.length),1),a[19]||(a[19]=n("span",{class:"stat-label"},"我的收藏",-1))])])])])):v("",!0),n("div",ls,[n("div",ns,[n("h2",null,k(Ss()),1),n("span",cs,k(c(K).filteredTools.length)+" 个工具",1)]),n("div",os,[n("div",is,[n("button",rs,[i(c(f),{class:"icon"})])])])]),c(K).filteredTools.length>0?(Q(),l("div",ds,[(Q(!0),l(m,null,C(c(K).filteredTools,s=>(Q(),l("div",{key:s.id,class:"tool-card",onClick:a=>(s=>{K.incrementClickCount(s.id),window.open(s.url,"_blank","noopener,noreferrer")})(s)},[n("div",us,[n("div",ps,k(s.icon),1),n("button",{class:o(["favorite-button",{active:s.isFavorite}]),onClick:w(a=>c(K).toggleFavorite(s.id),["stop"])},[i(c(g),{class:"icon"})],10,hs)]),n("div",ys,[n("h3",gs,k(s.name),1),n("p",bs,k(s.description),1),n("div",ks,[(Q(!0),l(m,null,C(s.tags.slice(0,3),s=>(Q(),l("span",{key:s,class:"tag"},k(s),1))),128)),s.tags.length>3?(Q(),l("span",ms," +"+k(s.tags.length-3),1)):v("",!0)])]),n("div",Cs,[n("div",fs,[n("span",ws,[i(c(x),{class:"stat-icon"}),y(" "+k(s.clickCount),1)])]),i(c(_),{class:"external-icon"})])],8,vs))),128))])):(Q(),l("div",xs,[a[22]||(a[22]=n("div",{class:"empty-icon"},"🔍",-1)),a[23]||(a[23]=n("h3",null,"未找到相关工具",-1)),a[24]||(a[24]=n("p",null,"尝试使用其他关键词搜索，或浏览其他分类",-1)),n("button",{class:"empty-action",onClick:a[6]||(a[6]=s=>c(K).setSearchQuery(""))}," 清除搜索条件 ")])),a[25]||(a[25]=n("div",{class:"bottom-spacer"},null,-1))],2)])],32)]))}}),[["__scopeId","data-v-a020019a"]]);export{_s as default};
