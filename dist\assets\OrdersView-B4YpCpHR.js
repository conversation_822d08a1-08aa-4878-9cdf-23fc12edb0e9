import{u as e,_ as t}from"./index-Dhbj_B_-.js";import{d as a,i as s,m as l,j as n,c as i,a as r,F as d,s as o,q as c,h as u,t as p,p as v,e as m,u as y,ag as g,w as k,r as h,o as b}from"./vendor-DhoxJlSg.js";const f={class:"orders-view"},w={class:"orders-filters"},I=["onClick"],S={class:"filter-count"},C={class:"orders-content"},D={key:0,class:"loading-state"},A={key:1,class:"orders-list"},O={class:"order-header"},_={class:"order-info"},P={class:"order-number"},j={class:"order-date"},q={class:"order-status"},x={class:"order-items"},N={class:"item-image"},Y=["src","alt"],z={class:"item-info"},F={class:"item-name"},L={class:"item-description"},M={class:"item-meta"},T={class:"item-quantity"},U={class:"item-price"},V={class:"item-total"},$={class:"order-footer"},B={class:"order-total"},E={class:"total-amount"},G={class:"order-actions"},H=["onClick"],J=["onClick"],K=["onClick"],Q=["onClick"],R={key:2,class:"empty-state"},W=t(a({__name:"OrdersView",setup(t){const a=e(),W=s(!0),X=s("all"),Z=s([]),ee=[{key:"all",label:"全部订单"},{key:"pending",label:"待支付"},{key:"paid",label:"已支付"},{key:"cancelled",label:"已取消"},{key:"refunded",label:"已退款"}],te=l(()=>"all"===X.value?Z.value:Z.value.filter(e=>e.status===X.value));return n(()=>{(async()=>{try{W.value=!0,await new Promise(e=>setTimeout(e,1e3)),Z.value=[{id:"order-1",userId:"user-1",items:[{id:"item-1",orderId:"order-1",productId:"product-1",quantity:1,unitPrice:299,totalPrice:299,createdAt:(new Date).toISOString(),product:{id:"product-1",name:"高效办公套件",shortDescription:"提升办公效率的完整解决方案",images:["/placeholder.jpg"]}}],totalAmount:299,currency:"CNY",status:"paid",paymentMethod:"alipay",createdAt:new Date(Date.now()-864e5).toISOString(),updatedAt:(new Date).toISOString(),completedAt:(new Date).toISOString()},{id:"order-2",userId:"user-1",items:[{id:"item-2",orderId:"order-2",productId:"product-2",quantity:1,unitPrice:199,totalPrice:199,createdAt:(new Date).toISOString(),product:{id:"product-2",name:"设计师工具包",shortDescription:"专业设计师必备工具集合",images:["/placeholder.jpg"]}}],totalAmount:199,currency:"CNY",status:"pending",createdAt:new Date(Date.now()-36e5).toISOString(),updatedAt:(new Date).toISOString()}]}catch(e){console.error("加载订单失败:",e)}finally{W.value=!1}})()}),(e,t)=>{const s=h("router-link");return b(),i("div",f,[t[5]||(t[5]=r("div",{class:"orders-header"},[r("h1",null,"我的订单"),r("p",null,"查看和管理您的订单历史")],-1)),r("div",w,[(b(),i(d,null,o(ee,e=>{return r("button",{key:e.key,class:c(["filter-btn",{active:X.value===e.key}]),onClick:t=>X.value=e.key},[u(p(e.label)+" ",1),r("span",S,p((t=e.key,"all"===t?Z.value.length:Z.value.filter(e=>e.status===t).length)),1)],10,I);var t}),64))]),r("div",C,[W.value?(b(),i("div",D,t[0]||(t[0]=[r("div",{class:"loading-spinner"},null,-1),r("p",null,"正在加载订单...",-1)]))):te.value.length>0?(b(),i("div",A,[(b(!0),i(d,null,o(te.value,e=>{return b(),i("div",{key:e.id,class:"order-item"},[r("div",O,[r("div",_,[r("h3",P," 订单号: "+p(e.id.slice(-8).toUpperCase()),1),r("p",j," 下单时间: "+p((l=e.createdAt,new Date(l).toLocaleString("zh-CN"))),1)]),r("div",q,[r("span",{class:c(["status-badge",e.status])},p((s=e.status,{pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[s]||s)),3)])]),r("div",x,[(b(!0),i(d,null,o(e.items,e=>{var t,a,s,l,n;return b(),i("div",{key:e.id,class:"order-item-detail"},[r("div",N,[r("img",{src:(null==(a=null==(t=e.product)?void 0:t.images)?void 0:a[0])||"/placeholder.jpg",alt:null==(s=e.product)?void 0:s.name},null,8,Y)]),r("div",z,[r("h4",F,p(null==(l=e.product)?void 0:l.name),1),r("p",L,p(null==(n=e.product)?void 0:n.shortDescription),1),r("div",M,[r("span",T,"数量: "+p(e.quantity),1),r("span",U,"单价: ¥"+p(e.unitPrice),1)])]),r("div",V,"¥"+p(e.totalPrice),1)])}),128))]),r("div",$,[r("div",B,[t[1]||(t[1]=r("span",{class:"total-label"},"订单总额:",-1)),r("span",E,"¥"+p(e.totalAmount),1)]),r("div",G,["pending"===e.status?(b(),i("button",{key:0,class:"action-btn primary",onClick:t=>(e=>{a.push(`/payment?order=${e.id}`)})(e)}," 立即支付 ",8,H)):v("",!0),"pending"===e.status?(b(),i("button",{key:1,class:"action-btn secondary",onClick:t=>(async e=>{if(confirm("确定要取消这个订单吗？"))try{const t=Z.value.findIndex(t=>t.id===e.id);-1!==t&&(Z.value[t].status="cancelled")}catch(t){console.error("取消订单失败:",t)}})(e)}," 取消订单 ",8,J)):v("",!0),"paid"===e.status?(b(),i("button",{key:2,class:"action-btn secondary",onClick:t=>(e=>{console.log("下载订单产品:",e.id)})(e)},[m(y(g),{class:"icon"}),t[2]||(t[2]=u(" 下载产品 "))],8,K)):v("",!0),r("button",{class:"action-btn secondary",onClick:t=>(e=>{console.log("查看订单详情:",e.id)})(e)}," 查看详情 ",8,Q)])])]);var s,l}),128))])):(b(),i("div",R,[t[4]||(t[4]=r("div",{class:"empty-icon"},"📦",-1)),r("h3",null,p({all:"暂无订单",pending:"暂无待支付订单",paid:"暂无已支付订单",cancelled:"暂无已取消订单",refunded:"暂无已退款订单"}[X.value]||"暂无订单"),1),r("p",null,p({all:"您还没有任何订单，去购买一些产品吧！",pending:"您没有待支付的订单",paid:"您没有已支付的订单",cancelled:"您没有已取消的订单",refunded:"您没有已退款的订单"}[X.value]||"暂无相关订单"),1),m(s,{to:"/products",class:"empty-action"},{default:k(()=>t[3]||(t[3]=[u("去购买产品")])),_:1,__:[3]})]))])])}}}),[["__scopeId","data-v-355be95d"]]);export{W as default};
