import{d as a,i as e,c as l,a as s,p as o,x as r,z as i,ah as u,H as t,u as n,a7 as d,ai as c,e as p,h as v,A as m,w as b,r as f,t as g,v as w,o as h}from"./vendor-DhoxJlSg.js";import{u as y,_ as k}from"./index-BWTdsPe5.js";const _={class:"login-view"},x={class:"form-group"},V=["disabled"],U={class:"form-group"},j={class:"password-input"},q=["type","disabled"],z={class:"form-options"},A={class:"checkbox-label"},C=["disabled"],E={key:0,class:"loading-spinner"},H={key:0,class:"error-message"},I={class:"login-footer"},L=k(a({__name:"LoginView",setup(a){const k=y(),L=e(!1),P=e(null),S=e(!1),T=e({email:"",password:"",remember:!1}),B=async()=>{try{L.value=!0,P.value=null,await new Promise(a=>setTimeout(a,1e3)),console.log("登录成功:",T.value.email),k.push("/")}catch(a){P.value=a instanceof Error?a.message:"登录失败，请重试"}finally{L.value=!1}};return(a,e)=>{const y=f("router-link");return h(),l("div",_,[e[11]||(e[11]=s("div",{class:"login-header"},[s("h1",null,"登录"),s("p",null,"欢迎回来，请登录您的账户")],-1)),s("form",{class:"login-form",onSubmit:w(B,["prevent"])},[s("div",x,[e[4]||(e[4]=s("label",{for:"email"},"邮箱地址",-1)),r(s("input",{id:"email","onUpdate:modelValue":e[0]||(e[0]=a=>T.value.email=a),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:L.value},null,8,V),[[i,T.value.email]])]),s("div",U,[e[5]||(e[5]=s("label",{for:"password"},"密码",-1)),s("div",j,[r(s("input",{id:"password","onUpdate:modelValue":e[1]||(e[1]=a=>T.value.password=a),type:S.value?"text":"password",required:"",placeholder:"请输入您的密码",disabled:L.value},null,8,q),[[u,T.value.password]]),s("button",{type:"button",class:"password-toggle",onClick:e[2]||(e[2]=a=>S.value=!S.value)},[S.value?(h(),t(n(c),{key:1,class:"icon"})):(h(),t(n(d),{key:0,class:"icon"}))])])]),s("div",z,[s("label",A,[r(s("input",{"onUpdate:modelValue":e[3]||(e[3]=a=>T.value.remember=a),type:"checkbox"},null,512),[[m,T.value.remember]]),e[6]||(e[6]=s("span",{class:"checkmark"},null,-1)),e[7]||(e[7]=v(" 记住我 "))]),p(y,{to:"/auth/forgot-password",class:"forgot-link"},{default:b(()=>e[8]||(e[8]=[v(" 忘记密码？ ")])),_:1,__:[8]})]),s("button",{type:"submit",class:"login-btn",disabled:L.value},[L.value?(h(),l("div",E)):o("",!0),s("span",null,g(L.value?"登录中...":"登录"),1)],8,C),P.value?(h(),l("div",H,g(P.value),1)):o("",!0)],32),s("div",I,[s("p",null,[e[10]||(e[10]=v(" 还没有账户？ ")),p(y,{to:"/auth/register",class:"register-link"},{default:b(()=>e[9]||(e[9]=[v("立即注册")])),_:1,__:[9]})])])])}}}),[["__scopeId","data-v-61ca95f0"]]);export{L as default};
