import{d as a,i as s,j as t,c as d,a as e,e as l,u as i,ae as c,t as o,af as v,ak as n,ap as r,b as u,x as h,y as b,a4 as m,w as p,h as f,r as w,F as T,s as _,q as g,o as M}from"./vendor-gTrNmiX-.js";import{_ as k}from"./index-BBkjtX5T.js";const U={class:"dashboard-view"},C={class:"dashboard-content"},y={class:"stats-grid"},N={class:"stat-card"},P={class:"stat-icon tools"},j={class:"stat-info"},x={class:"stat-number"},A={class:"stat-change positive"},F={class:"stat-card"},G={class:"stat-icon products"},R={class:"stat-info"},V={class:"stat-number"},I={class:"stat-change positive"},q={class:"stat-card"},z={class:"stat-icon users"},D={class:"stat-info"},S={class:"stat-number"},B={class:"stat-change positive"},E={class:"stat-card"},H={class:"stat-icon revenue"},J={class:"stat-info"},K={class:"stat-number"},L={class:"stat-change positive"},O={class:"dashboard-grid"},Q={class:"dashboard-card"},W={class:"card-header"},X={class:"card-content"},Y={class:"chart-placeholder"},Z={class:"dashboard-card"},$={class:"card-header"},aa={class:"card-content"},sa={class:"popular-items"},ta={class:"item-icon"},da={class:"item-info"},ea={class:"item-name"},la={class:"item-stats"},ia={class:"item-trend"},ca={class:"dashboard-card"},oa={class:"card-header"},va={class:"card-content"},na={class:"recent-orders"},ra={class:"order-info"},ua={class:"order-id"},ha={class:"order-user"},ba={class:"order-amount"},ma=k(a({__name:"DashboardView",setup(a){const k=s("30d"),ma=s({totalTools:0,totalProducts:0,totalUsers:0,totalRevenue:0,newToolsThisMonth:0,newProductsThisMonth:0,newUsersThisMonth:0,revenueGrowth:0}),pa=s([{id:"1",name:"VS Code",icon:"💻",clickCount:1250},{id:"2",name:"Figma",icon:"🎨",clickCount:980},{id:"3",name:"Notion",icon:"📝",clickCount:756}]),fa=s([{id:"order-1",user:{fullName:"张三"},totalAmount:299,status:"paid"},{id:"order-2",user:{fullName:"李四"},totalAmount:199,status:"pending"}]);return t(()=>{(async()=>{try{ma.value={totalTools:156,totalProducts:42,totalUsers:1284,totalRevenue:125680,newToolsThisMonth:12,newProductsThisMonth:5,newUsersThisMonth:89,revenueGrowth:15.6}}catch(a){console.error("加载仪表盘数据失败:",a)}})()}),(a,s)=>{const t=w("router-link");return M(),d("div",U,[s[13]||(s[13]=e("div",{class:"dashboard-header"},[e("h1",null,"仪表盘"),e("p",null,"系统概览和关键指标")],-1)),e("div",C,[e("div",y,[e("div",N,[e("div",P,[l(i(c),{class:"icon"})]),e("div",j,[e("div",x,o(ma.value.totalTools),1),s[1]||(s[1]=e("div",{class:"stat-label"},"工具总数",-1)),e("div",A," +"+o(ma.value.newToolsThisMonth)+" 本月新增 ",1)])]),e("div",F,[e("div",G,[l(i(v),{class:"icon"})]),e("div",R,[e("div",V,o(ma.value.totalProducts),1),s[2]||(s[2]=e("div",{class:"stat-label"},"产品总数",-1)),e("div",I," +"+o(ma.value.newProductsThisMonth)+" 本月新增 ",1)])]),e("div",q,[e("div",z,[l(i(n),{class:"icon"})]),e("div",D,[e("div",S,o(ma.value.totalUsers),1),s[3]||(s[3]=e("div",{class:"stat-label"},"用户总数",-1)),e("div",B," +"+o(ma.value.newUsersThisMonth)+" 本月新增 ",1)])]),e("div",E,[e("div",H,[l(i(r),{class:"icon"})]),e("div",J,[e("div",K," ¥"+o((wa=ma.value.totalRevenue,new Intl.NumberFormat("zh-CN").format(wa))),1),s[4]||(s[4]=e("div",{class:"stat-label"},"总收入",-1)),e("div",L," +"+o(ma.value.revenueGrowth)+"% 本月增长 ",1)])])]),e("div",O,[e("div",Q,[e("div",W,[s[6]||(s[6]=e("h3",null,"访问趋势",-1)),h(e("select",{"onUpdate:modelValue":s[0]||(s[0]=a=>k.value=a),class:"period-select"},s[5]||(s[5]=[e("option",{value:"7d"},"最近7天",-1),e("option",{value:"30d"},"最近30天",-1),e("option",{value:"90d"},"最近90天",-1)]),512),[[b,k.value]])]),e("div",X,[e("div",Y,[l(i(m),{class:"chart-icon"}),s[7]||(s[7]=e("p",null,"访问量图表",-1)),e("small",null,o("7d"===k.value?"7天":"30d"===k.value?"30天":"90天")+"内的访问趋势",1)])])]),e("div",Z,[e("div",$,[s[9]||(s[9]=e("h3",null,"热门工具",-1)),l(t,{to:"/admin/tools",class:"view-all"},{default:p(()=>s[8]||(s[8]=[f("查看全部")])),_:1,__:[8]})]),e("div",aa,[e("div",sa,[(M(!0),d(T,null,_(pa.value,a=>(M(),d("div",{key:a.id,class:"popular-item"},[e("div",ta,o(a.icon||"🔧"),1),e("div",da,[e("div",ea,o(a.name),1),e("div",la,o(a.clickCount)+" 次访问",1)]),e("div",ia,[l(i(m),{class:"trend-icon"})])]))),128))])])]),e("div",ca,[e("div",oa,[s[11]||(s[11]=e("h3",null,"最新订单",-1)),l(t,{to:"/admin/orders",class:"view-all"},{default:p(()=>s[10]||(s[10]=[f("查看全部")])),_:1,__:[10]})]),e("div",va,[e("div",na,[(M(!0),d(T,null,_(fa.value,a=>{var s,t;return M(),d("div",{key:a.id,class:"order-item"},[e("div",ra,[e("div",ua," #"+o(a.id.slice(-8).toUpperCase()),1),e("div",ha,o((null==(s=a.user)?void 0:s.fullName)||"匿名用户"),1)]),e("div",ba,"¥"+o(a.totalAmount),1),e("div",{class:g(["order-status",a.status])},o((t=a.status,{pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[t]||t)),3)])}),128))])])]),s[12]||(s[12]=u('<div class="dashboard-card" data-v-b9ea1d78><div class="card-header" data-v-b9ea1d78><h3 data-v-b9ea1d78>系统状态</h3></div><div class="card-content" data-v-b9ea1d78><div class="system-status" data-v-b9ea1d78><div class="status-item" data-v-b9ea1d78><div class="status-indicator online" data-v-b9ea1d78></div><div class="status-info" data-v-b9ea1d78><div class="status-label" data-v-b9ea1d78>数据库</div><div class="status-value" data-v-b9ea1d78>正常</div></div></div><div class="status-item" data-v-b9ea1d78><div class="status-indicator online" data-v-b9ea1d78></div><div class="status-info" data-v-b9ea1d78><div class="status-label" data-v-b9ea1d78>存储服务</div><div class="status-value" data-v-b9ea1d78>正常</div></div></div><div class="status-item" data-v-b9ea1d78><div class="status-indicator online" data-v-b9ea1d78></div><div class="status-info" data-v-b9ea1d78><div class="status-label" data-v-b9ea1d78>支付服务</div><div class="status-value" data-v-b9ea1d78>正常</div></div></div></div></div></div>',1))])])]);var wa}}}),[["__scopeId","data-v-b9ea1d78"]]);export{ma as default};
