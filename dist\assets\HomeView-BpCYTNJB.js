import{d as s,r as a,o as e,Z as t,m as l,q as n,u as c,H as o,y as i,$ as r,A as d,t as v,a2 as u,B as p,a3 as h,N as y,a6 as b,a5 as g,C as m,F as k,z as C,an as f,x as w,ap as x,O as _,G as Q}from"./vendor-CSeT1gXp.js";import{b as S,_ as K}from"./index-BKFWLFbU.js";const F={class:"fluent-app"},T={class:"app-header"},z={class:"header-content"},E={class:"header-left"},I={class:"header-center"},j={class:"search-container"},q={key:0,class:"search-shortcut"},B={class:"header-right"},G={class:"app-main"},H={class:"sidebar-content"},L={class:"category-nav"},O={class:"nav-section"},U={class:"nav-list"},V={class:"nav-count"},$={class:"nav-count"},A={class:"nav-section"},D={class:"nav-list"},N=["onClick"],R={class:"nav-icon"},Z={class:"nav-text"},J={class:"nav-count"},M={key:0,class:"hero-section"},P={class:"hero-content"},W={class:"hero-stats"},X={class:"stat-item"},Y={class:"stat-number"},ss={class:"stat-item"},as={class:"stat-number"},es={class:"stat-item"},ts={class:"stat-number"},ls={class:"content-header"},ns={class:"content-title"},cs={class:"content-count"},os={class:"content-actions"},is={class:"view-options"},rs={class:"view-button active"},ds={key:1,class:"tools-grid"},vs=["onClick"],us={class:"card-header"},ps={class:"tool-icon"},hs=["onClick"],ys={class:"card-content"},bs={class:"tool-name"},gs={class:"tool-description"},ms={class:"tool-tags"},ks={key:0,class:"tag more"},Cs={class:"card-footer"},fs={class:"tool-stats"},ws={class:"stat"},xs={key:2,class:"empty-state"},_s=K(s({__name:"HomeView",setup(s){const K=S(),_s=a(null),Qs=a(!1),Ss=()=>{if("all"===K.selectedCategory)return"全部工具";if("favorites"===K.selectedCategory)return"我的收藏";const s=K.categories.find(s=>s.id===K.selectedCategory);return s?s.name:"未知分类"},Ks=()=>{K.searchQuery.trim()&&Fs()},Fs=()=>{const s=K.searchQuery.trim();if(s){const a=`https://www.google.com/search?q=${encodeURIComponent(s+" 工具")}`;window.open(a,"_blank","noopener,noreferrer")}},Ts=()=>{K.setSelectedCategory("favorites")},zs=()=>{window.open("/user/profile","_blank")},Es=()=>{window.open("/products","_blank")},Is=s=>{var a,e;(s.ctrlKey||s.metaKey)&&"k"===s.key&&(s.preventDefault(),null==(a=_s.value)||a.focus()),"Escape"===s.key&&(null==(e=_s.value)||e.blur(),K.setSearchQuery(""))};return e(async()=>{K.initialized||await K.initialize(),document.addEventListener("keydown",Is)}),t(()=>{document.removeEventListener("keydown",Is)}),(s,a)=>(Q(),l("div",F,[n("div",{tabindex:"-1",class:"app-container",onKeydown:Is},[n("header",T,[n("div",z,[n("div",E,[n("button",{class:o(["sidebar-toggle",{active:!c(K).sidebarCollapsed}]),onClick:a[0]||(a[0]=s=>c(K).toggleSidebar())},[i(c(r),{class:"icon"})],2),a[7]||(a[7]=n("div",{class:"app-title"},[n("div",{class:"title-icon"},"🚀"),n("div",{class:"title-text"},[n("h1",null,"工具导航站"),n("span",null,"让工作更高效")])],-1))]),n("div",I,[n("div",j,[i(c(u),{class:"search-icon"}),d(n("input",{ref_key:"searchInput",ref:_s,"onUpdate:modelValue":a[1]||(a[1]=s=>c(K).searchQuery=s),type:"text",placeholder:"搜索工具... (Ctrl+K)",class:"search-input",onFocus:a[2]||(a[2]=s=>Qs.value=!0),onBlur:a[3]||(a[3]=s=>Qs.value=!1),onKeydown:h(Ks,["enter"])},null,544),[[p,c(K).searchQuery]]),Qs.value||c(K).searchQuery?v("",!0):(Q(),l("div",q,a[8]||(a[8]=[n("kbd",null,"Ctrl",-1),y(" + "),n("kbd",null,"K",-1)]))),c(K).searchQuery.trim()?(Q(),l("button",{key:1,class:"external-search-btn",title:"在Google中搜索",onClick:Fs}," 🌐 ")):v("",!0)])]),n("div",B,[n("button",{class:o(["header-button",{active:c(K).showFavoritesOnly}]),onClick:Ts},[i(c(b),{class:"icon"}),a[9]||(a[9]=n("span",null,"收藏",-1))],2),n("button",{class:"user-avatar",onClick:zs},[i(c(g),{class:"icon"})])])])]),n("div",G,[n("aside",{class:o(["sidebar",{collapsed:c(K).sidebarCollapsed}])},[n("div",H,[n("nav",L,[n("div",O,[a[15]||(a[15]=n("h3",{class:"nav-title"},"导航",-1)),n("ul",U,[n("li",null,[n("button",{class:o(["nav-item",{active:"all"===c(K).selectedCategory}]),onClick:a[4]||(a[4]=s=>c(K).setSelectedCategory("all"))},[a[10]||(a[10]=n("div",{class:"nav-icon"},"🏠",-1)),a[11]||(a[11]=n("span",{class:"nav-text"},"全部工具",-1)),n("span",V,m(c(K).tools.length),1)],2)]),n("li",null,[n("button",{class:o(["nav-item",{active:"favorites"===c(K).selectedCategory}]),onClick:a[5]||(a[5]=s=>c(K).setSelectedCategory("favorites"))},[a[12]||(a[12]=n("div",{class:"nav-icon"},"⭐",-1)),a[13]||(a[13]=n("span",{class:"nav-text"},"我的收藏",-1)),n("span",$,m(c(K).favoriteTools.length),1)],2)]),n("li",null,[n("button",{class:"nav-item",onClick:Es},a[14]||(a[14]=[n("div",{class:"nav-icon"},"🛍️",-1),n("span",{class:"nav-text"},"产品展示",-1),n("span",{class:"nav-count"},"12",-1)]))])])]),n("div",A,[a[16]||(a[16]=n("h3",{class:"nav-title"},"分类",-1)),n("ul",D,[(Q(!0),l(k,null,C(c(K).categories,s=>(Q(),l("li",{key:s.id},[n("button",{class:o(["nav-item",{active:c(K).selectedCategory===s.id}]),onClick:a=>c(K).setSelectedCategory(s.id)},[n("div",R,m(s.icon),1),n("span",Z,m(s.name),1),n("span",J,m(s.count),1)],10,N)]))),128))])])])])],2),n("main",{class:o(["content",{"sidebar-collapsed":c(K).sidebarCollapsed}])},["all"===c(K).selectedCategory?(Q(),l("div",M,[n("div",P,[a[20]||(a[20]=n("h1",{class:"hero-title"},"发现优质工具",-1)),a[21]||(a[21]=n("p",{class:"hero-subtitle"},"精选高效工具，提升工作效率",-1)),n("div",W,[n("div",X,[n("span",Y,m(c(K).tools.length),1),a[17]||(a[17]=n("span",{class:"stat-label"},"精选工具",-1))]),n("div",ss,[n("span",as,m(c(K).categories.length),1),a[18]||(a[18]=n("span",{class:"stat-label"},"工具分类",-1))]),n("div",es,[n("span",ts,m(c(K).favoriteTools.length),1),a[19]||(a[19]=n("span",{class:"stat-label"},"我的收藏",-1))])])])])):v("",!0),n("div",ls,[n("div",ns,[n("h2",null,m(Ss()),1),n("span",cs,m(c(K).filteredTools.length)+" 个工具",1)]),n("div",os,[n("div",is,[n("button",rs,[i(c(f),{class:"icon"})])])])]),c(K).filteredTools.length>0?(Q(),l("div",ds,[(Q(!0),l(k,null,C(c(K).filteredTools,s=>(Q(),l("div",{key:s.id,class:"tool-card",onClick:a=>(s=>{K.incrementClickCount(s.id),window.open(s.url,"_blank","noopener,noreferrer")})(s)},[n("div",us,[n("div",ps,m(s.icon),1),n("button",{class:o(["favorite-button",{active:s.isFavorite}]),onClick:w(a=>c(K).toggleFavorite(s.id),["stop"])},[i(c(b),{class:"icon"})],10,hs)]),n("div",ys,[n("h3",bs,m(s.name),1),n("p",gs,m(s.description),1),n("div",ms,[(Q(!0),l(k,null,C(s.tags.slice(0,3),s=>(Q(),l("span",{key:s,class:"tag"},m(s),1))),128)),s.tags.length>3?(Q(),l("span",ks," +"+m(s.tags.length-3),1)):v("",!0)])]),n("div",Cs,[n("div",fs,[n("span",ws,[i(c(x),{class:"stat-icon"}),y(" "+m(s.clickCount),1)])]),i(c(_),{class:"external-icon"})])],8,vs))),128))])):(Q(),l("div",xs,[a[22]||(a[22]=n("div",{class:"empty-icon"},"🔍",-1)),a[23]||(a[23]=n("h3",null,"未找到相关工具",-1)),a[24]||(a[24]=n("p",null,"尝试使用其他关键词搜索，或浏览其他分类",-1)),n("button",{class:"empty-action",onClick:a[6]||(a[6]=s=>c(K).setSearchQuery(""))}," 清除搜索条件 ")])),a[25]||(a[25]=n("div",{class:"bottom-spacer"},null,-1))],2)])],32)]))}}),[["__scopeId","data-v-b211c09f"]]);export{_s as default};
