import{d as a,i as s,c as t,a as d,F as e,s as i,q as v,t as c,p as l,b as n,e as p,w as r,r as o,o as m}from"./vendor-DhoxJlSg.js";import{_ as h}from"./index-BWTdsPe5.js";const u={class:"comparison-view"},b={class:"container"},f={class:"comparison-tabs"},y=["onClick"],g={class:"comparison-content"},w={key:0,class:"comparison-section"},k={key:1,class:"comparison-section"},S={class:"demo-frame"},T={class:"demo-preview"},V={class:"preview-content"},C={class:"preview-grid"},L={key:2,class:"comparison-section"},_={key:3,class:"comparison-section"},H=h(a({__name:"ComparisonView",setup(a){const h=s("original"),H=[{key:"original",label:"原始版本"},{key:"enhanced",label:"增强版本"},{key:"technical",label:"技术对比"},{key:"performance",label:"性能对比"}];return(a,s)=>{const M=o("router-link");return m(),t("div",u,[d("div",b,[s[9]||(s[9]=d("header",{class:"comparison-header"},[d("h1",null,"代码优化对比演示"),d("p",null,"展示原HTML代码与Vue组件化改造后的效果对比")],-1)),d("div",f,[(m(),t(e,null,i(H,a=>d("button",{key:a.key,class:v(["tab-button",{active:h.value===a.key}]),onClick:s=>h.value=a.key},c(a.label),11,y)),64))]),d("div",g,["original"===h.value?(m(),t("div",w,s[0]||(s[0]=[n('<div class="section-header" data-v-51915077><h2 data-v-51915077>🔧 原始HTML版本</h2><div class="feature-tags" data-v-51915077><span class="tag static" data-v-51915077>静态页面</span><span class="tag limited" data-v-51915077>功能有限</span><span class="tag isolated" data-v-51915077>数据孤立</span></div></div><div class="demo-frame" data-v-51915077><iframe src="/original-demo.html" title="原始HTML演示" class="demo-iframe" data-v-51915077></iframe></div><div class="feature-list" data-v-51915077><h3 data-v-51915077>特性分析</h3><ul data-v-51915077><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>❌</span><span data-v-51915077>静态数据，无法与数据库交互</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>❌</span><span data-v-51915077>假收藏功能，无用户系统</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>❌</span><span data-v-51915077>简单搜索，无高级过滤</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>❌</span><span data-v-51915077>硬编码内容，难以维护</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>✅</span><span data-v-51915077>视觉设计优秀，用户体验良好</span></li></ul></div>',3)]))):l("",!0),"enhanced"===h.value?(m(),t("div",k,[s[5]||(s[5]=n('<div class="section-header" data-v-51915077><h2 data-v-51915077>🚀 Vue增强版本</h2><div class="feature-tags" data-v-51915077><span class="tag dynamic" data-v-51915077>动态数据</span><span class="tag integrated" data-v-51915077>数据库集成</span><span class="tag modern" data-v-51915077>现代架构</span></div></div>',1)),d("div",S,[p(M,{to:"/enhanced",class:"demo-link"},{default:r(()=>[d("div",T,[s[3]||(s[3]=d("div",{class:"preview-header"},[d("div",{class:"preview-logo"},"🚀"),d("h3",null,"高效工具导航站")],-1)),d("div",V,[s[2]||(s[2]=d("div",{class:"preview-search"},[d("input",{placeholder:"搜索工具、分类或功能...",readonly:""}),d("button",null,"搜索")],-1)),d("div",C,[(m(),t(e,null,i(6,a=>d("div",{key:a,class:"preview-card"},s[1]||(s[1]=[d("div",{class:"card-header"},null,-1),d("div",{class:"card-content"},null,-1)]))),64))])]),s[4]||(s[4]=d("div",{class:"preview-overlay"},[d("span",null,"点击查看完整版本")],-1))])]),_:1})]),s[6]||(s[6]=n('<div class="feature-list" data-v-51915077><h3 data-v-51915077>增强特性</h3><ul data-v-51915077><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>✅</span><span data-v-51915077>Supabase数据库集成，实时数据同步</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>✅</span><span data-v-51915077>用户认证系统，真实收藏功能</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>✅</span><span data-v-51915077>高级搜索和过滤，智能分类</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>✅</span><span data-v-51915077>点击统计，数据分析支持</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>✅</span><span data-v-51915077>组件化架构，易于维护扩展</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>✅</span><span data-v-51915077>TypeScript类型安全</span></li><li class="feature-item" data-v-51915077><span class="icon" data-v-51915077>✅</span><span data-v-51915077>响应式设计，移动端优化</span></li></ul></div>',1))])):l("",!0),"technical"===h.value?(m(),t("div",L,s[7]||(s[7]=[n('<div class="section-header" data-v-51915077><h2 data-v-51915077>⚙️ 技术架构对比</h2></div><div class="tech-comparison" data-v-51915077><div class="tech-column" data-v-51915077><h3 data-v-51915077>原始HTML</h3><div class="tech-stack" data-v-51915077><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>HTML5</span><span class="tech-desc" data-v-51915077>静态标记</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>CSS3</span><span class="tech-desc" data-v-51915077>样式定义</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>Vanilla JS</span><span class="tech-desc" data-v-51915077>基础交互</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>Font Awesome</span><span class="tech-desc" data-v-51915077>图标库</span></div></div></div><div class="tech-arrow" data-v-51915077>→</div><div class="tech-column" data-v-51915077><h3 data-v-51915077>Vue增强版</h3><div class="tech-stack" data-v-51915077><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>Vue 3</span><span class="tech-desc" data-v-51915077>组合式API</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>TypeScript</span><span class="tech-desc" data-v-51915077>类型安全</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>Pinia</span><span class="tech-desc" data-v-51915077>状态管理</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>Supabase</span><span class="tech-desc" data-v-51915077>数据库+认证</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>Tailwind CSS</span><span class="tech-desc" data-v-51915077>原子化CSS</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>Lucide Icons</span><span class="tech-desc" data-v-51915077>现代图标</span></div><div class="tech-item" data-v-51915077><span class="tech-name" data-v-51915077>Vite</span><span class="tech-desc" data-v-51915077>构建工具</span></div></div></div></div>',2)]))):l("",!0),"performance"===h.value?(m(),t("div",_,s[8]||(s[8]=[n('<div class="section-header" data-v-51915077><h2 data-v-51915077>📊 性能与维护性对比</h2></div><div class="performance-metrics" data-v-51915077><div class="metric-group" data-v-51915077><h3 data-v-51915077>开发效率</h3><div class="metric-bars" data-v-51915077><div class="metric-bar" data-v-51915077><span class="metric-label" data-v-51915077>代码复用性</span><div class="bar-container" data-v-51915077><div class="bar original" style="width:20%;" data-v-51915077></div><span class="bar-value" data-v-51915077>20%</span></div></div><div class="metric-bar" data-v-51915077><span class="metric-label" data-v-51915077>Vue组件化</span><div class="bar-container" data-v-51915077><div class="bar enhanced" style="width:90%;" data-v-51915077></div><span class="bar-value" data-v-51915077>90%</span></div></div></div></div><div class="metric-group" data-v-51915077><h3 data-v-51915077>可维护性</h3><div class="metric-bars" data-v-51915077><div class="metric-bar" data-v-51915077><span class="metric-label" data-v-51915077>代码组织</span><div class="bar-container" data-v-51915077><div class="bar original" style="width:30%;" data-v-51915077></div><span class="bar-value" data-v-51915077>30%</span></div></div><div class="metric-bar" data-v-51915077><span class="metric-label" data-v-51915077>模块化架构</span><div class="bar-container" data-v-51915077><div class="bar enhanced" style="width:95%;" data-v-51915077></div><span class="bar-value" data-v-51915077>95%</span></div></div></div></div><div class="metric-group" data-v-51915077><h3 data-v-51915077>功能完整性</h3><div class="metric-bars" data-v-51915077><div class="metric-bar" data-v-51915077><span class="metric-label" data-v-51915077>静态展示</span><div class="bar-container" data-v-51915077><div class="bar original" style="width:60%;" data-v-51915077></div><span class="bar-value" data-v-51915077>60%</span></div></div><div class="metric-bar" data-v-51915077><span class="metric-label" data-v-51915077>完整应用</span><div class="bar-container" data-v-51915077><div class="bar enhanced" style="width:100%;" data-v-51915077></div><span class="bar-value" data-v-51915077>100%</span></div></div></div></div></div>',2)]))):l("",!0)])])])}}}),[["__scopeId","data-v-51915077"]]);export{H as default};
