import{u as e,_ as t}from"./index-BKFWLFbU.js";import{d as a,r as s,c as l,o as n,m as r,q as i,F as d,z as o,H as c,N as u,C as p,t as v,y as m,u as y,aw as g,a1 as k,a0 as f,G as h}from"./vendor-CSeT1gXp.js";const b={class:"orders-view"},w={class:"orders-filters"},C=["onClick"],I={class:"filter-count"},S={class:"orders-content"},D={key:0,class:"loading-state"},A={key:1,class:"orders-list"},O={class:"order-header"},_={class:"order-info"},P={class:"order-number"},j={class:"order-date"},q={class:"order-status"},N={class:"order-items"},x={class:"item-image"},z=["src","alt"],Y={class:"item-info"},F={class:"item-name"},G={class:"item-description"},H={class:"item-meta"},L={class:"item-quantity"},M={class:"item-price"},T={class:"item-total"},U={class:"order-footer"},V={class:"order-total"},$={class:"total-amount"},B={class:"order-actions"},E=["onClick"],J=["onClick"],K=["onClick"],Q=["onClick"],R={key:2,class:"empty-state"},W=t(a({__name:"OrdersView",setup(t){const a=e(),W=s(!0),X=s("all"),Z=s([]),ee=[{key:"all",label:"全部订单"},{key:"pending",label:"待支付"},{key:"paid",label:"已支付"},{key:"cancelled",label:"已取消"},{key:"refunded",label:"已退款"}],te=l(()=>"all"===X.value?Z.value:Z.value.filter(e=>e.status===X.value));return n(()=>{(async()=>{try{W.value=!0,await new Promise(e=>setTimeout(e,1e3)),Z.value=[{id:"order-1",userId:"user-1",items:[{id:"item-1",orderId:"order-1",productId:"product-1",quantity:1,unitPrice:299,totalPrice:299,createdAt:(new Date).toISOString(),product:{id:"product-1",name:"高效办公套件",shortDescription:"提升办公效率的完整解决方案",images:["/placeholder.jpg"]}}],totalAmount:299,currency:"CNY",status:"paid",paymentMethod:"alipay",createdAt:new Date(Date.now()-864e5).toISOString(),updatedAt:(new Date).toISOString(),completedAt:(new Date).toISOString()},{id:"order-2",userId:"user-1",items:[{id:"item-2",orderId:"order-2",productId:"product-2",quantity:1,unitPrice:199,totalPrice:199,createdAt:(new Date).toISOString(),product:{id:"product-2",name:"设计师工具包",shortDescription:"专业设计师必备工具集合",images:["/placeholder.jpg"]}}],totalAmount:199,currency:"CNY",status:"pending",createdAt:new Date(Date.now()-36e5).toISOString(),updatedAt:(new Date).toISOString()}]}catch(e){console.error("加载订单失败:",e)}finally{W.value=!1}})()}),(e,t)=>{const s=f("router-link");return h(),r("div",b,[t[5]||(t[5]=i("div",{class:"orders-header"},[i("h1",null,"我的订单"),i("p",null,"查看和管理您的订单历史")],-1)),i("div",w,[(h(),r(d,null,o(ee,e=>{return i("button",{key:e.key,class:c(["filter-btn",{active:X.value===e.key}]),onClick:t=>X.value=e.key},[u(p(e.label)+" ",1),i("span",I,p((t=e.key,"all"===t?Z.value.length:Z.value.filter(e=>e.status===t).length)),1)],10,C);var t}),64))]),i("div",S,[W.value?(h(),r("div",D,t[0]||(t[0]=[i("div",{class:"loading-spinner"},null,-1),i("p",null,"正在加载订单...",-1)]))):te.value.length>0?(h(),r("div",A,[(h(!0),r(d,null,o(te.value,e=>{return h(),r("div",{key:e.id,class:"order-item"},[i("div",O,[i("div",_,[i("h3",P," 订单号: "+p(e.id.slice(-8).toUpperCase()),1),i("p",j," 下单时间: "+p((l=e.createdAt,new Date(l).toLocaleString("zh-CN"))),1)]),i("div",q,[i("span",{class:c(["status-badge",e.status])},p((s=e.status,{pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[s]||s)),3)])]),i("div",N,[(h(!0),r(d,null,o(e.items,e=>{var t,a,s,l,n;return h(),r("div",{key:e.id,class:"order-item-detail"},[i("div",x,[i("img",{src:(null==(a=null==(t=e.product)?void 0:t.images)?void 0:a[0])||"/placeholder.jpg",alt:null==(s=e.product)?void 0:s.name},null,8,z)]),i("div",Y,[i("h4",F,p(null==(l=e.product)?void 0:l.name),1),i("p",G,p(null==(n=e.product)?void 0:n.shortDescription),1),i("div",H,[i("span",L,"数量: "+p(e.quantity),1),i("span",M,"单价: ¥"+p(e.unitPrice),1)])]),i("div",T,"¥"+p(e.totalPrice),1)])}),128))]),i("div",U,[i("div",V,[t[1]||(t[1]=i("span",{class:"total-label"},"订单总额:",-1)),i("span",$,"¥"+p(e.totalAmount),1)]),i("div",B,["pending"===e.status?(h(),r("button",{key:0,class:"action-btn primary",onClick:t=>(e=>{a.push(`/payment?order=${e.id}`)})(e)}," 立即支付 ",8,E)):v("",!0),"pending"===e.status?(h(),r("button",{key:1,class:"action-btn secondary",onClick:t=>(async e=>{if(confirm("确定要取消这个订单吗？"))try{const t=Z.value.findIndex(t=>t.id===e.id);-1!==t&&(Z.value[t].status="cancelled")}catch(t){console.error("取消订单失败:",t)}})(e)}," 取消订单 ",8,J)):v("",!0),"paid"===e.status?(h(),r("button",{key:2,class:"action-btn secondary",onClick:t=>(e=>{console.log("下载订单产品:",e.id)})(e)},[m(y(g),{class:"icon"}),t[2]||(t[2]=u(" 下载产品 "))],8,K)):v("",!0),i("button",{class:"action-btn secondary",onClick:t=>(e=>{console.log("查看订单详情:",e.id)})(e)}," 查看详情 ",8,Q)])])]);var s,l}),128))])):(h(),r("div",R,[t[4]||(t[4]=i("div",{class:"empty-icon"},"📦",-1)),i("h3",null,p({all:"暂无订单",pending:"暂无待支付订单",paid:"暂无已支付订单",cancelled:"暂无已取消订单",refunded:"暂无已退款订单"}[X.value]||"暂无订单"),1),i("p",null,p({all:"您还没有任何订单，去购买一些产品吧！",pending:"您没有待支付的订单",paid:"您没有已支付的订单",cancelled:"您没有已取消的订单",refunded:"您没有已退款的订单"}[X.value]||"暂无相关订单"),1),m(s,{to:"/products",class:"empty-action"},{default:k(()=>t[3]||(t[3]=[u("去购买产品")])),_:1,__:[3]})]))])])}}}),[["__scopeId","data-v-512cfe8f"]]);export{W as default};
