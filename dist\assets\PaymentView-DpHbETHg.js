import{d as a,i as e,m as l,j as s,c as i,a as t,p as n,F as o,s as d,t as u,q as c,x as r,aA as m,e as p,u as v,aB as y,z as f,y as h,o as g}from"./vendor-DhoxJlSg.js";import{c as b,u as q,_ as w}from"./index-Dhbj_B_-.js";const k={class:"payment-view"},V={class:"payment-container"},N={class:"payment-content"},U={class:"order-summary"},x={class:"order-items"},_={class:"item-image"},j=["src","alt"],C={class:"item-info"},P={class:"item-name"},M={class:"item-description"},z={class:"item-meta"},A={class:"item-quantity"},B={class:"item-price"},E={class:"order-total"},F={class:"total-row"},I={class:"total-row"},J={class:"discount"},S={class:"total-row final"},T={class:"final-amount"},$={class:"payment-methods"},D={class:"methods-list"},G=["value"],H={class:"method-content"},K={class:"method-icon"},L=["src","alt"],O={class:"method-info"},Q={class:"method-name"},R={class:"method-description"},W={class:"method-check"},X={class:"billing-address"},Y={class:"address-form"},Z={class:"form-row"},aa={class:"form-group"},ea={class:"form-group"},la={class:"form-row"},sa={class:"form-group"},ia={class:"form-group"},ta={class:"form-group"},na={class:"payment-actions"},oa=["disabled"],da={key:0,class:"loading-spinner"},ua={key:0,class:"error-message"},ca=w(a({__name:"PaymentView",setup(a){const w=q(),ca=b(),ra=e(!1),ma=e(null),pa=e(""),va=e([{id:"1",name:"高效办公套件",description:"提升办公效率的完整解决方案",quantity:1,price:299,image:"/placeholder.jpg"}]),ya=e({fullName:"",email:"",country:"",city:"",address:""}),fa=[{id:"alipay",name:"支付宝",description:"使用支付宝安全快捷支付",icon:"/payment-alipay.png"},{id:"wechat",name:"微信支付",description:"使用微信支付便捷支付",icon:"/payment-wechat.png"},{id:"stripe",name:"信用卡",description:"支持 Visa、MasterCard 等",icon:"/payment-stripe.png"}],ha=l(()=>va.value.reduce((a,e)=>a+e.price*e.quantity,0)),ga=l(()=>0),ba=l(()=>ha.value-ga.value),qa=l(()=>pa.value&&ya.value.fullName&&ya.value.email&&ya.value.country&&ya.value.city&&ya.value.address),wa=async()=>{try{ra.value=!0,ma.value=null,await new Promise(a=>setTimeout(a,2e3)),w.push("/payment/success")}catch(a){ma.value=a instanceof Error?a.message:"支付失败，请重试"}finally{ra.value=!1}},ka=()=>{w.go(-1)};return s(()=>{(()=>{const a=ca.query.product,e=ca.query.order;a?console.log("从产品创建订单:",a):e&&console.log("加载现有订单:",e)})(),fa.length>0&&(pa.value=fa[0].id)}),(a,e)=>(g(),i("div",k,[t("div",V,[e[18]||(e[18]=t("div",{class:"payment-header"},[t("h1",null,"支付订单"),t("p",null,"请选择支付方式完成购买")],-1)),t("div",N,[t("div",U,[e[9]||(e[9]=t("h3",null,"订单信息",-1)),t("div",x,[(g(!0),i(o,null,d(va.value,a=>(g(),i("div",{key:a.id,class:"order-item"},[t("div",_,[t("img",{src:a.image,alt:a.name},null,8,j)]),t("div",C,[t("h4",P,u(a.name),1),t("p",M,u(a.description),1),t("div",z,[t("span",A,"数量: "+u(a.quantity),1),t("span",B,"¥"+u(a.price),1)])])]))),128))]),t("div",E,[t("div",F,[e[6]||(e[6]=t("span",null,"商品总额",-1)),t("span",null,"¥"+u(ha.value),1)]),t("div",I,[e[7]||(e[7]=t("span",null,"优惠折扣",-1)),t("span",J,"-¥"+u(ga.value),1)]),t("div",S,[e[8]||(e[8]=t("span",null,"应付金额",-1)),t("span",T,"¥"+u(ba.value),1)])])]),t("div",$,[e[10]||(e[10]=t("h3",null,"选择支付方式",-1)),t("div",D,[(g(),i(o,null,d(fa,a=>t("label",{key:a.id,class:c(["method-option",{selected:pa.value===a.id}])},[r(t("input",{"onUpdate:modelValue":e[0]||(e[0]=a=>pa.value=a),type:"radio",value:a.id,name:"paymentMethod"},null,8,G),[[m,pa.value]]),t("div",H,[t("div",K,[t("img",{src:a.icon,alt:a.name},null,8,L)]),t("div",O,[t("div",Q,u(a.name),1),t("div",R,u(a.description),1)]),t("div",W,[p(v(y),{class:"check-icon"})])])],2)),64))])]),t("div",X,[e[17]||(e[17]=t("h3",null,"账单地址",-1)),t("form",Y,[t("div",Z,[t("div",aa,[e[11]||(e[11]=t("label",{for:"fullName"},"姓名 *",-1)),r(t("input",{id:"fullName","onUpdate:modelValue":e[1]||(e[1]=a=>ya.value.fullName=a),type:"text",required:"",placeholder:"请输入姓名"},null,512),[[f,ya.value.fullName]])]),t("div",ea,[e[12]||(e[12]=t("label",{for:"email"},"邮箱 *",-1)),r(t("input",{id:"email","onUpdate:modelValue":e[2]||(e[2]=a=>ya.value.email=a),type:"email",required:"",placeholder:"请输入邮箱地址"},null,512),[[f,ya.value.email]])])]),t("div",la,[t("div",sa,[e[14]||(e[14]=t("label",{for:"country"},"国家/地区 *",-1)),r(t("select",{id:"country","onUpdate:modelValue":e[3]||(e[3]=a=>ya.value.country=a),required:""},e[13]||(e[13]=[t("option",{value:""},"请选择国家/地区",-1),t("option",{value:"CN"},"中国",-1),t("option",{value:"US"},"美国",-1),t("option",{value:"JP"},"日本",-1)]),512),[[h,ya.value.country]])]),t("div",ia,[e[15]||(e[15]=t("label",{for:"city"},"城市 *",-1)),r(t("input",{id:"city","onUpdate:modelValue":e[4]||(e[4]=a=>ya.value.city=a),type:"text",required:"",placeholder:"请输入城市"},null,512),[[f,ya.value.city]])])]),t("div",ta,[e[16]||(e[16]=t("label",{for:"address"},"详细地址 *",-1)),r(t("input",{id:"address","onUpdate:modelValue":e[5]||(e[5]=a=>ya.value.address=a),type:"text",required:"",placeholder:"请输入详细地址"},null,512),[[f,ya.value.address]])])])]),t("div",na,[t("button",{class:"cancel-btn",onClick:ka},"取消订单"),t("button",{class:"pay-btn",disabled:!qa.value||ra.value,onClick:wa},[ra.value?(g(),i("div",da)):n("",!0),t("span",null,u(ra.value?"处理中...":`立即支付 ¥${ba.value}`),1)],8,oa)]),ma.value?(g(),i("div",ua,u(ma.value),1)):n("",!0)])])]))}}),[["__scopeId","data-v-0887093f"]]);export{ca as default};
