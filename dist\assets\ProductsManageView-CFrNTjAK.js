import{d as a,i as s,m as e,j as l,c,a as i,p as t,e as o,h as d,u as n,aa as v,x as r,W as u,z as p,y as m,b as g,af as b,F as h,s as f,t as y,q as k,aq as C,ar as w,X as x,v as P,o as _}from"./vendor-DhoxJlSg.js";import{_ as j}from"./index-BLSkTtYG.js";const L={class:"products-manage-view"},V={class:"page-header"},q={class:"filters"},U={class:"search-box"},z={class:"products-table"},F={key:0,class:"loading-state"},I={key:1,class:"empty-state"},M={key:2,class:"table-body"},T={class:"col-image"},W=["src","alt"],A={class:"col-name"},B={class:"product-name"},D={class:"product-description"},E={class:"col-category"},G={class:"category-tag"},H={class:"col-price"},J={class:"price"},K={key:0,class:"original-price"},N={class:"col-status"},O={class:"col-actions"},Q=["onClick"],R=["onClick"],S={class:"modal-header"},X={class:"modal-footer"},Y={class:"btn primary"},Z=j(a({__name:"ProductsManageView",setup(a){const j=s(!0),Z=s(""),$=s(""),aa=s(!1),sa=s(!1),ea=s(null),la=s([{id:1,name:"高效办公套件",description:"提升办公效率的完整解决方案",price:299,originalPrice:399,image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=100&h=100&fit=crop",category:"办公工具",status:"active"},{id:2,name:"设计师工具包",description:"专业设计师必备工具集合",price:199,image:"https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=100&h=100&fit=crop",category:"设计工具",status:"active"},{id:3,name:"开发者助手",description:"程序员开发必备工具",price:399,originalPrice:499,image:"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=100&h=100&fit=crop",category:"开发工具",status:"draft"}]),ca=e(()=>{let a=la.value;if(Z.value.trim()){const s=Z.value.toLowerCase();a=a.filter(a=>a.name.toLowerCase().includes(s)||a.description.toLowerCase().includes(s))}return $.value&&(a=a.filter(a=>a.status===$.value)),a}),ia=()=>{aa.value=!1,sa.value=!1,ea.value=null};return l(()=>{setTimeout(()=>{j.value=!1},1e3)}),(a,s)=>(_(),c("div",L,[i("div",V,[s[5]||(s[5]=i("h2",{class:"page-title"},"产品管理",-1)),i("button",{class:"add-btn",onClick:s[0]||(s[0]=a=>aa.value=!0)},[o(n(v),{class:"icon"}),s[4]||(s[4]=d(" 添加产品 "))])]),i("div",q,[i("div",U,[o(n(u),{class:"search-icon"}),r(i("input",{"onUpdate:modelValue":s[1]||(s[1]=a=>Z.value=a),type:"text",placeholder:"搜索产品...",class:"search-input"},null,512),[[p,Z.value]])]),r(i("select",{"onUpdate:modelValue":s[2]||(s[2]=a=>$.value=a),class:"status-filter"},s[6]||(s[6]=[i("option",{value:""},"全部状态",-1),i("option",{value:"active"},"已发布",-1),i("option",{value:"draft"},"草稿",-1),i("option",{value:"inactive"},"已下架",-1)]),512),[[m,$.value]])]),i("div",z,[s[10]||(s[10]=g('<div class="table-header" data-v-906c6801><div class="col-image" data-v-906c6801>图片</div><div class="col-name" data-v-906c6801>产品名称</div><div class="col-category" data-v-906c6801>分类</div><div class="col-price" data-v-906c6801>价格</div><div class="col-status" data-v-906c6801>状态</div><div class="col-actions" data-v-906c6801>操作</div></div>',1)),j.value?(_(),c("div",F,s[7]||(s[7]=[i("div",{class:"loading-spinner"},null,-1),i("p",null,"加载中...",-1)]))):0===ca.value.length?(_(),c("div",I,[o(n(b),{class:"empty-icon"}),s[8]||(s[8]=i("h3",null,"暂无产品",-1)),s[9]||(s[9]=i("p",null,"还没有添加任何产品",-1))])):(_(),c("div",M,[(_(!0),c(h,null,f(ca.value,a=>{return _(),c("div",{key:a.id,class:"table-row"},[i("div",T,[i("img",{src:a.image,alt:a.name,class:"product-image"},null,8,W)]),i("div",A,[i("div",B,y(a.name),1),i("div",D,y(a.description),1)]),i("div",E,[i("span",G,y(a.category),1)]),i("div",H,[i("div",J,"¥"+y(a.price),1),a.originalPrice?(_(),c("div",K," ¥"+y(a.originalPrice),1)):t("",!0)]),i("div",N,[i("span",{class:k(["status-badge",a.status])},y((s=a.status,{active:"已发布",draft:"草稿",inactive:"已下架"}[s]||s)),3)]),i("div",O,[i("button",{class:"action-btn edit",onClick:s=>(a=>{ea.value=a,sa.value=!0})(a)},[o(n(C),{class:"icon"})],8,Q),i("button",{class:"action-btn delete",onClick:s=>{return e=a.id,void(confirm("确定要删除这个产品吗？")&&(la.value=la.value.filter(a=>a.id!==e),console.log("删除产品:",e)));var e}},[o(n(w),{class:"icon"})],8,R)])]);var s}),128))]))]),aa.value||sa.value?(_(),c("div",{key:0,class:"modal-overlay",onClick:ia},[i("div",{class:"modal-content",onClick:s[3]||(s[3]=P(()=>{},["stop"]))},[i("div",S,[i("h3",null,y(aa.value?"添加产品":"编辑产品"),1),i("button",{class:"close-btn",onClick:ia},[o(n(x),{class:"icon"})])]),s[11]||(s[11]=i("div",{class:"modal-body"},[i("p",null,"产品管理功能正在开发中..."),i("p",null,"将包含完整的产品添加、编辑、删除功能")],-1)),i("div",X,[i("button",{class:"btn secondary",onClick:ia},"取消"),i("button",Y,y(aa.value?"添加":"保存"),1)])])])):t("",!0)]))}}),[["__scopeId","data-v-906c6801"]]);export{Z as default};
