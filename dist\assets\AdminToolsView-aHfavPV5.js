import{s as e,a as t,_ as a}from"./index-Dhbj_B_-.js";import{u as s}from"./categories-qunZ0Aqu.js";import{d as i,i as o,m as c,j as r,c as d,a as l,p as n,t as u,q as _,u as v,F as m,s as p,o as b}from"./vendor-DhoxJlSg.js";const f=[{id:"850e8400-e29b-41d4-a716-446655440013",name:"Vercel",description:"现代化的前端部署平台，支持静态网站和Serverless函数",url:"https://vercel.com",icon:"⚡",category_id:"************************************",is_featured:!0,click_count:128,sort_order:13,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440014",name:"Netlify",description:"静态网站托管和部署平台，支持持续集成和CDN加速",url:"https://netlify.com",icon:"🌐",category_id:"************************************",is_featured:!1,click_count:95,sort_order:14,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440015",name:"Docker",description:"容器化平台，简化应用程序的打包、分发和部署",url:"https://docker.com",icon:"🐳",category_id:"************************************",is_featured:!1,click_count:87,sort_order:15,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440016",name:"Stack Overflow",description:"程序员问答社区，解决编程问题的最佳平台",url:"https://stackoverflow.com",icon:"📚",category_id:"************************************",is_featured:!0,click_count:203,sort_order:16,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440017",name:"Sketch",description:"Mac平台专业的UI/UX设计工具，矢量图形设计的首选",url:"https://sketch.com",icon:"💎",category_id:"550e8400-e29b-41d4-a716-446655440002",is_featured:!1,click_count:78,sort_order:17,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440018",name:"Adobe XD",description:"Adobe的UI/UX设计和原型制作工具，支持协作和交互设计",url:"https://www.adobe.com/products/xd.html",icon:"🎨",category_id:"550e8400-e29b-41d4-a716-446655440002",is_featured:!1,click_count:56,sort_order:18,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440019",name:"Slack",description:"团队协作和即时通讯平台，提高团队沟通效率",url:"https://slack.com",icon:"💬",category_id:"550e8400-e29b-41d4-a716-446655440003",is_featured:!0,click_count:142,sort_order:19,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440020",name:"Zoom",description:"视频会议和在线协作平台，远程办公的必备工具",url:"https://zoom.us",icon:"📹",category_id:"550e8400-e29b-41d4-a716-446655440003",is_featured:!0,click_count:198,sort_order:20,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440021",name:"Google Drive",description:"云存储和文档协作平台，随时随地访问和编辑文件",url:"https://drive.google.com",icon:"☁️",category_id:"550e8400-e29b-41d4-a716-446655440003",is_featured:!1,click_count:176,sort_order:21,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440022",name:"Dropbox",description:"云存储和文件同步服务，安全可靠的文件备份解决方案",url:"https://dropbox.com",icon:"📦",category_id:"550e8400-e29b-41d4-a716-446655440003",is_featured:!1,click_count:89,sort_order:22,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440023",name:"Claude",description:"Anthropic开发的AI助手，擅长分析、写作和复杂推理任务",url:"https://claude.ai",icon:"🤖",category_id:"550e8400-e29b-41d4-a716-446655440004",is_featured:!0,click_count:156,sort_order:23,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440024",name:"Stable Diffusion",description:"开源的AI图像生成模型，支持本地部署和自定义训练",url:"https://stability.ai",icon:"🎭",category_id:"550e8400-e29b-41d4-a716-446655440004",is_featured:!1,click_count:87,sort_order:24,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440025",name:"GitHub Copilot",description:"AI代码助手，基于机器学习提供智能代码补全和建议",url:"https://github.com/features/copilot",icon:"🚁",category_id:"550e8400-e29b-41d4-a716-446655440004",is_featured:!0,click_count:134,sort_order:25,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440026",name:"Cloudflare",description:"全球CDN和网络安全服务，提供网站加速和DDoS防护",url:"https://cloudflare.com",icon:"☁️",category_id:"550e8400-e29b-41d4-a716-446655440005",is_featured:!1,click_count:92,sort_order:26,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440027",name:"YouTube",description:"全球最大的视频分享平台，观看和上传各类视频内容",url:"https://youtube.com",icon:"📺",category_id:"550e8400-e29b-41d4-a716-446655440006",is_featured:!0,click_count:456,sort_order:27,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440028",name:"TikTok",description:"短视频社交平台，创作和分享有趣的短视频内容",url:"https://tiktok.com",icon:"🎵",category_id:"550e8400-e29b-41d4-a716-446655440006",is_featured:!0,click_count:298,sort_order:28,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440029",name:"Spotify",description:"音乐流媒体平台，收听和发现全球音乐内容",url:"https://spotify.com",icon:"🎵",category_id:"550e8400-e29b-41d4-a716-446655440006",is_featured:!1,click_count:187,sort_order:29,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440030",name:"Google Translate",description:"谷歌翻译，支持100多种语言的在线翻译服务",url:"https://translate.google.com",icon:"🌍",category_id:"550e8400-e29b-41d4-a716-446655440007",is_featured:!0,click_count:234,sort_order:30,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440031",name:"1Password",description:"密码管理器，安全存储和管理所有账户密码",url:"https://1password.com",icon:"🔐",category_id:"550e8400-e29b-41d4-a716-446655440007",is_featured:!1,click_count:78,sort_order:31,status:"active"},{id:"850e8400-e29b-41d4-a716-446655440032",name:"PayPal",description:"在线支付平台，安全便捷的国际支付解决方案",url:"https://paypal.com",icon:"💳",category_id:"550e8400-e29b-41d4-a716-446655440007",is_featured:!1,click_count:156,sort_order:32,status:"active"}];const g={class:"admin-tools-view"},y={class:"admin-actions"},h=["disabled"],k={class:"tools-summary"},w={class:"stats-grid"},D={class:"stat-card"},A={class:"stat-number"},C={class:"stat-card"},S={class:"stat-number"},T={class:"stat-card"},z={class:"stat-number"},I={class:"tools-preview"},x={class:"tools-grid"},j={class:"tool-icon"},U={class:"tool-info"},G={class:"tool-meta"},N={class:"category"},P={class:"clicks"},X=a(i({__name:"AdminToolsView",setup(a){const i=t(),X=s(),F=o(!1),V=o(""),q=o("success"),E=c(()=>i.tools.filter(e=>e.isFeatured).length),H=c(()=>i.tools.slice().sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()).slice(0,6)),M=async()=>{try{F.value=!0,V.value="",await async function(){try{console.log("开始添加新工具...");const{data:t,error:a}=await e.from("tools").insert(f).select();if(a)throw console.error("添加工具失败:",a),a;return console.log(`成功添加 ${(null==t?void 0:t.length)||0} 个工具`),t}catch(t){throw console.error("添加工具时出错:",t),t}}(),await i.initialize(),V.value="成功添加新工具！",q.value="success"}catch(t){console.error("添加工具失败:",t),V.value="添加工具失败: "+(t instanceof Error?t.message:"未知错误"),q.value="error"}finally{F.value=!1}},O=async()=>{try{await i.initialize(),await X.initialize(),V.value="工具列表已刷新",q.value="success"}catch(e){V.value="刷新失败",q.value="error"}};return r(async()=>{await i.initialize(),await X.initialize()}),(e,t)=>(b(),d("div",g,[t[5]||(t[5]=l("div",{class:"admin-header"},[l("h1",null,"管理工具"),l("p",null,"添加和管理工具数据")],-1)),l("div",y,[l("button",{class:"action-button primary",disabled:F.value,onClick:M},u(F.value?"添加中...":"添加新工具"),9,h),l("button",{class:"action-button secondary",onClick:O}," 刷新工具列表 ")]),V.value?(b(),d("div",{key:0,class:_(["message",q.value])},u(V.value),3)):n("",!0),l("div",k,[t[3]||(t[3]=l("h2",null,"当前工具统计",-1)),l("div",w,[l("div",D,[l("div",A,u(v(i).tools.length),1),t[0]||(t[0]=l("div",{class:"stat-label"},"总工具数",-1))]),l("div",C,[l("div",S,u(E.value),1),t[1]||(t[1]=l("div",{class:"stat-label"},"推荐工具",-1))]),l("div",T,[l("div",z,u(v(X).categories.length),1),t[2]||(t[2]=l("div",{class:"stat-label"},"分类数",-1))])])]),l("div",I,[t[4]||(t[4]=l("h2",null,"最新添加的工具",-1)),l("div",x,[(b(!0),d(m,null,p(H.value,e=>{var t;return b(),d("div",{key:e.id,class:"tool-card"},[l("div",j,u(e.icon),1),l("div",U,[l("h3",null,u(e.name),1),l("p",null,u(e.description),1),l("div",G,[l("span",N,u(null==(t=e.category)?void 0:t.name),1),l("span",P,u(e.clickCount)+" 次点击",1)])])])}),128))])])]))}}),[["__scopeId","data-v-421c69fc"]]);export{X as default};
