import{d as e,r as l,o as s,Z as n,m as t,q as a,C as o,G as i}from"./vendor-CSeT1gXp.js";import{_ as r}from"./index-BKFWLFbU.js";const d={class:"test-page"},u={class:"test-content"},v={class:"test-info"},c=r(e({__name:"TestStatusBar",setup(e){const r=l(0),c=l(0),m=l(0),p=()=>{r.value=document.documentElement.scrollHeight,c.value=window.innerHeight,m.value=window.scrollY};return s(()=>{p(),window.addEventListener("scroll",p),window.addEventListener("resize",p)}),n(()=>{window.removeEventListener("scroll",p),window.removeEventListener("resize",p)}),(e,l)=>(i(),t("div",d,[a("div",u,[l[2]||(l[2]=a("h1",null,"状态栏测试页面",-1)),l[3]||(l[3]=a("p",null,"这个页面用于测试底部状态栏是否正常显示",-1)),a("div",v,[l[1]||(l[1]=a("h2",null,"测试信息",-1)),a("ul",null,[a("li",null,"页面高度: "+o(r.value)+"px",1),a("li",null,"视口高度: "+o(c.value)+"px",1),a("li",null,"滚动位置: "+o(m.value)+"px",1),l[0]||(l[0]=a("li",null,"状态栏应该显示在底部",-1))])]),l[4]||(l[4]=a("div",{class:"spacer"},null,-1)),l[5]||(l[5]=a("div",{class:"bottom-content"},[a("p",null,"这是页面底部内容，状态栏应该在这下面显示")],-1))])]))}}),[["__scopeId","data-v-7756201b"]]);export{c as default};
