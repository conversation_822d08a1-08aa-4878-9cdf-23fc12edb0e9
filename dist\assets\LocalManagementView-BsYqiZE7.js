import{d as a,i as l,m as s,j as e,k as t,c as n,a as o,p as c,q as i,u as d,t as u,e as r,C as v,H as p,h as m,as as g,at as b,au as f,ae as h,a0 as y,am as k,aa as C,F as w,s as x,aq as S,ar as V,x as L,y as M,A as U,ag as D,av as I,D as _,X as B,z as F,v as j,aw as z,o as T}from"./vendor-DhoxJlSg.js";import{d as E,_ as O}from"./index-BWTdsPe5.js";const P={class:"local-management-view"},q={class:"page-header"},N={class:"header-content"},A={class:"status-indicators"},G={key:0,class:"status-item pending"},H={class:"header-actions"},K=["disabled"],$=["disabled"],J={class:"stats-grid"},Q={class:"stat-card"},R={class:"stat-icon"},W={class:"stat-content"},X={class:"stat-value"},Y={class:"stat-card"},Z={class:"stat-icon"},aa={class:"stat-content"},la={class:"stat-value"},sa={class:"stat-card"},ea={class:"stat-icon"},ta={class:"stat-content"},na={class:"stat-value"},oa={class:"stat-card"},ca={class:"stat-icon"},ia={class:"stat-content"},da={class:"stat-value"},ua={class:"content-grid"},ra={class:"content-section"},va={class:"section-header"},pa={class:"tools-list"},ma={class:"tool-info"},ga={class:"tool-icon"},ba={class:"tool-details"},fa={class:"tool-meta"},ha={class:"modified-time"},ya={class:"tool-actions"},ka=["onClick"],Ca=["onClick"],wa={key:0,class:"empty-state"},xa={class:"content-section"},Sa={class:"preferences-form"},Va={class:"form-group"},La={class:"form-group"},Ma={class:"form-group"},Ua={class:"checkbox-label"},Da={class:"form-group"},Ia={class:"checkbox-label"},_a={class:"content-section"},Ba={class:"data-actions"},Fa={class:"storage-usage"},ja={class:"usage-header"},za={class:"usage-bar"},Ta={class:"usage-details"},Ea={class:"modal-header"},Oa={class:"modal-body"},Pa={class:"form-group"},qa={class:"form-group"},Na={class:"form-group"},Aa={class:"form-group"},Ga={class:"modal-footer"},Ha=["disabled"],Ka={key:1,class:"error-toast"},$a=O(a({__name:"LocalManagementView",setup(a){const O=E(),$a=l(!1),Ja=l(),Qa=l({name:"",description:"",url:"",icon:"🔧"}),{isOfflineMode:Ra,isSyncing:Wa,syncError:Xa,lastSyncTime:Ya,userPreferences:Za,localTools:al,localCategories:ll,pendingSyncCount:sl,storageInfo:el,isOnline:tl}=O,nl=s({get:()=>Za.value,set:a=>O.updateUserPreferences(a)}),ol=s(()=>{if(!Ya.value)return"从未同步";return new Date(Ya.value).toLocaleString("zh-CN")}),cl=async()=>{try{await O.forceSyncData()}catch(a){console.error("同步失败:",a)}},il=()=>{O.toggleOfflineMode()},dl=()=>{O.updateUserPreferences(nl.value)},ul=()=>{try{O.addLocalTool({name:Qa.value.name,description:Qa.value.description,url:Qa.value.url,icon:Qa.value.icon,categoryId:"",tags:[],isFeatured:!1,clickCount:0}),rl()}catch(a){console.error("添加工具失败:",a)}},rl=()=>{$a.value=!1,Qa.value={name:"",description:"",url:"",icon:"🔧"}},vl=()=>{O.exportLocalData()},pl=()=>{var a;null==(a=Ja.value)||a.click()},ml=async a=>{var l;const s=null==(l=a.target.files)?void 0:l[0];if(s)try{await O.importLocalData(s)}catch(e){console.error("导入失败:",e)}},gl=()=>{confirm("确定要清空所有本地数据吗？此操作不可恢复。")&&O.clearLocalData()},bl=a=>{if(0===a)return"0 B";const l=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,l)).toFixed(2))+" "+["B","KB","MB","GB"][l]};return e(()=>{O.initialize()}),t(()=>{window.removeEventListener("online",()=>{}),window.removeEventListener("offline",()=>{})}),(a,l)=>(T(),n("div",P,[o("div",q,[o("div",N,[l[12]||(l[12]=o("h2",{class:"page-title"},"本地管理",-1)),o("div",A,[o("div",{class:i(["status-item",{online:d(tl),offline:!d(tl)}])},[l[11]||(l[11]=o("div",{class:"status-dot"},null,-1)),o("span",null,u(d(tl)?"在线":"离线"),1)],2),d(sl)>0?(T(),n("div",G,[r(d(v),{class:"icon"}),o("span",null,u(d(sl))+" 项待同步",1)])):c("",!0)])]),o("div",H,[o("button",{class:"btn secondary",disabled:d(Wa),onClick:il},[d(Ra)?(T(),p(d(b),{key:1,class:"icon"})):(T(),p(d(g),{key:0,class:"icon"})),m(" "+u(d(Ra)?"启用在线模式":"启用离线模式"),1)],8,K),o("button",{class:"btn primary",disabled:!d(tl)||d(Wa)||0===d(sl),onClick:cl},[r(d(f),{class:i(["icon",{spinning:d(Wa)}])},null,8,["class"]),m(" "+u(d(Wa)?"同步中...":"立即同步"),1)],8,$)])]),o("div",J,[o("div",Q,[o("div",R,[r(d(h),{class:"icon"})]),o("div",W,[o("div",X,u(d(al).length),1),l[13]||(l[13]=o("div",{class:"stat-label"},"本地工具",-1))])]),o("div",Y,[o("div",Z,[r(d(y),{class:"icon"})]),o("div",aa,[o("div",la,u(d(ll).length),1),l[14]||(l[14]=o("div",{class:"stat-label"},"本地分类",-1))])]),o("div",sa,[o("div",ea,[r(d(k),{class:"icon"})]),o("div",ta,[o("div",na,u(bl(d(el).used)),1),l[15]||(l[15]=o("div",{class:"stat-label"},"存储使用",-1))])]),o("div",oa,[o("div",ca,[r(d(v),{class:"icon"})]),o("div",ia,[o("div",da,u(ol.value),1),l[16]||(l[16]=o("div",{class:"stat-label"},"最后同步",-1))])])]),o("div",ua,[o("div",ra,[o("div",va,[l[18]||(l[18]=o("h3",null,"本地工具",-1)),o("button",{class:"btn small primary",onClick:l[0]||(l[0]=a=>$a.value=!0)},[r(d(C),{class:"icon"}),l[17]||(l[17]=m(" 添加工具 "))])]),o("div",pa,[(T(!0),n(w,null,x(d(al),a=>{return T(),n("div",{key:a.localId||a.id,class:i(["tool-item",{pending:"pending"===a.syncStatus}])},[o("div",ma,[o("div",ga,u(a.icon||"🔧"),1),o("div",ba,[o("h4",null,u(a.name),1),o("p",null,u(a.description),1),o("div",fa,[o("span",{class:i(["sync-status",a.syncStatus])},u((s=a.syncStatus,{pending:"待同步",synced:"已同步",conflict:"冲突"}[s||"synced"]||"未知")),3),o("span",ha,u((l=a.lastModified,l?new Date(l).toLocaleString("zh-CN"):"")),1)])])]),o("div",ya,[o("button",{class:"btn small secondary",onClick:l=>(a=>{console.log("编辑工具:",a)})(a)},[r(d(S),{class:"icon"})],8,ka),o("button",{class:"btn small danger",onClick:l=>(a=>{confirm("确定要删除这个工具吗？")&&O.deleteLocalTool(a.id||a.localId)})(a)},[r(d(V),{class:"icon"})],8,Ca)])],2);var l,s}),128)),0===d(al).length?(T(),n("div",wa,[r(d(h),{class:"empty-icon"}),l[19]||(l[19]=o("p",null,"暂无本地工具",-1))])):c("",!0)])]),o("div",xa,[l[26]||(l[26]=o("div",{class:"section-header"},[o("h3",null,"偏好设置")],-1)),o("div",Sa,[o("div",Va,[l[21]||(l[21]=o("label",null,"主题",-1)),L(o("select",{"onUpdate:modelValue":l[1]||(l[1]=a=>nl.value.theme=a),onChange:dl},l[20]||(l[20]=[o("option",{value:"auto"},"自动",-1),o("option",{value:"light"},"浅色",-1),o("option",{value:"dark"},"深色",-1)]),544),[[M,nl.value.theme]])]),o("div",La,[l[23]||(l[23]=o("label",null,"默认视图",-1)),L(o("select",{"onUpdate:modelValue":l[2]||(l[2]=a=>nl.value.defaultView=a),onChange:dl},l[22]||(l[22]=[o("option",{value:"grid"},"网格",-1),o("option",{value:"list"},"列表",-1)]),544),[[M,nl.value.defaultView]])]),o("div",Ma,[o("label",Ua,[L(o("input",{"onUpdate:modelValue":l[3]||(l[3]=a=>nl.value.autoSync=a),type:"checkbox",onChange:dl},null,544),[[U,nl.value.autoSync]]),l[24]||(l[24]=o("span",null,"自动同步",-1))])]),o("div",Da,[o("label",Ia,[L(o("input",{"onUpdate:modelValue":l[4]||(l[4]=a=>nl.value.sidebarCollapsed=a),type:"checkbox",onChange:dl},null,544),[[U,nl.value.sidebarCollapsed]]),l[25]||(l[25]=o("span",null,"折叠侧边栏",-1))])])])]),o("div",_a,[l[31]||(l[31]=o("div",{class:"section-header"},[o("h3",null,"数据管理")],-1)),o("div",Ba,[o("button",{class:"btn secondary full-width",onClick:vl},[r(d(D),{class:"icon"}),l[27]||(l[27]=m(" 导出本地数据 "))]),o("button",{class:"btn secondary full-width",onClick:pl},[r(d(I),{class:"icon"}),l[28]||(l[28]=m(" 导入本地数据 "))]),o("input",{ref_key:"fileInput",ref:Ja,type:"file",accept:".json",style:{display:"none"},onChange:ml},null,544),o("button",{class:"btn danger full-width",onClick:gl},[r(d(V),{class:"icon"}),l[29]||(l[29]=m(" 清空本地数据 "))])]),o("div",Fa,[o("div",ja,[l[30]||(l[30]=o("span",null,"存储使用情况",-1)),o("span",null,u(d(el).percentage.toFixed(1))+"%",1)]),o("div",za,[o("div",{class:"usage-fill",style:_({width:`${Math.min(d(el).percentage,100)}%`})},null,4)]),o("div",Ta,[o("span",null,u(bl(d(el).used))+" / "+u(bl(d(el).total)),1)])])])]),$a.value?(T(),n("div",{key:0,class:"modal-overlay",onClick:rl},[o("div",{class:"modal-content",onClick:l[9]||(l[9]=j(()=>{},["stop"]))},[o("div",Ea,[l[32]||(l[32]=o("h3",null,"添加本地工具",-1)),o("button",{class:"close-btn",onClick:rl},[r(d(B),{class:"icon"})])]),o("div",Oa,[o("div",Pa,[l[33]||(l[33]=o("label",null,"工具名称",-1)),L(o("input",{"onUpdate:modelValue":l[5]||(l[5]=a=>Qa.value.name=a),type:"text",placeholder:"输入工具名称"},null,512),[[F,Qa.value.name]])]),o("div",qa,[l[34]||(l[34]=o("label",null,"工具描述",-1)),L(o("textarea",{"onUpdate:modelValue":l[6]||(l[6]=a=>Qa.value.description=a),placeholder:"输入工具描述"},null,512),[[F,Qa.value.description]])]),o("div",Na,[l[35]||(l[35]=o("label",null,"工具链接",-1)),L(o("input",{"onUpdate:modelValue":l[7]||(l[7]=a=>Qa.value.url=a),type:"url",placeholder:"https://example.com"},null,512),[[F,Qa.value.url]])]),o("div",Aa,[l[36]||(l[36]=o("label",null,"工具图标",-1)),L(o("input",{"onUpdate:modelValue":l[8]||(l[8]=a=>Qa.value.icon=a),type:"text",placeholder:"🔧"},null,512),[[F,Qa.value.icon]])])]),o("div",Ga,[o("button",{class:"btn secondary",onClick:rl},"取消"),o("button",{class:"btn primary",disabled:!Qa.value.name,onClick:ul}," 添加 ",8,Ha)])])])):c("",!0),d(Xa)?(T(),n("div",Ka,[r(d(z),{class:"icon"}),o("span",null,u(d(Xa)),1),o("button",{onClick:l[10]||(l[10]=a=>Xa.value=null)},[r(d(B),{class:"icon"})])])):c("",!0)]))}}),[["__scopeId","data-v-68fb3c9a"]]);export{$a as default};
