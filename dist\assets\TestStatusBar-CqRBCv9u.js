import{d as e,i as l,j as s,k as n,c as t,a,t as i,o}from"./vendor-DhoxJlSg.js";import{_ as d}from"./index-Dhbj_B_-.js";const r={class:"test-page"},u={class:"test-content"},v={class:"test-info"},c=d(e({__name:"TestStatusBar",setup(e){const d=l(0),c=l(0),p=l(0),w=()=>{d.value=document.documentElement.scrollHeight,c.value=window.innerHeight,p.value=window.scrollY};return s(()=>{w(),window.addEventListener("scroll",w),window.addEventListener("resize",w)}),n(()=>{window.removeEventListener("scroll",w),window.removeEventListener("resize",w)}),(e,l)=>(o(),t("div",r,[a("div",u,[l[2]||(l[2]=a("h1",null,"状态栏测试页面",-1)),l[3]||(l[3]=a("p",null,"这个页面用于测试底部状态栏是否正常显示",-1)),a("div",v,[l[1]||(l[1]=a("h2",null,"测试信息",-1)),a("ul",null,[a("li",null,"页面高度: "+i(d.value)+"px",1),a("li",null,"视口高度: "+i(c.value)+"px",1),a("li",null,"滚动位置: "+i(p.value)+"px",1),l[0]||(l[0]=a("li",null,"状态栏应该显示在底部",-1))])]),l[4]||(l[4]=a("div",{class:"spacer"},null,-1)),l[5]||(l[5]=a("div",{class:"bottom-content"},[a("p",null,"这是页面底部内容，状态栏应该在这下面显示")],-1))])]))}}),[["__scopeId","data-v-7756201b"]]);export{c as default};
