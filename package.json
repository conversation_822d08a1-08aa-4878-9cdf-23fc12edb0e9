{"name": "advanced-tools-navigation", "version": "1.0.0", "description": "工具导航站 - 让工作更高效", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "deploy": "gh-pages -d dist", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:status": "supabase status", "supabase:reset": "supabase db reset", "supabase:push": "supabase db push", "supabase:pull": "supabase db pull", "supabase:gen-types": "supabase gen types typescript --linked > src/types/database.ts", "supabase:setup": "bash scripts/deployment/setup-supabase.sh", "supabase:setup-win": "powershell -ExecutionPolicy Bypass -File scripts/deployment/setup-supabase.ps1", "supabase:verify": "node scripts/deployment/verify-deployment.js", "supabase:storage-setup": "node scripts/database/setup-storage-buckets.js", "supabase:fix-policies": "node scripts/database/fix-storage-policies.js", "supabase:create-buckets": "node scripts/database/create-storage-buckets-only.js", "supabase:simple-setup": "node scripts/database/simple-storage-setup.js", "db:check-wee": "node scripts/database/simple-check-wee.js", "supabase:deploy": "powershell -ExecutionPolicy Bypass -File scripts/deployment/setup-supabase-auto.ps1", "deployment:status": "node scripts/deployment/check-deployment-status.js", "deployment:trigger": "node scripts/deployment/trigger-deployment.js", "secrets:check": "node scripts/deployment/check-github-secrets.js", "deps:clean": "node scripts/deployment/clean-dependencies.js", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:integration": "vitest run src/tests/integration", "test:e2e": "vitest run src/tests/e2e", "test:deployment": "node scripts/deployment/test-deployment-workflow.js", "pre-deploy": "node scripts/deployment/pre-deploy-check.mjs", "build:optimize": "node scripts/performance/optimize-build.mjs", "analyze": "yarn build && npx vite-bundle-analyzer dist", "monitor:health": "node scripts/deployment/monitor-deployment.js", "monitor:watch": "node scripts/deployment/monitor-deployment.js --monitor", "config:verify": "node scripts/deployment/verify-config-match.js", "deploy:guide": "echo '📖 查看部署指南: docs/deployment/UPDATE_DEPLOYMENT_GUIDE.md'"}, "dependencies": {"@octokit/rest": "22.0.0", "@supabase/supabase-js": "^2.50.1", "@types/uuid": "^10.0.0", "@vueuse/core": "^10.7.0", "@vueuse/integrations": "^13.4.0", "dotenv": "16.5.0", "lucide-vue-next": "^0.294.0", "pinia": "2.3.1", "uuid": "^11.1.0", "vue": "3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^5.0.0", "@vitest/ui": "^0.34.3", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/runtime-dom": "3.5.17", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "gh-pages": "^6.3.0", "jsdom": "^22.1.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.4.0", "terser": "^5.43.1", "typescript": "~5.3.0", "vite": "^5.0.0", "vitest": "^0.34.3", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.19.0"}, "optionalDependencies": {"@rollup/rollup-win32-x64-msvc": "4.44.0"}}