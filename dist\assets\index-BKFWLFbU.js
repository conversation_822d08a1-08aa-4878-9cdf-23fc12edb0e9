const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-Bj0i_E7S.js","./vendor-CSeT1gXp.js","./EnhancedHomeView-D9tysLox.js","./categories-V_cSW4yv.js","./EnhancedHomeView-CmvVSbzx.css","./HomeView-BpCYTNJB.js","./HomeView-CsZZ03yk.css","./ComparisonView-Dvi63WL6.js","./ComparisonView-BBLNMYPy.css","./TestStatusBar-BSgIjrnl.js","./TestStatusBar-k2A8_0Fe.css","./ToolsView-K3Vb-3Wc.js","./ToolsView-BiLlEeUJ.css","./ProductsView-BzprdfvT.js","./ProductsView-DT_bfOFu.css","./ProductDetailView-U0E56uPS.js","./ProductDetailView-B1YLHNwr.css","./UserView-DTOTqe8H.js","./UserView-BGzqa1ua.css","./ProfileView-BPVWElh8.js","./ProfileView-DpPtkwxE.css","./FavoritesView-DLwbXd_E.js","./FavoritesView-RzeM2c7T.css","./OrdersView-BWnDGqel.js","./OrdersView-BY6Q3swg.css","./AuthView-CVZYkuZm.js","./AuthView-kJTw_t9R.css","./LoginView-DWiq3o6Q.js","./LoginView-DqUiEiAL.css","./RegisterView-jB4aOFAd.js","./RegisterView-BfPJesmG.css","./ForgotPasswordView-Dp_FYhbU.js","./ForgotPasswordView-CsrSL9dm.css","./AdminView-CKcVIYJV.js","./AdminView-D-c4a1ji.css","./DashboardView-CS5iCQ6c.js","./DashboardView-CkbHANYK.css","./AdminToolsView-CMiBhoo5.js","./AdminToolsView-FnuLN_2m.css","./ProductsManageView-BlGcTlSA.js","./ProductsManageView-BMAVYlPv.css","./LocalManagementView-BEHQLmBq.js","./LocalManagementView-BKhuuKM8.css","./LocalManagementTestView-CWc-a4IA.js","./LocalManagementTestView-CCGHFHjt.css","./PaymentView-CkXy32NF.js","./PaymentView-dCfgJGjJ.css","./PaymentSuccessView-cOBijyKY.js","./PaymentSuccessView-BYCqjQvv.css","./PaymentCancelView-D-y4Rq7n.js","./PaymentCancelView-5StAoE7K.css","./NotFoundView-DxjTcsqR.js","./NotFoundView-J7iPsJwS.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,r,s)=>((t,r,s)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s)(t,"symbol"!=typeof r?r+"":r,s);import{s as r,d as s,u as a,a as n,c as o,p as i,r as l,w as c,h as u,n as d,i as h,b as f,e as p,o as m,g as v,f as g,j as y,k as w,l as _,m as b,q as k,t as S,v as E,S as T,M as O,x as C,y as P,X as A,F as j,z as I,A as R,B as x,C as $,D as L,E as U,G as D,H as q,I as N,J as M,K as B,L as F,N as V,O as z,P as G,Q as J,R as W,T as H,U as K,V as Q,W as Y,Y as Z,Z as X,_ as ee,$ as te,a0 as re,a1 as se,a2 as ae,a3 as ne,a4 as oe,a5 as ie,a6 as le,a7 as ce,a8 as ue,a9 as de,aa as he,ab as fe,ac as pe,ad as me,ae as ve,af as ge,ag as ye,ah as we,ai as _e}from"./vendor-CSeT1gXp.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();
/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */
const be="undefined"!=typeof document;function ke(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const Se=Object.assign;function Ee(e,t){const r={};for(const s in t){const a=t[s];r[s]=Oe(a)?a.map(e):e(a)}return r}const Te=()=>{},Oe=Array.isArray,Ce=/#/g,Pe=/&/g,Ae=/\//g,je=/=/g,Ie=/\?/g,Re=/\+/g,xe=/%5B/g,$e=/%5D/g,Le=/%5E/g,Ue=/%60/g,De=/%7B/g,qe=/%7C/g,Ne=/%7D/g,Me=/%20/g;function Be(e){return encodeURI(""+e).replace(qe,"|").replace(xe,"[").replace($e,"]")}function Fe(e){return Be(e).replace(Re,"%2B").replace(Me,"+").replace(Ce,"%23").replace(Pe,"%26").replace(Ue,"`").replace(De,"{").replace(Ne,"}").replace(Le,"^")}function Ve(e){return Fe(e).replace(je,"%3D")}function ze(e){return null==e?"":function(e){return Be(e).replace(Ce,"%23").replace(Ie,"%3F")}(e).replace(Ae,"%2F")}function Ge(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Je=/\/$/;function We(e,t,r="/"){let s,a={},n="",o="";const i=t.indexOf("#");let l=t.indexOf("?");return i<l&&i>=0&&(l=-1),l>-1&&(s=t.slice(0,l),n=t.slice(l+1,i>-1?i:t.length),a=e(n)),i>-1&&(s=s||t.slice(0,i),o=t.slice(i,t.length)),s=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),s=e.split("/"),a=s[s.length-1];".."!==a&&"."!==a||s.push("");let n,o,i=r.length-1;for(n=0;n<s.length;n++)if(o=s[n],"."!==o){if(".."!==o)break;i>1&&i--}return r.slice(0,i).join("/")+"/"+s.slice(n).join("/")}(null!=s?s:t,r),{fullPath:s+(n&&"?")+n+o,path:s,query:a,hash:Ge(o)}}function He(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Ke(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Qe(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!Ye(e[r],t[r]))return!1;return!0}function Ye(e,t){return Oe(e)?Ze(e,t):Oe(t)?Ze(t,e):e===t}function Ze(e,t){return Oe(t)?e.length===t.length&&e.every((e,r)=>e===t[r]):1===e.length&&e[0]===t}const Xe={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var et,tt,rt,st;function at(e){if(!e)if(be){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Je,"")}(tt=et||(et={})).pop="pop",tt.push="push",(st=rt||(rt={})).back="back",st.forward="forward",st.unknown="";const nt=/^[^#]+#/;function ot(e,t){return e.replace(nt,"#")+t}const it=()=>({left:window.scrollX,top:window.scrollY});function lt(e){let t;if("el"in e){const r=e.el,s="string"==typeof r&&r.startsWith("#"),a="string"==typeof r?s?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!a)return;t=function(e,t){const r=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-r.left-(t.left||0),top:s.top-r.top-(t.top||0)}}(a,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function ct(e,t){return(history.state?history.state.position-t:-1)+e}const ut=new Map;function dt(e,t){const{pathname:r,search:s,hash:a}=t,n=e.indexOf("#");if(n>-1){let t=a.includes(e.slice(n))?e.slice(n).length:1,r=a.slice(t);return"/"!==r[0]&&(r="/"+r),He(r,"")}return He(r,e)+s+a}function ht(e,t,r,s=!1,a=!1){return{back:e,current:t,forward:r,replaced:s,position:window.history.length,scroll:a?it():null}}function ft(e){const{history:t,location:r}=window,s={value:dt(e,r)},a={value:t.state};function n(s,n,o){const i=e.indexOf("#"),l=i>-1?(r.host&&document.querySelector("base")?e:e.slice(i))+s:location.protocol+"//"+location.host+e+s;try{t[o?"replaceState":"pushState"](n,"",l),a.value=n}catch(c){console.error(c),r[o?"replace":"assign"](l)}}return a.value||n(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:s,state:a,push:function(e,r){const o=Se({},a.value,t.state,{forward:e,scroll:it()});n(o.current,o,!0),n(e,Se({},ht(s.value,e,null),{position:o.position+1},r),!1),s.value=e},replace:function(e,r){n(e,Se({},t.state,ht(a.value.back,e,a.value.forward,!0),r,{position:a.value.position}),!0),s.value=e}}}function pt(e){return"string"==typeof e||"symbol"==typeof e}const mt=Symbol("");var vt,gt;function yt(e,t){return Se(new Error,{type:e,[mt]:!0},t)}function wt(e,t){return e instanceof Error&&mt in e&&(null==t||!!(e.type&t))}(gt=vt||(vt={}))[gt.aborted=4]="aborted",gt[gt.cancelled=8]="cancelled",gt[gt.duplicated=16]="duplicated";const _t="[^/]+?",bt={sensitive:!1,strict:!1,start:!0,end:!0},kt=/[.+*?^${}()[\]/\\]/g;function St(e,t){let r=0;for(;r<e.length&&r<t.length;){const s=t[r]-e[r];if(s)return s;r++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Et(e,t){let r=0;const s=e.score,a=t.score;for(;r<s.length&&r<a.length;){const e=St(s[r],a[r]);if(e)return e;r++}if(1===Math.abs(a.length-s.length)){if(Tt(s))return 1;if(Tt(a))return-1}return a.length-s.length}function Tt(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ot={type:0,value:""},Ct=/[a-zA-Z0-9_]/;function Pt(e,t,r){const s=function(e,t){const r=Se({},bt,t),s=[];let a=r.start?"^":"";const n=[];for(const l of e){const e=l.length?[]:[90];r.strict&&!l.length&&(a+="/");for(let t=0;t<l.length;t++){const s=l[t];let o=40+(r.sensitive?.25:0);if(0===s.type)t||(a+="/"),a+=s.value.replace(kt,"\\$&"),o+=40;else if(1===s.type){const{value:e,repeatable:r,optional:c,regexp:u}=s;n.push({name:e,repeatable:r,optional:c});const d=u||_t;if(d!==_t){o+=10;try{new RegExp(`(${d})`)}catch(i){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+i.message)}}let h=r?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(h=c&&l.length<2?`(?:/${h})`:"/"+h),c&&(h+="?"),a+=h,o+=20,c&&(o+=-8),r&&(o+=-20),".*"===d&&(o+=-50)}e.push(o)}s.push(e)}if(r.strict&&r.end){const e=s.length-1;s[e][s[e].length-1]+=.7000000000000001}r.strict||(a+="/?"),r.end?a+="$":r.strict&&!a.endsWith("/")&&(a+="(?:/|$)");const o=new RegExp(a,r.sensitive?"":"i");return{re:o,score:s,keys:n,parse:function(e){const t=e.match(o),r={};if(!t)return null;for(let s=1;s<t.length;s++){const e=t[s]||"",a=n[s-1];r[a.name]=e&&a.repeatable?e.split("/"):e}return r},stringify:function(t){let r="",s=!1;for(const a of e){s&&r.endsWith("/")||(r+="/"),s=!1;for(const e of a)if(0===e.type)r+=e.value;else if(1===e.type){const{value:n,repeatable:o,optional:i}=e,l=n in t?t[n]:"";if(Oe(l)&&!o)throw new Error(`Provided param "${n}" is an array but it is not repeatable (* or + modifiers)`);const c=Oe(l)?l.join("/"):l;if(!c){if(!i)throw new Error(`Missing required param "${n}"`);a.length<2&&(r.endsWith("/")?r=r.slice(0,-1):s=!0)}r+=c}}return r||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Ot]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${r})/"${c}": ${e}`)}let r=0,s=r;const a=[];let n;function o(){n&&a.push(n),n=[]}let i,l=0,c="",u="";function d(){c&&(0===r?n.push({type:0,value:c}):1===r||2===r||3===r?(n.length>1&&("*"===i||"+"===i)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),n.push({type:1,value:c,regexp:u,repeatable:"*"===i||"+"===i,optional:"*"===i||"?"===i})):t("Invalid state to consume buffer"),c="")}function h(){c+=i}for(;l<e.length;)if(i=e[l++],"\\"!==i||2===r)switch(r){case 0:"/"===i?(c&&d(),o()):":"===i?(d(),r=1):h();break;case 4:h(),r=s;break;case 1:"("===i?r=2:Ct.test(i)?h():(d(),r=0,"*"!==i&&"?"!==i&&"+"!==i&&l--);break;case 2:")"===i?"\\"==u[u.length-1]?u=u.slice(0,-1)+i:r=3:u+=i;break;case 3:d(),r=0,"*"!==i&&"?"!==i&&"+"!==i&&l--,u="";break;default:t("Unknown state")}else s=r,r=4;return 2===r&&t(`Unfinished custom RegExp for param "${c}"`),d(),o(),a}(e.path),r),a=Se(s,{record:e,parent:t,children:[],alias:[]});return t&&!a.record.aliasOf==!t.record.aliasOf&&t.children.push(a),a}function At(e,t){const r=[],s=new Map;function a(e,r,s){const i=!s,l=It(e);l.aliasOf=s&&s.record;const c=Lt(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(It(Se({},l,{components:s?s.record.components:l.components,path:e,aliasOf:s?s.record:l})))}let d,h;for(const t of u){const{path:u}=t;if(r&&"/"!==u[0]){const e=r.record.path,s="/"===e[e.length-1]?"":"/";t.path=r.record.path+(u&&s+u)}if(d=Pt(t,r,c),s?s.alias.push(d):(h=h||d,h!==d&&h.alias.push(d),i&&e.name&&!xt(d)&&n(e.name)),Ut(d)&&o(d),l.children){const e=l.children;for(let t=0;t<e.length;t++)a(e[t],d,s&&s.children[t])}s=s||d}return h?()=>{n(h)}:Te}function n(e){if(pt(e)){const t=s.get(e);t&&(s.delete(e),r.splice(r.indexOf(t),1),t.children.forEach(n),t.alias.forEach(n))}else{const t=r.indexOf(e);t>-1&&(r.splice(t,1),e.record.name&&s.delete(e.record.name),e.children.forEach(n),e.alias.forEach(n))}}function o(e){const t=function(e,t){let r=0,s=t.length;for(;r!==s;){const a=r+s>>1;Et(e,t[a])<0?s=a:r=a+1}const a=function(e){let t=e;for(;t=t.parent;)if(Ut(t)&&0===Et(e,t))return t;return}(e);a&&(s=t.lastIndexOf(a,s-1));return s}(e,r);r.splice(t,0,e),e.record.name&&!xt(e)&&s.set(e.record.name,e)}return t=Lt({strict:!1,end:!0,sensitive:!1},t),e.forEach(e=>a(e)),{addRoute:a,resolve:function(e,t){let a,n,o,i={};if("name"in e&&e.name){if(a=s.get(e.name),!a)throw yt(1,{location:e});o=a.record.name,i=Se(jt(t.params,a.keys.filter(e=>!e.optional).concat(a.parent?a.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&jt(e.params,a.keys.map(e=>e.name))),n=a.stringify(i)}else if(null!=e.path)n=e.path,a=r.find(e=>e.re.test(n)),a&&(i=a.parse(n),o=a.record.name);else{if(a=t.name?s.get(t.name):r.find(e=>e.re.test(t.path)),!a)throw yt(1,{location:e,currentLocation:t});o=a.record.name,i=Se({},t.params,e.params),n=a.stringify(i)}const l=[];let c=a;for(;c;)l.unshift(c.record),c=c.parent;return{name:o,path:n,params:i,matched:l,meta:$t(l)}},removeRoute:n,clearRoutes:function(){r.length=0,s.clear()},getRoutes:function(){return r},getRecordMatcher:function(e){return s.get(e)}}}function jt(e,t){const r={};for(const s of t)s in e&&(r[s]=e[s]);return r}function It(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Rt(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Rt(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const s in e.components)t[s]="object"==typeof r?r[s]:r;return t}function xt(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function $t(e){return e.reduce((e,t)=>Se(e,t.meta),{})}function Lt(e,t){const r={};for(const s in e)r[s]=s in t?t[s]:e[s];return r}function Ut({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Dt(e){const t={};if(""===e||"?"===e)return t;const r=("?"===e[0]?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const e=r[s].replace(Re," "),a=e.indexOf("="),n=Ge(a<0?e:e.slice(0,a)),o=a<0?null:Ge(e.slice(a+1));if(n in t){let e=t[n];Oe(e)||(e=t[n]=[e]),e.push(o)}else t[n]=o}return t}function qt(e){let t="";for(let r in e){const s=e[r];if(r=Ve(r),null==s){void 0!==s&&(t+=(t.length?"&":"")+r);continue}(Oe(s)?s.map(e=>e&&Fe(e)):[s&&Fe(s)]).forEach(e=>{void 0!==e&&(t+=(t.length?"&":"")+r,null!=e&&(t+="="+e))})}return t}function Nt(e){const t={};for(const r in e){const s=e[r];void 0!==s&&(t[r]=Oe(s)?s.map(e=>null==e?null:""+e):null==s?s:""+s)}return t}const Mt=Symbol(""),Bt=Symbol(""),Ft=Symbol(""),Vt=Symbol(""),zt=Symbol("");function Gt(){let e=[];return{add:function(t){return e.push(t),()=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Jt(e,t,r,s,a,n=e=>e()){const o=s&&(s.enterCallbacks[a]=s.enterCallbacks[a]||[]);return()=>new Promise((i,l)=>{const c=e=>{var n;!1===e?l(yt(4,{from:r,to:t})):e instanceof Error?l(e):"string"==typeof(n=e)||n&&"object"==typeof n?l(yt(2,{from:t,to:e})):(o&&s.enterCallbacks[a]===o&&"function"==typeof e&&o.push(e),i())},u=n(()=>e.call(s&&s.instances[a],t,r,c));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch(e=>l(e))})}function Wt(e,t,r,s,a=e=>e()){const n=[];for(const o of e)for(const e in o.components){let i=o.components[e];if("beforeRouteEnter"===t||o.instances[e])if(ke(i)){const l=(i.__vccOpts||i)[t];l&&n.push(Jt(l,r,s,o,e,a))}else{let l=i();n.push(()=>l.then(n=>{if(!n)throw new Error(`Couldn't resolve component "${e}" at "${o.path}"`);const i=(l=n).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&ke(l.default)?n.default:n;var l;o.mods[e]=n,o.components[e]=i;const c=(i.__vccOpts||i)[t];return c&&Jt(c,r,s,o,e,a)()}))}}return n}function Ht(e){const t=h(Ft),r=h(Vt),s=o(()=>{const r=a(e.to);return t.resolve(r)}),n=o(()=>{const{matched:e}=s.value,{length:t}=e,a=e[t-1],n=r.matched;if(!a||!n.length)return-1;const o=n.findIndex(Ke.bind(null,a));if(o>-1)return o;const i=Yt(e[t-2]);return t>1&&Yt(a)===i&&n[n.length-1].path!==i?n.findIndex(Ke.bind(null,e[t-2])):o}),i=o(()=>n.value>-1&&function(e,t){for(const r in t){const s=t[r],a=e[r];if("string"==typeof s){if(s!==a)return!1}else if(!Oe(a)||a.length!==s.length||s.some((e,t)=>e!==a[t]))return!1}return!0}(r.params,s.value.params)),l=o(()=>n.value>-1&&n.value===r.matched.length-1&&Qe(r.params,s.value.params));return{route:s,href:o(()=>s.value.href),isActive:i,isExactActive:l,navigate:function(r={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(r)){const r=t[a(e.replace)?"replace":"push"](a(e.to)).catch(Te);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition(()=>r),r}return Promise.resolve()}}}const Kt=s({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ht,setup(e,{slots:t}){const r=f(Ht(e)),{options:s}=h(Ft),a=o(()=>({[Zt(e.activeClass,s.linkActiveClass,"router-link-active")]:r.isActive,[Zt(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const s=t.default&&(1===(n=t.default(r)).length?n[0]:n);var n;return e.custom?s:u("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:a.value},s)}}}),Qt=Kt;function Yt(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Zt=(e,t,r)=>null!=e?e:null!=t?t:r;function Xt(e,t){if(!e)return null;const r=e(t);return 1===r.length?r[0]:r}const er=s({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const s=h(zt),n=o(()=>e.route||s.value),d=h(Bt,0),f=o(()=>{let e=a(d);const{matched:t}=n.value;let r;for(;(r=t[e])&&!r.components;)e++;return e}),p=o(()=>n.value.matched[f.value]);i(Bt,o(()=>f.value+1)),i(Mt,p),i(zt,n);const m=l();return c(()=>[m.value,p.value,e.name],([e,t,r],[s,a,n])=>{t&&(t.instances[r]=e,a&&a!==t&&e&&e===s&&(t.leaveGuards.size||(t.leaveGuards=a.leaveGuards),t.updateGuards.size||(t.updateGuards=a.updateGuards))),!e||!t||a&&Ke(t,a)&&s||(t.enterCallbacks[r]||[]).forEach(t=>t(e))},{flush:"post"}),()=>{const s=n.value,a=e.name,o=p.value,i=o&&o.components[a];if(!i)return Xt(r.default,{Component:i,route:s});const l=o.props[a],c=l?!0===l?s.params:"function"==typeof l?l(s):l:null,d=u(i,Se({},c,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(o.instances[a]=null)},ref:m}));return Xt(r.default,{Component:d,route:s})||d}}});function tr(){return h(Ft)}function rr(e){return h(Vt)}const sr={},ar=function(e,t,r){let s=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),n=(null==a?void 0:a.nonce)||(null==a?void 0:a.getAttribute("nonce"));s=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,r),t in sr)return;sr[t]=!0;const s=t.endsWith(".css"),a=s?'[rel="stylesheet"]':"";if(!!r)for(let r=e.length-1;r>=0;r--){const a=e[r];if(a.href===t&&(!s||"stylesheet"===a.rel))return}else if(document.querySelector(`link[href="${t}"]${a}`))return;const o=document.createElement("link");return o.rel=s?"stylesheet":"modulepreload",s||(o.as="script"),o.crossOrigin="",o.href=t,n&&o.setAttribute("nonce",n),document.head.appendChild(o),s?new Promise((e,r)=>{o.addEventListener("load",e),o.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function a(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return s.then(t=>{for(const e of t||[])"rejected"===e.status&&a(e.reason);return e().catch(a)})};class nr extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class or extends nr{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class ir extends nr{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class lr extends nr{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var cr,ur;(ur=cr||(cr={})).Any="any",ur.ApNortheast1="ap-northeast-1",ur.ApNortheast2="ap-northeast-2",ur.ApSouth1="ap-south-1",ur.ApSoutheast1="ap-southeast-1",ur.ApSoutheast2="ap-southeast-2",ur.CaCentral1="ca-central-1",ur.EuCentral1="eu-central-1",ur.EuWest1="eu-west-1",ur.EuWest2="eu-west-2",ur.EuWest3="eu-west-3",ur.SaEast1="sa-east-1",ur.UsEast1="us-east-1",ur.UsWest1="us-west-1",ur.UsWest2="us-west-2";var dr=function(e,t,r,s){return new(r||(r=Promise))(function(a,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function i(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};class hr{constructor(e,{headers:t={},customFetch:r,region:s=cr.Any}={}){this.url=e,this.headers=t,this.region=s,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ar(async()=>{const{default:e}=await Promise.resolve().then(()=>Cr);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)})(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return dr(this,void 0,void 0,function*(){try{const{headers:s,method:a,body:n}=t;let o,i={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(i["x-region"]=l),n&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&n instanceof Blob||n instanceof ArrayBuffer?(i["Content-Type"]="application/octet-stream",o=n):"string"==typeof n?(i["Content-Type"]="text/plain",o=n):"undefined"!=typeof FormData&&n instanceof FormData?o=n:(i["Content-Type"]="application/json",o=JSON.stringify(n)));const c=yield this.fetch(`${this.url}/${e}`,{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},i),this.headers),s),body:o}).catch(e=>{throw new or(e)}),u=c.headers.get("x-relay-error");if(u&&"true"===u)throw new ir(c);if(!c.ok)throw new lr(c);let d,h=(null!==(r=c.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return d="application/json"===h?yield c.json():"application/octet-stream"===h?yield c.blob():"text/event-stream"===h?c:"multipart/form-data"===h?yield c.formData():yield c.text(),{data:d,error:null}}catch(s){return{data:null,error:s}}})}}var fr="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function pr(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var s=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,s.get?s:{enumerable:!0,get:function(){return e[t]}})}),r}var mr={},vr={},gr={},yr={},wr={},_r={},br=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const kr=br.fetch,Sr=br.fetch.bind(br),Er=br.Headers,Tr=br.Request,Or=br.Response,Cr=Object.freeze(Object.defineProperty({__proto__:null,Headers:Er,Request:Tr,Response:Or,default:Sr,fetch:kr},Symbol.toStringTag,{value:"Module"})),Pr=pr(Cr);var Ar={};Object.defineProperty(Ar,"__esModule",{value:!0});let jr=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};Ar.default=jr;var Ir=fr&&fr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(_r,"__esModule",{value:!0});const Rr=Ir(Pr),xr=Ir(Ar);_r.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=Rr.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,s;let a=null,n=null,o=null,i=e.status,l=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(n="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const s=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),c=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");s&&c&&c.length>1&&(o=parseInt(c[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(n)&&(n.length>1?(a={code:"PGRST116",details:`Results contain ${n.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},n=null,o=null,i=406,l="Not Acceptable"):n=1===n.length?n[0]:null)}else{const t=await e.text();try{a=JSON.parse(t),Array.isArray(a)&&404===e.status&&(n=[],a=null,i=200,l="OK")}catch(c){404===e.status&&""===t?(i=204,l="No Content"):a={message:t}}if(a&&this.isMaybeSingle&&(null===(s=null==a?void 0:a.details)||void 0===s?void 0:s.includes("0 rows"))&&(a=null,i=200,l="OK"),a&&this.shouldThrowOnError)throw new xr.default(a)}return{error:a,data:n,count:o,status:i,statusText:l}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,s;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(s=null==e?void 0:e.code)&&void 0!==s?s:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}};var $r=fr&&fr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(wr,"__esModule",{value:!0});const Lr=$r(_r);let Ur=class extends Lr.default{select(e){let t=!1;const r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:s,referencedTable:a=s}={}){const n=a?`${a}.order`:"order",o=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){const s=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:s=r}={}){const a=void 0===s?"offset":`${s}.offset`,n=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(a,`${e}`),this.url.searchParams.set(n,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:s=!1,wal:a=!1,format:n="text"}={}){var o;const i=[e?"analyze":null,t?"verbose":null,r?"settings":null,s?"buffers":null,a?"wal":null].filter(Boolean).join("|"),l=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${i};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};wr.default=Ur;var Dr=fr&&fr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(yr,"__esModule",{value:!0});const qr=Dr(wr);let Nr=class extends qr.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const r=Array.from(new Set(t)).map(e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s}={}){let a="";"plain"===s?a="pl":"phrase"===s?a="ph":"websearch"===s&&(a="w");const n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${a}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}};yr.default=Nr;var Mr=fr&&fr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(gr,"__esModule",{value:!0});const Br=Mr(yr);gr.default=class{constructor(e,{headers:t={},schema:r,fetch:s}){this.url=e,this.headers=t,this.schema=r,this.fetch=s}select(e,{head:t=!1,count:r}={}){const s=t?"HEAD":"GET";let a=!1;const n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!a?"":('"'===e&&(a=!a),e)).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),new Br.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){const s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new Br.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:s,defaultToNull:a=!0}={}){const n=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&n.push(this.headers.Prefer),s&&n.push(`count=${s}`),a||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new Br.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new Br.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new Br.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var Fr={},Vr={};Object.defineProperty(Vr,"__esModule",{value:!0}),Vr.version=void 0,Vr.version="0.0.0-automated",Object.defineProperty(Fr,"__esModule",{value:!0}),Fr.DEFAULT_HEADERS=void 0;const zr=Vr;Fr.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${zr.version}`};var Gr=fr&&fr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(vr,"__esModule",{value:!0});const Jr=Gr(gr),Wr=Gr(yr),Hr=Fr;vr.default=class e{constructor(e,{headers:t={},schema:r,fetch:s}={}){this.url=e,this.headers=Object.assign(Object.assign({},Hr.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=s}from(e){const t=new URL(`${this.url}/${e}`);return new Jr.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:s=!1,count:a}={}){let n;const o=new URL(`${this.url}/rpc/${e}`);let i;r||s?(n=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{o.searchParams.append(e,t)})):(n="POST",i=t);const l=Object.assign({},this.headers);return a&&(l.Prefer=`count=${a}`),new Wr.default({method:n,url:o,headers:l,schema:this.schemaName,body:i,fetch:this.fetch,allowEmpty:!1})}};var Kr=fr&&fr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(mr,"__esModule",{value:!0}),mr.PostgrestError=mr.PostgrestBuilder=mr.PostgrestTransformBuilder=mr.PostgrestFilterBuilder=mr.PostgrestQueryBuilder=mr.PostgrestClient=void 0;const Qr=Kr(vr);mr.PostgrestClient=Qr.default;const Yr=Kr(gr);mr.PostgrestQueryBuilder=Yr.default;const Zr=Kr(yr);mr.PostgrestFilterBuilder=Zr.default;const Xr=Kr(wr);mr.PostgrestTransformBuilder=Xr.default;const es=Kr(_r);mr.PostgrestBuilder=es.default;const ts=Kr(Ar);mr.PostgrestError=ts.default;var rs=mr.default={PostgrestClient:Qr.default,PostgrestQueryBuilder:Yr.default,PostgrestFilterBuilder:Zr.default,PostgrestTransformBuilder:Xr.default,PostgrestBuilder:es.default,PostgrestError:ts.default};const{PostgrestClient:ss,PostgrestQueryBuilder:as,PostgrestFilterBuilder:ns,PostgrestTransformBuilder:os,PostgrestBuilder:is,PostgrestError:ls}=rs;const cs=function(){if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}();var us,ds,hs,fs,ps,ms,vs,gs,ys,ws,_s;(ds=us||(us={}))[ds.connecting=0]="connecting",ds[ds.open=1]="open",ds[ds.closing=2]="closing",ds[ds.closed=3]="closed",(fs=hs||(hs={})).closed="closed",fs.errored="errored",fs.joined="joined",fs.joining="joining",fs.leaving="leaving",(ms=ps||(ps={})).close="phx_close",ms.error="phx_error",ms.join="phx_join",ms.reply="phx_reply",ms.leave="phx_leave",ms.access_token="access_token",(vs||(vs={})).websocket="websocket",(ys=gs||(gs={})).Connecting="connecting",ys.Open="open",ys.Closing="closing",ys.Closed="closed";class bs{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const s=t.getUint8(1),a=t.getUint8(2);let n=this.HEADER_LENGTH+2;const o=r.decode(e.slice(n,n+s));n+=s;const i=r.decode(e.slice(n,n+a));n+=a;return{ref:null,topic:o,event:i,payload:JSON.parse(r.decode(e.slice(n,e.byteLength)))}}}class ks{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(_s=ws||(ws={})).abstime="abstime",_s.bool="bool",_s.date="date",_s.daterange="daterange",_s.float4="float4",_s.float8="float8",_s.int2="int2",_s.int4="int4",_s.int4range="int4range",_s.int8="int8",_s.int8range="int8range",_s.json="json",_s.jsonb="jsonb",_s.money="money",_s.numeric="numeric",_s.oid="oid",_s.reltime="reltime",_s.text="text",_s.time="time",_s.timestamp="timestamp",_s.timestamptz="timestamptz",_s.timetz="timetz",_s.tsrange="tsrange",_s.tstzrange="tstzrange";const Ss=(e,t,r={})=>{var s;const a=null!==(s=r.skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce((r,s)=>(r[s]=Es(s,e,t,a),r),{})},Es=(e,t,r,s)=>{const a=t.find(t=>t.name===e),n=null==a?void 0:a.type,o=r[e];return n&&!s.includes(n)?Ts(n,o):Os(o)},Ts=(e,t)=>{if("_"===e.charAt(0)){const r=e.slice(1,e.length);return js(t,r)}switch(e){case ws.bool:return Cs(t);case ws.float4:case ws.float8:case ws.int2:case ws.int4:case ws.int8:case ws.numeric:case ws.oid:return Ps(t);case ws.json:case ws.jsonb:return As(t);case ws.timestamp:return Is(t);case ws.abstime:case ws.date:case ws.daterange:case ws.int4range:case ws.int8range:case ws.money:case ws.reltime:case ws.text:case ws.time:case ws.timestamptz:case ws.timetz:case ws.tsrange:case ws.tstzrange:default:return Os(t)}},Os=e=>e,Cs=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Ps=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},As=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},js=(e,t)=>{if("string"!=typeof e)return e;const r=e.length-1,s=e[r];if("{"===e[0]&&"}"===s){let s;const n=e.slice(1,r);try{s=JSON.parse("["+n+"]")}catch(a){s=n?n.split(","):[]}return s.map(e=>Ts(t,e))}return e},Is=e=>"string"==typeof e?e.replace(" ","T"):e,Rs=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class xs{constructor(e,t,r={},s=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var $s,Ls,Us,Ds,qs,Ns,Ms,Bs;(Ls=$s||($s={})).SYNC="sync",Ls.JOIN="join",Ls.LEAVE="leave";class Fs{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Fs.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=Fs.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=Fs.syncDiff(this.state,e,t,r),s())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){const a=this.cloneDeep(e),n=this.transformState(t),o={},i={};return this.map(a,(e,t)=>{n[e]||(i[e]=t)}),this.map(n,(e,t)=>{const r=a[e];if(r){const s=t.map(e=>e.presence_ref),a=r.map(e=>e.presence_ref),n=t.filter(e=>a.indexOf(e.presence_ref)<0),l=r.filter(e=>s.indexOf(e.presence_ref)<0);n.length>0&&(o[e]=n),l.length>0&&(i[e]=l)}else o[e]=t}),this.syncDiff(a,{joins:o,leaves:i},r,s)}static syncDiff(e,t,r,s){const{joins:a,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(a,(t,s)=>{var a;const n=null!==(a=e[t])&&void 0!==a?a:[];if(e[t]=this.cloneDeep(s),n.length>0){const r=e[t].map(e=>e.presence_ref),s=n.filter(e=>r.indexOf(e.presence_ref)<0);e[t].unshift(...s)}r(t,n,s)}),this.map(n,(t,r)=>{let a=e[t];if(!a)return;const n=r.map(e=>e.presence_ref);a=a.filter(e=>n.indexOf(e.presence_ref)<0),e[t]=a,s(t,a,r),0===a.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const s=e[r];return t[r]="metas"in s?s.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(Ds=Us||(Us={})).ALL="*",Ds.INSERT="INSERT",Ds.UPDATE="UPDATE",Ds.DELETE="DELETE",(Ns=qs||(qs={})).BROADCAST="broadcast",Ns.PRESENCE="presence",Ns.POSTGRES_CHANGES="postgres_changes",Ns.SYSTEM="system",(Bs=Ms||(Ms={})).SUBSCRIBED="SUBSCRIBED",Bs.TIMED_OUT="TIMED_OUT",Bs.CLOSED="CLOSED",Bs.CHANNEL_ERROR="CHANNEL_ERROR";class Vs{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=hs.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new xs(this,ps.join,this.params,this.timeout),this.rejoinTimer=new ks(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=hs.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=hs.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=hs.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=hs.errored,this.rejoinTimer.scheduleTimeout())}),this._on(ps.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new Fs(this),this.broadcastEndpointURL=Rs(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.state==hs.closed){const{config:{broadcast:a,presence:n,private:o}}=this.params;this._onError(t=>null==e?void 0:e(Ms.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(Ms.CLOSED));const i={},l={broadcast:a,presence:n,postgres_changes:null!==(s=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==s?s:[],private:o};this.socket.accessTokenValue&&(i.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},i)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0!==t){const s=this.bindings.postgres_changes,a=null!==(r=null==s?void 0:s.length)&&void 0!==r?r:0,n=[];for(let r=0;r<a;r++){const a=s[r],{filter:{event:o,schema:i,table:l,filter:c}}=a,u=t&&t[r];if(!u||u.event!==o||u.schema!==i||u.table!==l||u.filter!==c)return this.unsubscribe(),this.state=hs.errored,void(null==e||e(Ms.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));n.push(Object.assign(Object.assign({},a),{id:u.id}))}return this.bindings.postgres_changes=n,void(e&&e(Ms.SUBSCRIBED))}null==e||e(Ms.SUBSCRIBED)}).receive("error",t=>{this.state=hs.errored,null==e||e(Ms.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(Ms.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,s;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var s,a,n;const o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(n=null===(a=null===(s=this.params)||void 0===s?void 0:s.config)||void 0===a?void 0:a.broadcast)||void 0===n?void 0:n.ack)||r("ok"),o.receive("ok",()=>r("ok")),o.receive("error",()=>r("error")),o.receive("timeout",()=>r("timed out"))});{const{event:n,payload:o}=e,i={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:n,payload:o,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,i,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await(null===(s=e.body)||void 0===s?void 0:s.cancel()),e.ok?"ok":"error"}catch(a){return"AbortError"===a.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=hs.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(ps.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(s=>{r=new xs(this,ps.leave,{},e),r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{null==r||r.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){const s=new AbortController,a=setTimeout(()=>s.abort(),r),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:s.signal}));return clearTimeout(a),n}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new xs(this,e,t,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,a;const n=e.toLocaleLowerCase(),{close:o,error:i,leave:l,join:c}=ps;if(r&&[o,i,l,c].indexOf(n)>=0&&r!==this._joinRef())return;let u=this._onMessage(n,t,r);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null===(s=this.bindings.postgres_changes)||void 0===s||s.filter(e=>{var t,r,s;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(s=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===s?void 0:s.toLocaleLowerCase())===n}).map(e=>e.callback(u,r)):null===(a=this.bindings[n])||void 0===a||a.filter(e=>{var r,s,a,o,i,l;if(["broadcast","presence","postgres_changes"].includes(n)){if("id"in e){const n=e.id,o=null===(r=e.filter)||void 0===r?void 0:r.event;return n&&(null===(s=t.ids)||void 0===s?void 0:s.includes(n))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null===(a=t.data)||void 0===a?void 0:a.type.toLocaleLowerCase()))}{const r=null===(i=null===(o=null==e?void 0:e.filter)||void 0===o?void 0:o.event)||void 0===i?void 0:i.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===n}).map(e=>{if("object"==typeof u&&"ids"in u){const e=u.data,{schema:t,table:r,commit_timestamp:s,type:a,errors:n}=e,o={schema:t,table:r,commit_timestamp:s,eventType:a,new:{},old:{},errors:n};u=Object.assign(Object.assign({},o),this._getPayloadRecords(e))}e.callback(u,r)})}_isClosed(){return this.state===hs.closed}_isJoined(){return this.state===hs.joined}_isJoining(){return this.state===hs.joining}_isLeaving(){return this.state===hs.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const s=e.toLocaleLowerCase(),a={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(a):this.bindings[s]=[a],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var s;return!((null===(s=e.type)||void 0===s?void 0:s.toLocaleLowerCase())===r&&Vs.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(ps.close,{},e)}_onError(e){this._on(ps.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=hs.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=Ss(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=Ss(e.columns,e.old_record)),t}}const zs=()=>{};class Gs{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=zs,this.ref=0,this.logger=zs,this.conn=null,this.sendBuffer=[],this.serializer=new bs,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ar(async()=>{const{default:e}=await import("./index-Bj0i_E7S.js");return{default:e}},__vite__mapDeps([0,1]),import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${vs.websocket}`,this.httpEndpoint=Rs(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const s=null===(r=null==t?void 0:t.params)||void 0===r?void 0:r.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new ks(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=cs),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case us.connecting:return gs.Connecting;case us.open:return gs.Open;case us.closing:return gs.Closing;default:return gs.Closed}}isConnected(){return this.connectionState()===gs.Open}channel(e,t={config:{}}){const r=`realtime:${e}`,s=this.getChannels().find(e=>e.topic===r);if(s)return s;{const r=new Vs(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){const{topic:t,event:r,payload:s,ref:a}=e,n=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${a})`,s),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{const r={access_token:t,version:"realtime-js/2.11.15"};t&&e.updateJoinPayload(r),e.joinedOnce&&e._isJoined()&&e._push(ps.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:s,ref:a}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${t} ${r} ${a&&"("+a+")"||""}`,s),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,s,a)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(ps.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const r=e.match(/\?/)?"&":"?";return`${e}${r}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class Js extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function Ws(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class Hs extends Js{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Ks extends Js{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Qs=function(e,t,r,s){return new(r||(r=Promise))(function(a,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function i(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};const Ys=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ar(async()=>{const{default:e}=await Promise.resolve().then(()=>Cr);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},Zs=e=>{if(Array.isArray(e))return e.map(e=>Zs(e));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([e,r])=>{const s=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[s]=Zs(r)}),t};var Xs=function(e,t,r,s){return new(r||(r=Promise))(function(a,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function i(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};const ea=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ta=(e,t,r)=>Xs(void 0,void 0,void 0,function*(){const s=yield Qs(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield ar(()=>Promise.resolve().then(()=>Cr),void 0,import.meta.url)).Response:Response});e instanceof s&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new Hs(ea(r),e.status||500))}).catch(e=>{t(new Ks(ea(e),e))}):t(new Ks(ea(e),e))});function ra(e,t,r,s,a,n){return Xs(this,void 0,void 0,function*(){return new Promise((o,i)=>{e(r,((e,t,r,s)=>{const a={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?a:(a.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),s&&(a.body=JSON.stringify(s)),Object.assign(Object.assign({},a),r))})(t,s,a,n)).then(e=>{if(!e.ok)throw e;return(null==s?void 0:s.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>ta(e,i,s))})})}function sa(e,t,r,s){return Xs(this,void 0,void 0,function*(){return ra(e,"GET",t,r,s)})}function aa(e,t,r,s,a){return Xs(this,void 0,void 0,function*(){return ra(e,"POST",t,s,a,r)})}function na(e,t,r,s,a){return Xs(this,void 0,void 0,function*(){return ra(e,"DELETE",t,s,a,r)})}var oa=function(e,t,r,s){return new(r||(r=Promise))(function(a,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function i(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};const ia={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},la={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ca{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=Ys(s)}uploadOrUpdate(e,t,r,s){return oa(this,void 0,void 0,function*(){try{let a;const n=Object.assign(Object.assign({},la),s);let o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)});const i=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?(a=new FormData,a.append("cacheControl",n.cacheControl),i&&a.append("metadata",this.encodeMetadata(i)),a.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(a=r,a.append("cacheControl",n.cacheControl),i&&a.append("metadata",this.encodeMetadata(i))):(a=r,o["cache-control"]=`max-age=${n.cacheControl}`,o["content-type"]=n.contentType,i&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(i)))),(null==s?void 0:s.headers)&&(o=Object.assign(Object.assign({},o),s.headers));const l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:a,headers:o},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),d=yield u.json();if(u.ok)return{data:{path:l,id:d.Id,fullPath:d.Key},error:null};return{data:null,error:d}}catch(a){if(Ws(a))return{data:null,error:a};throw a}})}upload(e,t,r){return oa(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,s){return oa(this,void 0,void 0,function*(){const a=this._removeEmptyFolders(e),n=this._getFinalPath(a),o=new URL(this.url+`/object/upload/sign/${n}`);o.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:la.upsert},s),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r,e.append("cacheControl",t.cacheControl)):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);const i=yield this.fetch(o.toString(),{method:"PUT",body:e,headers:n}),l=yield i.json();if(i.ok)return{data:{path:a,fullPath:l.Key},error:null};return{data:null,error:l}}catch(i){if(Ws(i))return{data:null,error:i};throw i}})}createSignedUploadUrl(e,t){return oa(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e);const s=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(s["x-upsert"]="true");const a=yield aa(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),n=new URL(this.url+a.url),o=n.searchParams.get("token");if(!o)throw new Js("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:o},error:null}}catch(r){if(Ws(r))return{data:null,error:r};throw r}})}update(e,t,r){return oa(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return oa(this,void 0,void 0,function*(){try{return{data:yield aa(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(Ws(s))return{data:null,error:s};throw s}})}copy(e,t,r){return oa(this,void 0,void 0,function*(){try{return{data:{path:(yield aa(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(Ws(s))return{data:null,error:s};throw s}})}createSignedUrl(e,t,r){return oa(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),a=yield aa(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});const n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return a={signedUrl:encodeURI(`${this.url}${a.signedURL}${n}`)},{data:a,error:null}}catch(s){if(Ws(s))return{data:null,error:s};throw s}})}createSignedUrls(e,t,r){return oa(this,void 0,void 0,function*(){try{const s=yield aa(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),a=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${a}`):null})),error:null}}catch(s){if(Ws(s))return{data:null,error:s};throw s}})}download(e,t){return oa(this,void 0,void 0,function*(){const r=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),a=s?`?${s}`:"";try{const t=this._getFinalPath(e),s=yield sa(this.fetch,`${this.url}/${r}/${t}${a}`,{headers:this.headers,noResolveJson:!0});return{data:yield s.blob(),error:null}}catch(n){if(Ws(n))return{data:null,error:n};throw n}})}info(e){return oa(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield sa(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Zs(e),error:null}}catch(r){if(Ws(r))return{data:null,error:r};throw r}})}exists(e){return oa(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,r,s){return Xs(this,void 0,void 0,function*(){return ra(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),s)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(Ws(r)&&r instanceof Ks){const e=r.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:r}}throw r}})}getPublicUrl(e,t){const r=this._getFinalPath(e),s=[],a=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==a&&s.push(a);const n=void 0!==(null==t?void 0:t.transform)?"render/image":"object",o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&s.push(o);let i=s.join("&");return""!==i&&(i=`?${i}`),{data:{publicUrl:encodeURI(`${this.url}/${n}/public/${r}${i}`)}}}remove(e){return oa(this,void 0,void 0,function*(){try{return{data:yield na(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(Ws(t))return{data:null,error:t};throw t}})}list(e,t,r){return oa(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},ia),t),{prefix:e||""});return{data:yield aa(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(Ws(s))return{data:null,error:s};throw s}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const ua={"X-Client-Info":"storage-js/2.7.1"};var da=function(e,t,r,s){return new(r||(r=Promise))(function(a,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function i(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};class ha{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},ua),t),this.fetch=Ys(r)}listBuckets(){return da(this,void 0,void 0,function*(){try{return{data:yield sa(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(Ws(e))return{data:null,error:e};throw e}})}getBucket(e){return da(this,void 0,void 0,function*(){try{return{data:yield sa(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(Ws(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return da(this,void 0,void 0,function*(){try{return{data:yield aa(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(Ws(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return da(this,void 0,void 0,function*(){try{const r=yield function(e,t,r,s,a){return Xs(this,void 0,void 0,function*(){return ra(e,"PUT",t,s,a,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:r,error:null}}catch(r){if(Ws(r))return{data:null,error:r};throw r}})}emptyBucket(e){return da(this,void 0,void 0,function*(){try{return{data:yield aa(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(Ws(t))return{data:null,error:t};throw t}})}deleteBucket(e){return da(this,void 0,void 0,function*(){try{return{data:yield na(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(Ws(t))return{data:null,error:t};throw t}})}}class fa extends ha{constructor(e,t={},r){super(e,t,r)}from(e){return new ca(this.url,this.headers,e,this.fetch)}}let pa="";pa="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const ma={headers:{"X-Client-Info":`supabase-js-${pa}/2.50.2`}},va={schema:"public"},ga={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ya={};var wa=function(e,t,r,s){return new(r||(r=Promise))(function(a,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function i(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};const _a=(e,t,r)=>{const s=(e=>{let t;return t=e||("undefined"==typeof fetch?Sr:fetch),(...e)=>t(...e)})(r),a="undefined"==typeof Headers?Er:Headers;return(r,n)=>wa(void 0,void 0,void 0,function*(){var o;const i=null!==(o=yield t())&&void 0!==o?o:e;let l=new a(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${i}`),s(r,Object.assign(Object.assign({},n),{headers:l}))})};var ba=function(e,t,r,s){return new(r||(r=Promise))(function(a,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function i(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};const ka="2.70.0",Sa=3e4,Ea=9e4,Ta={"X-Client-Info":`gotrue-js/${ka}`},Oa="X-Supabase-Api-Version",Ca={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},Pa=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class Aa extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function ja(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class Ia extends Aa{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class Ra extends Aa{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class xa extends Aa{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}}class $a extends xa{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class La extends xa{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Ua extends xa{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Da extends xa{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class qa extends xa{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Na extends xa{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Ma(e){return ja(e)&&"AuthRetryableFetchError"===e.name}class Ba extends xa{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class Fa extends xa{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Va="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),za=" \t\n\r=".split(""),Ga=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<za.length;t+=1)e[za[t].charCodeAt(0)]=-2;for(let t=0;t<Va.length;t+=1)e[Va[t].charCodeAt(0)]=t;return e})();function Ja(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(Va[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(Va[e]),t.queuedBits-=6}}function Wa(e,t,r){const s=Ga[e];if(!(s>-1)){if(-2===s)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function Ha(e){const t=[],r=e=>{t.push(String.fromCodePoint(e))},s={utf8seq:0,codepoint:0},a={queue:0,queuedBits:0},n=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return void r(e);for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,s,r)};for(let o=0;o<e.length;o+=1)Wa(e.charCodeAt(o),a,n);return t.join("")}function Ka(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function Qa(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};for(let a=0;a<e.length;a+=1)Wa(e.charCodeAt(a),r,s);return new Uint8Array(t)}function Ya(e){const t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){const t=1024*(s-55296)&65535;s=65536+(e.charCodeAt(r+1)-56320&65535|t),r+=1}Ka(s,t)}}(e,e=>t.push(e)),new Uint8Array(t)}function Za(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};return e.forEach(e=>Ja(e,r,s)),Ja(null,r,s),t.join("")}const Xa=()=>"undefined"!=typeof window&&"undefined"!=typeof document,en={tested:!1,writable:!1},tn=()=>{if(!Xa())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(en.tested)return en.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),en.tested=!0,en.writable=!0}catch(t){en.tested=!0,en.writable=!1}return en.writable};const rn=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ar(async()=>{const{default:e}=await Promise.resolve().then(()=>Cr);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},sn=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},an=async(e,t)=>{const r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(s){return r}},nn=async(e,t)=>{await e.removeItem(t)};class on{constructor(){this.promise=new on.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function ln(e){const t=e.split(".");if(3!==t.length)throw new Fa("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!Pa.test(t[r]))throw new Fa("JWT not in base64url format");return{header:JSON.parse(Ha(t[0])),payload:JSON.parse(Ha(t[1])),signature:Qa(t[2]),raw:{header:t[0],payload:t[1]}}}function cn(e){return("0"+e.toString(16)).substr(-2)}async function un(e){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),r=await crypto.subtle.digest("SHA-256",t),s=new Uint8Array(r);return Array.from(s).map(e=>String.fromCharCode(e)).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function dn(e,t,r=!1){const s=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let r="";for(let s=0;s<56;s++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,cn).join("")}();let a=s;r&&(a+="/PASSWORD_RECOVERY"),await sn(e,`${t}-code-verifier`,a);const n=await un(s);return[n,s===n?"plain":"s256"]}on.promiseConstructor=Promise;const hn=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const fn=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function pn(e){if(!fn.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}const mn=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),vn=[502,503,504];async function gn(e){var t,r;if(!("object"==typeof(r=e)&&null!==r&&"status"in r&&"ok"in r&&"json"in r&&"function"==typeof r.json))throw new Na(mn(e),0);if(vn.includes(e.status))throw new Na(mn(e),e.status);let s,a;try{s=await e.json()}catch(o){throw new Ra(mn(o),o)}const n=function(e){const t=e.headers.get(Oa);if(!t)return null;if(!t.match(hn))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(o){return null}}(e);if(n&&n.getTime()>=Ca.timestamp&&"object"==typeof s&&s&&"string"==typeof s.code?a=s.code:"object"==typeof s&&s&&"string"==typeof s.error_code&&(a=s.error_code),a){if("weak_password"===a)throw new Ba(mn(s),e.status,(null===(t=s.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===a)throw new $a}else if("object"==typeof s&&s&&"object"==typeof s.weak_password&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new Ba(mn(s),e.status,s.weak_password.reasons);throw new Ia(mn(s),e.status||500,a)}async function yn(e,t,r,s){var a;const n=Object.assign({},null==s?void 0:s.headers);n[Oa]||(n[Oa]=Ca.name),(null==s?void 0:s.jwt)&&(n.Authorization=`Bearer ${s.jwt}`);const o=null!==(a=null==s?void 0:s.query)&&void 0!==a?a:{};(null==s?void 0:s.redirectTo)&&(o.redirect_to=s.redirectTo);const i=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await async function(e,t,r,s,a,n){const o=((e,t,r,s)=>{const a={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?a:(a.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),a.body=JSON.stringify(s),Object.assign(Object.assign({},a),r))})(t,s,a,n);let i;try{i=await e(r,Object.assign({},o))}catch(l){throw console.error(l),new Na(mn(l),0)}i.ok||await gn(i);if(null==s?void 0:s.noResolveJson)return i;try{return await i.json()}catch(l){await gn(l)}}(e,t,r+i,{headers:n,noResolveJson:null==s?void 0:s.noResolveJson},{},null==s?void 0:s.body);return(null==s?void 0:s.xform)?null==s?void 0:s.xform(l):{data:Object.assign({},l),error:null}}function wn(e){var t;let r=null;var s;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=(s=e.expires_in,Math.round(Date.now()/1e3)+s)));return{data:{session:r,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function _n(e){const t=wn(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function bn(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function kn(e){return{data:e,error:null}}function Sn(e){const{action_link:t,email_otp:r,hashed_token:s,redirect_to:a,verification_type:n}=e,o=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(s=Object.getOwnPropertySymbols(e);a<s.length;a++)t.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]])}return r}(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:r,hashed_token:s,redirect_to:a,verification_type:n},user:Object.assign({},o)},error:null}}function En(e){return e}const Tn=["global","local","others"];class On{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=rn(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=Tn[0]){if(Tn.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Tn.join(", ")}`);try{return await yn(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(r){if(ja(r))return{data:null,error:r};throw r}}async inviteUserByEmail(e,t={}){try{return await yn(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:bn})}catch(r){if(ja(r))return{data:{user:null},error:r};throw r}}async generateLink(e){try{const{options:t}=e,r=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(s=Object.getOwnPropertySymbols(e);a<s.length;a++)t.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]])}return r}(e,["options"]),s=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(s.new_email=null==r?void 0:r.newEmail,delete s.newEmail),await yn(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:Sn,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(ja(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await yn(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:bn})}catch(t){if(ja(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,r,s,a,n,o,i;try{const l={nextPage:null,lastPage:0,total:0},c=await yn(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(a=null===(s=null==e?void 0:e.perPage)||void 0===s?void 0:s.toString())&&void 0!==a?a:""},xform:En});if(c.error)throw c.error;const u=await c.json(),d=null!==(n=c.headers.get("x-total-count"))&&void 0!==n?n:0,h=null!==(i=null===(o=c.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==i?i:[];return h.length>0&&(h.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(d)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(l){if(ja(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){pn(e);try{return await yn(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:bn})}catch(t){if(ja(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){pn(e);try{return await yn(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:bn})}catch(r){if(ja(r))return{data:{user:null},error:r};throw r}}async deleteUser(e,t=!1){pn(e);try{return await yn(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:bn})}catch(r){if(ja(r))return{data:{user:null},error:r};throw r}}async _listFactors(e){pn(e.userId);try{const{data:t,error:r}=await yn(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(t){if(ja(t))return{data:null,error:t};throw t}}async _deleteFactor(e){pn(e.userId),pn(e.id);try{return{data:await yn(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(ja(t))return{data:null,error:t};throw t}}}const Cn={getItem:e=>tn()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{tn()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{tn()&&globalThis.localStorage.removeItem(e)}};function Pn(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}const An=!!(globalThis&&tn()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class jn extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class In extends jn{}async function Rn(e,t,r){An&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const s=new globalThis.AbortController;return t>0&&setTimeout(()=>{s.abort(),An&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},async s=>{if(!s){if(0===t)throw An&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new In(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(An)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(a){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",a)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}An&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await r()}finally{An&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const xn={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Ta,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function $n(e,t,r){return await r()}class Ln{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Ln.nextInstanceID,Ln.nextInstanceID+=1,this.instanceID>0&&Xa()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},xn),e);if(this.logDebugMessages=!!s.debug,"function"==typeof s.debug&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new On({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=rn(s.fetch),this.lock=s.lock||$n,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:Xa()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=Rn:this.lock=$n,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:tn()?this.storage=Cn:(this.memoryStorage={},this.storage=Pn(this.memoryStorage)):(this.memoryStorage={},this.storage=Pn(this.memoryStorage)),Xa()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(a){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",a)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${ka}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(s){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href);let r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),Xa()&&this.detectSessionInUrl&&"none"!==r){const{data:s,error:a}=await this._getSessionFromURL(t,r);if(a){if(this._debug("#_initialize()","error detecting session from URL",a),function(e){return ja(e)&&"AuthImplicitGrantRedirectError"===e.name}(a)){const t=null===(e=a.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:a}}return await this._removeSession(),{error:a}}const{session:n,redirectType:o}=s;return this._debug("#_initialize()","detected session in URL",n,"redirect type",o),await this._saveSession(n),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return ja(t)?{error:t}:{error:new Ra("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,s;try{const a=await yn(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken}},xform:wn}),{data:n,error:o}=a;if(o||!n)return{data:{user:null,session:null},error:o};const i=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",i)),{data:{user:l,session:i},error:null}}catch(a){if(ja(a))return{data:{user:null,session:null},error:a};throw a}}async signUp(e){var t,r,s;try{let a;if("email"in e){const{email:r,password:s,options:n}=e;let o=null,i=null;"pkce"===this.flowType&&([o,i]=await dn(this.storage,this.storageKey)),a=await yn(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:s,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:o,code_challenge_method:i},xform:wn})}else{if(!("phone"in e))throw new Ua("You must provide either an email or phone number and a password");{const{phone:t,password:n,options:o}=e;a=await yn(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!==(r=null==o?void 0:o.data)&&void 0!==r?r:{},channel:null!==(s=null==o?void 0:o.channel)&&void 0!==s?s:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:wn})}}const{data:n,error:o}=a;if(o||!n)return{data:{user:null,session:null},error:o};const i=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",i)),{data:{user:l,session:i},error:null}}catch(a){if(ja(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithPassword(e){try{let t;if("email"in e){const{email:r,password:s,options:a}=e;t=await yn(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:s,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:_n})}else{if(!("phone"in e))throw new Ua("You must provide either an email or phone number and a password");{const{phone:r,password:s,options:a}=e;t=await yn(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:s,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:_n})}}const{data:r,error:s}=t;return s?{data:{user:null,session:null},error:s}:r&&r.session&&r.user?(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s}):{data:{user:null,session:null},error:new La}}catch(t){if(ja(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,r,s,a;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:null===(a=e.options)||void 0===a?void 0:a.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,s,a,n,o,i,l,c,u,d,h;let f,p;if("message"in e)f=e.message,p=e.signature;else{const{chain:d,wallet:h,statement:m,options:v}=e;let g;if(Xa())if("object"==typeof h)g=h;else{const e=window;if(!("solana"in e)||"object"!=typeof e.solana||!("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");g=e.solana}else{if("object"!=typeof h||!(null==v?void 0:v.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");g=h}const y=new URL(null!==(t=null==v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in g&&g.signIn){const e=await g.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null==v?void 0:v.signInWithSolana),{version:"1",domain:y.host,uri:y.href}),m?{statement:m}:null));let t;if(Array.isArray(e)&&e[0]&&"object"==typeof e[0])t=e[0];else{if(!(e&&"object"==typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"==typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"==typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in g&&"function"==typeof g.signMessage&&"publicKey"in g&&"object"==typeof g&&g.publicKey&&"toBase58"in g.publicKey&&"function"==typeof g.publicKey.toBase58))throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${y.host} wants you to sign in with your Solana account:`,g.publicKey.toBase58(),...m?["",m,""]:[""],"Version: 1",`URI: ${y.href}`,`Issued At: ${null!==(s=null===(r=null==v?void 0:v.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==s?s:(new Date).toISOString()}`,...(null===(a=null==v?void 0:v.signInWithSolana)||void 0===a?void 0:a.notBefore)?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...(null===(n=null==v?void 0:v.signInWithSolana)||void 0===n?void 0:n.expirationTime)?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...(null===(o=null==v?void 0:v.signInWithSolana)||void 0===o?void 0:o.chainId)?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...(null===(i=null==v?void 0:v.signInWithSolana)||void 0===i?void 0:i.nonce)?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...(null===(l=null==v?void 0:v.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...(null===(u=null===(c=null==v?void 0:v.signInWithSolana)||void 0===c?void 0:c.resources)||void 0===u?void 0:u.length)?["Resources",...v.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");const e=await g.signMessage((new TextEncoder).encode(f),"utf8");if(!(e&&e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:r}=await yn(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:Za(p)},(null===(d=e.options)||void 0===d?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:null===(h=e.options)||void 0===h?void 0:h.captchaToken}}:null),xform:wn});if(r)throw r;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}):{data:{user:null,session:null},error:new La}}catch(m){if(ja(m))return{data:{user:null,session:null},error:m};throw m}}async _exchangeCodeForSession(e){const t=await an(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(null!=t?t:"").split("/");try{const{data:t,error:a}=await yn(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:wn});if(await nn(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=s?s:null}),error:a}):{data:{user:null,session:null,redirectType:null},error:new La}}catch(a){if(ja(a))return{data:{user:null,session:null,redirectType:null},error:a};throw a}}async signInWithIdToken(e){try{const{options:t,provider:r,token:s,access_token:a,nonce:n}=e,o=await yn(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:a,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:wn}),{data:i,error:l}=o;return l?{data:{user:null,session:null},error:l}:i&&i.session&&i.user?(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:i,error:l}):{data:{user:null,session:null},error:new La}}catch(t){if(ja(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,r,s,a,n;try{if("email"in e){const{email:s,options:a}=e;let n=null,o=null;"pkce"===this.flowType&&([n,o]=await dn(this.storage,this.storageKey));const{error:i}=await yn(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:{},create_user:null===(r=null==a?void 0:a.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken},code_challenge:n,code_challenge_method:o},redirectTo:null==a?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in e){const{phone:t,options:r}=e,{data:o,error:i}=await yn(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(s=null==r?void 0:r.data)&&void 0!==s?s:{},create_user:null===(a=null==r?void 0:r.shouldCreateUser)||void 0===a||a,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(n=null==r?void 0:r.channel)&&void 0!==n?n:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:i}}throw new Ua("You must provide either an email or phone number.")}catch(o){if(ja(o))return{data:{user:null,session:null},error:o};throw o}}async verifyOtp(e){var t,r;try{let s,a;"options"in e&&(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo,a=null===(r=e.options)||void 0===r?void 0:r.captchaToken);const{data:n,error:o}=await yn(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:a}}),redirectTo:s,xform:wn});if(o)throw o;if(!n)throw new Error("An error occurred on token verification.");const i=n.session,l=n.user;return(null==i?void 0:i.access_token)&&(await this._saveSession(i),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",i)),{data:{user:l,session:i},error:null}}catch(s){if(ja(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(e){var t,r,s;try{let a=null,n=null;return"pkce"===this.flowType&&([a,n]=await dn(this.storage,this.storageKey)),await yn(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:a,code_challenge_method:n}),headers:this.headers,xform:kn})}catch(a){if(ja(a))return{data:null,error:a};throw a}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new $a;const{error:s}=await yn(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:s}})}catch(e){if(ja(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:r,type:s,options:a}=e,{error:n}=await yn(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},redirectTo:null==a?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){const{phone:r,type:s,options:a}=e,{data:n,error:o}=await yn(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:o}}throw new Ua("You must provide either an email or phone number and a type")}catch(t){if(ja(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await an(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const r=!!e.expires_at&&1e3*e.expires_at-Date.now()<Ea;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,s)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,s))})}return{data:{session:e},error:null}}const{session:s,error:a}=await this._callRefreshToken(e.refresh_token);return a?{data:{session:null},error:a}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,async()=>await this._getUser())}async _getUser(e){try{return e?await yn(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:bn}):await this._useSession(async e=>{var t,r,s;const{data:a,error:n}=e;if(n)throw n;return(null===(t=a.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await yn(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(s=null===(r=a.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0,xform:bn}):{data:{user:null},error:new $a}})}catch(t){if(ja(t))return function(e){return ja(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await nn(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{const{data:s,error:a}=r;if(a)throw a;if(!s.session)throw new $a;const n=s.session;let o=null,i=null;"pkce"===this.flowType&&null!=e.email&&([o,i]=await dn(this.storage,this.storageKey));const{data:l,error:c}=await yn(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:i}),jwt:n.access_token,xform:bn});if(c)throw c;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(r){if(ja(r))return{data:{user:null},error:r};throw r}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new $a;const t=Date.now()/1e3;let r=t,s=!0,a=null;const{payload:n}=ln(e.access_token);if(n.exp&&(r=n.exp,s=r<=t),s){const{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};a=t}else{const{data:s,error:n}=await this._getUser(e.access_token);if(n)throw n;a={access_token:e.access_token,refresh_token:e.refresh_token,user:s.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(a),await this._notifyAllSubscribers("SIGNED_IN",a)}return{data:{user:a.user,session:a},error:null}}catch(t){if(ja(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){const{data:s,error:a}=t;if(a)throw a;e=null!==(r=s.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new $a;const{session:s,error:a}=await this._callRefreshToken(e.refresh_token);return a?{data:{user:null,session:null},error:a}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(ja(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!Xa())throw new Da("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Da(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new qa("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new Da("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new qa("No code detected.");const{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;const s=new URL(window.location.href);return s.searchParams.delete("code"),window.history.replaceState(window.history.state,"",s.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:s,access_token:a,refresh_token:n,expires_in:o,expires_at:i,token_type:l}=e;if(!(a&&o&&n&&l))throw new Da("No session defined in URL");const c=Math.round(Date.now()/1e3),u=parseInt(o);let d=c+u;i&&(d=parseInt(i));const h=d-c;1e3*h<=Sa&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${h}s, should have been closer to ${u}s`);const f=d-u;c-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,d,c):c-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,d,c);const{data:p,error:m}=await this._getUser(a);if(m)throw m;const v={provider_token:r,provider_refresh_token:s,access_token:a,expires_in:u,expires_at:d,refresh_token:n,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:e.type},error:null}}catch(r){if(ja(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await an(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;const{data:s,error:a}=t;if(a)return{error:a};const n=null===(r=s.session)||void 0===r?void 0:r.access_token;if(n){const{error:t}=await this.admin.signOut(n,e);if(t&&(!function(e){return ja(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await nn(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,s;try{const{data:{session:s},error:a}=t;if(a)throw a;await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",e,"session",s)}catch(a){await(null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",a),console.error(a)}})}async resetPasswordForEmail(e,t={}){let r=null,s=null;"pkce"===this.flowType&&([r,s]=await dn(this.storage,this.storageKey,!0));try{return await yn(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(a){if(ja(a))return{data:null,error:a};throw a}}async getUserIdentities(){var e;try{const{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(ja(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:r,error:s}=await this._useSession(async t=>{var r,s,a,n,o;const{data:i,error:l}=t;if(l)throw l;const c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(a=e.options)||void 0===a?void 0:a.queryParams,skipBrowserRedirect:!0});return await yn(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(o=null===(n=i.session)||void 0===n?void 0:n.access_token)&&void 0!==o?o:void 0})});if(s)throw s;return Xa()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(r){if(ja(r))return{data:{provider:e.provider,url:null},error:r};throw r}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,s;const{data:a,error:n}=t;if(n)throw n;return await yn(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(s=null===(r=a.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0})})}catch(t){if(ja(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const a=Date.now();return await(r=async r=>(r>0&&await async function(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await yn(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:wn})),s=(e,t)=>{const r=200*Math.pow(2,e);return t&&Ma(t)&&Date.now()+r-a<Sa},new Promise((e,t)=>{(async()=>{for(let n=0;n<1/0;n++)try{const t=await r(n);if(!s(n,null,t))return void e(t)}catch(a){if(!s(n,a))return void t(a)}})()}))}catch(a){if(this._debug(t,"error",a),ja(a))return{data:{session:null,user:null},error:a};throw a}finally{this._debug(t,"end")}var r,s}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),Xa()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const r=await an(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r))return this._debug(t,"session is not valid"),void(null!==r&&await this._removeSession());const s=1e3*(null!==(e=r.expires_at)&&void 0!==e?e:1/0)-Date.now()<Ea;if(this._debug(t,`session has${s?"":" not"} expired with margin of 90000s`),s){if(this.autoRefreshToken&&r.refresh_token){const{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),Ma(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){return this._debug(t,"error",r),void console.error(r)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new $a;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new on;const{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new $a;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const s={session:t.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(a){if(this._debug(s,"error",a),ja(a)){const e={session:null,error:a};return Ma(a)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(a),a}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(e,t,r=!0){const s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});const s=[],a=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(a){s.push(a)}});if(await Promise.all(a),s.length>0){for(let e=0;e<s.length;e+=1)console.error(s[e]);throw s[0]}}finally{this._debug(s,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await sn(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await nn(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&Xa()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),Sa);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async e=>{const{data:{session:r}}=e;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const s=Math.floor((1e3*r.expires_at-t)/Sa);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),s<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(!(e.isAcquireTimeout||e instanceof jn))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Xa()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){const s=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){const[e,t]=await dn(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});s.push(r.toString())}if(null==r?void 0:r.queryParams){const e=new URLSearchParams(r.queryParams);s.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${s.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;const{data:s,error:a}=t;return a?{data:null,error:a}:await yn(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(t){if(ja(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var r,s;const{data:a,error:n}=t;if(n)return{data:null,error:n};const o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:i,error:l}=await yn(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null===(r=null==a?void 0:a.session)||void 0===r?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(s=null==i?void 0:i.totp)||void 0===s?void 0:s.qr_code)&&(i.totp.qr_code=`data:image/svg+xml;utf-8,${i.totp.qr_code}`),{data:i,error:null})})}catch(t){if(ja(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:s,error:a}=t;if(a)return{data:null,error:a};const{data:n,error:o}=await yn(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:o})})}catch(t){if(ja(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:s,error:a}=t;return a?{data:null,error:a}:await yn(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(t){if(ja(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const r=(null==e?void 0:e.factors)||[],s=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),a=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:s,phone:a},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;const{data:{session:s},error:a}=e;if(a)return{data:null,error:a};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:n}=ln(s.access_token);let o=null;n.aal&&(o=n.aal);let i=o;(null!==(r=null===(t=s.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(i="aal2");return{data:{currentLevel:o,nextLevel:i,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r)return r;if(r=this.jwks.keys.find(t=>t.kid===e),r&&this.jwks_cached_at+6e5>Date.now())return r;const{data:s,error:a}=await yn(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(a)throw a;if(!s.keys||0===s.keys.length)throw new Fa("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),r=s.keys.find(t=>t.kid===e),!r)throw new Fa("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}const{header:s,payload:a,signature:n,raw:{header:o,payload:i}}=ln(r);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(a.exp),!s.kid||"HS256"===s.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:a,header:s,signature:n},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(s.alg),c=await this.fetchJwk(s.kid,t),u=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(await crypto.subtle.verify(l,u,n,Ya(`${o}.${i}`))))throw new Fa("Invalid JWT signature");return{data:{claims:a,header:s,signature:n},error:null}}catch(r){if(ja(r))return{data:null,error:r};throw r}}}Ln.nextInstanceID=0;const Un=Ln;class Dn extends Un{constructor(e){super(e)}}var qn=function(e,t,r,s){return new(r||(r=Promise))(function(a,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function i(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};class Nn{constructor(e,t,r){var s,a,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const o=(i=e).endsWith("/")?i:i+"/";var i;const l=new URL(o);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,u=function(e,t){var r,s;const{db:a,auth:n,realtime:o,global:i}=e,{db:l,auth:c,realtime:u,global:d}=t,h={db:Object.assign(Object.assign({},l),a),auth:Object.assign(Object.assign({},c),n),realtime:Object.assign(Object.assign({},u),o),global:Object.assign(Object.assign(Object.assign({},d),i),{headers:Object.assign(Object.assign({},null!==(r=null==d?void 0:d.headers)&&void 0!==r?r:{}),null!==(s=null==i?void 0:i.headers)&&void 0!==s?s:{})}),accessToken:()=>ba(this,void 0,void 0,function*(){return""})};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}(null!=r?r:{},{db:va,realtime:ya,auth:Object.assign(Object.assign({},ga),{storageKey:c}),global:ma});this.storageKey=null!==(s=u.auth.storageKey)&&void 0!==s?s:"",this.headers=null!==(a=u.global.headers)&&void 0!==a?a:{},u.accessToken?(this.accessToken=u.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(n=u.auth)&&void 0!==n?n:{},this.headers,u.global.fetch),this.fetch=_a(t,this._getAccessToken.bind(this),u.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},u.realtime)),this.rest=new ss(new URL("rest/v1",l).href,{headers:this.headers,schema:u.db.schema,fetch:this.fetch}),u.accessToken||this._listenForAuthEvents()}get functions(){return new hr(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new fa(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return qn(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:a,flowType:n,lock:o,debug:i},l,c){const u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Dn({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:a,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:n,lock:o,debug:i,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new Gs(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===r?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=r}}const Mn=new Nn("https://fytiwsutzgmygfxnqoft.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ5dGl3c3V0emdteWdmeG5xb2Z0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MDM1ODcsImV4cCI6MjA2NjM3OTU4N30.LM9vazR9QCZ4vLC_Q1lJmtCj3pEVqM6vpW4TKzntAQA",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}});const Bn={TOOLS:"tools",CATEGORIES:"categories",PRODUCTS:"products",PRODUCT_CATEGORIES:"product_categories",USERS:"users",USER_PROFILES:"user_profiles",FAVORITES:"favorites",ORDERS:"orders",ORDER_ITEMS:"order_items",PAYMENTS:"payments",REVIEWS:"reviews",TAGS:"tags",TOOL_TAGS:"tool_tags",ANALYTICS:"analytics"},Fn=e=>(console.error("Supabase Error:",e),(null==e?void 0:e.message)?e.message:(null==e?void 0:e.error_description)?e.error_description:"操作失败，请稍后重试");function Vn(e){var t;return(null==(t=e.category)?void 0:t.id)?e.category.id:e.categoryId?e.categoryId:e.category_id?e.category_id:null}class zn{static async getTools(e){try{let t=Mn.from(Bn.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status","active");(null==e?void 0:e.query)&&(t=t.or(`name.ilike.%${e.query}%,description.ilike.%${e.query}%`)),(null==e?void 0:e.category)&&"all"!==e.category&&(t=t.eq("category_id",e.category));const r=(null==e?void 0:e.sortBy)||"sort_order",s=(null==e?void 0:e.sortOrder)||"asc";t=t.order(r,{ascending:"asc"===s});const a=(null==e?void 0:e.page)||1,n=(null==e?void 0:e.limit)||20,o=(a-1)*n;t=t.range(o,o+n-1);const{data:i,error:l,count:c}=await t;if(l)throw new Error(Fn(l));return{items:(i||[]).map(this.transformToolRow),total:c||0,page:a,limit:n,hasMore:(c||0)>o+n}}catch(t){throw console.error("Error fetching tools:",t),t}}static async getTool(e){try{const{data:t,error:r}=await Mn.from(Bn.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("id",e).single();if(r)throw new Error(Fn(r));return this.transformToolRow(t)}catch(t){throw console.error("Error fetching tool:",t),t}}static async createTool(e){try{!function(e,t,r="Entity"){const s=[];for(const a of t)e[a]||s.push(a);if(s.length>0)throw new Error(`${r} validation failed: missing required fields: ${s.join(", ")}`)}(e,["name","description","url"],"Tool");const t=function(e,t="Category"){const r=Vn(e);if(!r)throw new Error(`${t} is required`);return r}(e),{data:r,error:s}=await Mn.from(Bn.TOOLS).insert({name:e.name,description:e.description,url:e.url,category_id:t,icon:e.icon,is_featured:e.isFeatured||!1,status:"active",meta_title:e.metaTitle,meta_description:e.metaDescription,sort_order:e.sortOrder||0}).select("\n          *,\n          category:categories(*)\n        ").single();if(s)throw new Error(Fn(s));return this.transformToolRow(r)}catch(t){throw console.error("Error creating tool:",t),t}}static async updateTool(e,t){try{const r={};t.name&&(r.name=t.name),t.description&&(r.description=t.description),t.url&&(r.url=t.url);const s=Vn(t);s&&(r.category_id=s),void 0!==t.icon&&(r.icon=t.icon),void 0!==t.isFeatured&&(r.is_featured=t.isFeatured),t.status&&(r.status=t.status),void 0!==t.metaTitle&&(r.meta_title=t.metaTitle),void 0!==t.metaDescription&&(r.meta_description=t.metaDescription),void 0!==t.sortOrder&&(r.sort_order=t.sortOrder),r.updated_at=(new Date).toISOString();const{data:a,error:n}=await Mn.from(Bn.TOOLS).update(r).eq("id",e).select("\n          *,\n          category:categories(*)\n        ").single();if(n)throw new Error(Fn(n));return this.transformToolRow(a)}catch(r){throw console.error("Error updating tool:",r),r}}static async deleteTool(e){try{const{error:t}=await Mn.from(Bn.TOOLS).delete().eq("id",e);if(t)throw new Error(Fn(t))}catch(t){throw console.error("Error deleting tool:",t),t}}static async incrementClickCount(e){try{const{data:t,error:r}=await Mn.from(Bn.TOOLS).select("click_count").eq("id",e).single();if(r)throw new Error(Fn(r));const{error:s}=await Mn.from(Bn.TOOLS).update({click_count:((null==t?void 0:t.click_count)||0)+1,updated_at:(new Date).toISOString()}).eq("id",e);if(s)throw new Error(Fn(s))}catch(t){throw console.error("Error incrementing click count:",t),t}}static async getPopularTools(e=10){try{const{data:t,error:r}=await Mn.from(Bn.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status","active").order("click_count",{ascending:!1}).limit(e);if(r)throw new Error(Fn(r));return(t||[]).map(this.transformToolRow)}catch(t){throw console.error("Error fetching popular tools:",t),t}}static async getFeaturedTools(e=6){try{const{data:t,error:r}=await Mn.from(Bn.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status","active").eq("is_featured",!0).order("sort_order",{ascending:!0}).limit(e);if(r)throw new Error(Fn(r));return(t||[]).map(this.transformToolRow)}catch(t){throw console.error("Error fetching featured tools:",t),t}}static async searchTools(e,t=20){try{const{data:r,error:s}=await Mn.from(Bn.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status","active").or(`name.ilike.%${e}%,description.ilike.%${e}%`).order("click_count",{ascending:!1}).limit(t);if(s)throw new Error(Fn(s));return(r||[]).map(this.transformToolRow)}catch(r){throw console.error("Error searching tools:",r),r}}static transformToolRow(e){return{id:e.id,name:e.name,description:e.description,url:e.url,icon:e.icon,category_id:e.category_id,tags:[],isFavorite:!1,click_count:e.click_count,is_featured:e.is_featured,status:e.status,created_at:e.created_at,updated_at:e.updated_at,created_by:e.created_by,meta_title:e.meta_title,meta_description:e.meta_description,sort_order:e.sort_order}}}class Gn{static async getCategories(){try{const{data:e,error:t}=await Mn.from(Bn.CATEGORIES).select("*").eq("is_active",!0).order("sort_order",{ascending:!0});if(t)throw new Error(Fn(t));const r=(e||[]).map(this.transformCategoryRow);return this.buildCategoryTree(r)}catch(e){throw console.error("Error fetching categories:",e),e}}static async getCategory(e){try{const{data:t,error:r}=await Mn.from(Bn.CATEGORIES).select("*").eq("id",e).single();if(r)throw new Error(Fn(r));return this.transformCategoryRow(t)}catch(t){throw console.error("Error fetching category:",t),t}}static async createCategory(e){try{const{data:t,error:r}=await Mn.from(Bn.CATEGORIES).insert({name:e.name,description:e.description,icon:e.icon,color:e.color,parent_id:e.parentId,sort_order:e.sortOrder||0,is_active:!0}).select().single();if(r)throw new Error(Fn(r));return this.transformCategoryRow(t)}catch(t){throw console.error("Error creating category:",t),t}}static async updateCategory(e,t){try{const r={};t.name&&(r.name=t.name),void 0!==t.description&&(r.description=t.description),t.icon&&(r.icon=t.icon),t.color&&(r.color=t.color),void 0!==t.parentId&&(r.parent_id=t.parentId),void 0!==t.sortOrder&&(r.sort_order=t.sortOrder),void 0!==t.isActive&&(r.is_active=t.isActive),r.updated_at=(new Date).toISOString();const{data:s,error:a}=await Mn.from(Bn.CATEGORIES).update(r).eq("id",e).select().single();if(a)throw new Error(Fn(a));return this.transformCategoryRow(s)}catch(r){throw console.error("Error updating category:",r),r}}static async deleteCategory(e){try{const{data:t,error:r}=await Mn.from(Bn.CATEGORIES).select("id").eq("parent_id",e);if(r)throw new Error(Fn(r));if(t&&t.length>0)throw new Error("无法删除包含子分类的分类，请先删除或移动子分类");const{data:s,error:a}=await Mn.from(Bn.TOOLS).select("id").eq("category_id",e);if(a)throw new Error(Fn(a));if(s&&s.length>0)throw new Error("无法删除包含工具的分类，请先删除或移动工具");const{error:n}=await Mn.from(Bn.CATEGORIES).delete().eq("id",e);if(n)throw new Error(Fn(n))}catch(t){throw console.error("Error deleting category:",t),t}}static async getCategoryStats(){try{const{data:e,error:t}=await Mn.from(Bn.TOOLS).select("category_id").eq("status","active");if(t)throw new Error(Fn(t));const r=new Map;return null==e||e.forEach(e=>{const t=r.get(e.category_id)||0;r.set(e.category_id,t+1)}),Array.from(r.entries()).map(([e,t])=>({categoryId:e,count:t}))}catch(e){throw console.error("Error fetching category stats:",e),e}}static async getCategoriesWithStats(){try{const[e,t]=await Promise.all([this.getCategories(),this.getCategoryStats()]),r=new Map(t.map(e=>[e.categoryId,e.count])),s=e=>{var t;const a=r.get(e.id)||0,n=(null==(t=e.children)?void 0:t.map(s))||[],o=n.reduce((e,t)=>e+t.count,0);return{...e,count:a+o,children:n}};return e.map(s)}catch(e){throw console.error("Error fetching categories with stats:",e),e}}static buildCategoryTree(e){const t=new Map,r=[];return e.forEach(e=>{t.set(e.id,{...e,children:[]})}),e.forEach(e=>{const s=t.get(e.id);if(e.parentId){const a=t.get(e.parentId);a?(a.children=a.children||[],a.children.push(s)):r.push(s)}else r.push(s)}),r}static transformCategoryRow(e){return{id:e.id,name:e.name,description:e.description,icon:e.icon,color:e.color,parentId:e.parent_id,count:0,sortOrder:e.sort_order,isActive:e.is_active,createdAt:e.created_at,updatedAt:e.updated_at}}}const Jn=p("tools",()=>{const e=l(""),t=l("all"),r=l(!1),s=l(!1),a=l(!1),n=l(null),i=l(!1),c=l([{id:"1",name:"Visual Studio Code",description:"微软开发的免费代码编辑器，支持多种编程语言和丰富的插件生态",url:"https://code.visualstudio.com/",icon:"📝",category_id:"1",tags:["编辑器","开发","免费","微软"],is_featured:!0,click_count:1250,isFavorite:!1,status:"active",sort_order:1,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),meta_title:"Visual Studio Code - 强大的代码编辑器",meta_description:"微软出品的轻量级但功能强大的代码编辑器，支持多种编程语言和丰富的插件生态"},{id:"2",name:"GitHub",description:"全球最大的代码托管平台，支持Git版本控制和协作开发",url:"https://github.com/",icon:"🐙",category_id:"1",tags:["代码托管","Git","协作","开源"],is_featured:!0,click_count:2100,isFavorite:!1,status:"active",sort_order:2,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),meta_title:"GitHub - 代码托管平台",meta_description:"全球最大的代码托管平台，支持Git版本控制和开源协作"}]),u=l([{id:"1",name:"开发工具",description:"编程开发相关工具",icon:"💻",color:"#3498db",count:5,sort_order:1,is_active:!0,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},{id:"2",name:"设计工具",description:"UI/UX设计工具",icon:"🎨",color:"#e74c3c",count:3,sort_order:2,is_active:!0,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}]),d=o(()=>{let s=c.value;if(e.value){const t=e.value.toLowerCase();s=s.filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.tags.some(e=>e.toLowerCase().includes(t)))}return"all"!==t.value&&(s=s.filter(e=>e.category_id===t.value)),r.value&&(s=s.filter(e=>e.isFavorite)),s}),h=o(()=>c.value.filter(e=>e.isFavorite)),f=o(()=>[...c.value].sort((e,t)=>t.click_count-e.click_count).slice(0,5)),p=o(()=>c.value.filter(e=>e.is_featured)),m=async()=>{try{const e=await zn.getTools();c.value=e.items?e.items:e}catch(e){throw new Error("加载工具失败")}},v=async()=>{try{const e=await Gn.getCategories();u.value=e}catch(e){throw new Error("加载分类失败")}};return{searchQuery:e,selectedCategory:t,showFavoritesOnly:r,sidebarCollapsed:s,loading:a,error:n,initialized:i,tools:c,categories:u,filteredTools:d,favoriteTools:h,popularTools:f,featuredTools:p,initialize:async()=>{if(!i.value)try{a.value=!0,n.value=null,await Promise.all([m(),v()]),i.value=!0}catch(e){n.value=e instanceof Error?e.message:"初始化失败",console.error("Error initializing state:",e)}finally{a.value=!1}},toggleFavorite:async e=>{const t=c.value.find(t=>t.id===e);t&&(t.isFavorite=!t.isFavorite)},incrementClickCount:async e=>{const t=c.value.find(t=>t.id===e);t&&t.click_count++},clearError:()=>{n.value=null},setSearchQuery:t=>e.value=t,setSelectedCategory:e=>t.value=e,setSidebarCollapsed:e=>s.value=e,toggleSidebar:()=>s.value=!s.value}});class Wn{static async getCurrentUser(){try{const{data:{user:e}}=await Mn.auth.getUser();if(!e)return null;const{data:t,error:r}=await Mn.from("user_profiles").select("*").eq("id",e.id).single();if(r){if("PGRST116"===r.code)return this.createUserProfile(e.id,e.email);throw r}return this.transformUser(t)}catch(e){return console.error("获取用户信息失败:",e),null}}static async getUserProfile(e){try{const{data:t,error:r}=await Mn.from("user_profiles").select("*").eq("id",e).single();if(r)throw r;return t?this.transformUser(t):null}catch(t){return console.error("获取用户资料失败:",t),null}}static async updateProfile(e,t){try{const r={full_name:t.fullName,username:t.username,bio:t.bio,website:t.website,location:t.location,updated_at:(new Date).toISOString()};if(t.avatar){const s=await this.uploadAvatar(e,t.avatar);r.avatar_url=s}const{data:s,error:a}=await Mn.from("user_profiles").update(r).eq("id",e).select("*").single();if(a)throw a;if(!s)throw new Error("更新用户资料失败");return this.transformUser(s)}catch(r){throw console.error("更新用户资料失败:",r),new Error("更新用户资料失败")}}static async createUserProfile(e,t){try{const r={id:e,email:t,role:"user",is_active:!0,email_verified:!1},{data:s,error:a}=await Mn.from("user_profiles").insert(r).select("*").single();if(a)throw a;if(!s)throw new Error("创建用户资料失败");return this.transformUser(s)}catch(r){throw console.error("创建用户资料失败:",r),new Error("创建用户资料失败")}}static async uploadAvatar(e,t){try{const r=t.name.split(".").pop(),s=`avatars/${`${e}-${Date.now()}.${r}`}`,{data:a}=await Mn.storage.from("avatars").list("",{search:e});if(a&&a.length>0){const e=a.map(e=>e.name);await Mn.storage.from("avatars").remove(e)}const{error:n}=await Mn.storage.from("avatars").upload(s,t);if(n)throw n;const{data:o}=Mn.storage.from("avatars").getPublicUrl(s);return o.publicUrl}catch(r){throw console.error("上传头像失败:",r),new Error("上传头像失败")}}static async updateLastLogin(e){try{const{error:t}=await Mn.from("user_profiles").update({last_login_at:(new Date).toISOString()}).eq("id",e);if(t)throw t}catch(t){console.error("更新最后登录时间失败:",t)}}static async checkUsernameAvailability(e,t){try{let r=Mn.from("user_profiles").select("id").eq("username",e);t&&(r=r.neq("id",t));const{data:s,error:a}=await r;if(a)throw a;return!s||0===s.length}catch(r){return console.error("检查用户名可用性失败:",r),!1}}static async getUserStats(e){try{const[t,r]=await Promise.all([Mn.from("favorites").select("tool_id, product_id").eq("user_id",e),Mn.from("orders").select("total_amount, status").eq("user_id",e)]),s=t.data||[],a=r.data||[],n=s.filter(e=>e.tool_id).length,o=s.filter(e=>e.product_id).length,i=a.length;return{favoriteToolsCount:n,favoriteProductsCount:o,ordersCount:i,totalSpent:a.filter(e=>"paid"===e.status).reduce((e,t)=>e+t.total_amount,0)}}catch(t){return console.error("获取用户统计信息失败:",t),{favoriteToolsCount:0,favoriteProductsCount:0,ordersCount:0,totalSpent:0}}}static async deleteAccount(e){try{await Promise.all([Mn.from("favorites").delete().eq("user_id",e),Mn.from("orders").delete().eq("user_id",e),Mn.from("user_profiles").delete().eq("id",e)]);const{error:t}=await Mn.auth.admin.deleteUser(e);if(t)throw t}catch(t){throw console.error("删除用户账户失败:",t),new Error("删除用户账户失败")}}static transformUser(e){return{id:e.id,email:e.email,username:e.username,fullName:e.full_name,avatarUrl:e.avatar_url,bio:e.bio,website:e.website,location:e.location,role:e.role,isActive:e.is_active,emailVerified:e.email_verified,createdAt:e.created_at,updatedAt:e.updated_at,lastLoginAt:e.last_login_at}}}class Hn{static async login(e){try{const{data:t,error:r}=await Mn.auth.signInWithPassword({email:e.email,password:e.password});if(r)throw r;if(!t.user)throw new Error("登录失败");await Wn.updateLastLogin(t.user.id);const s=await Wn.getCurrentUser();if(!s)throw new Error("获取用户信息失败");return{user:s,session:t.session}}catch(t){throw console.error("登录失败:",t),new Error(t instanceof Error?t.message:"登录失败")}}static async register(e){try{if(e.username){if(!(await Wn.checkUsernameAvailability(e.username)))throw new Error("用户名已被使用")}const{data:t,error:r}=await Mn.auth.signUp({email:e.email,password:e.password,options:{data:{full_name:e.fullName,username:e.username}}});if(r)throw r;if(!t.user)throw new Error("注册失败");const s=await Wn.createUserProfile(t.user.id,e.email);if(e.fullName||e.username){return{user:await Wn.updateProfile(t.user.id,{fullName:e.fullName,username:e.username}),session:t.session}}return{user:s,session:t.session}}catch(t){throw console.error("注册失败:",t),new Error(t instanceof Error?t.message:"注册失败")}}static async logout(){try{const{error:e}=await Mn.auth.signOut();if(e)throw e}catch(e){throw console.error("登出失败:",e),new Error("登出失败")}}static async forgotPassword(e){try{const{error:t}=await Mn.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/auth/reset-password`});if(t)throw t}catch(t){throw console.error("发送重置密码邮件失败:",t),new Error("发送重置密码邮件失败")}}static async resetPassword(e){try{const{error:t}=await Mn.auth.updateUser({password:e});if(t)throw t}catch(t){throw console.error("重置密码失败:",t),new Error("重置密码失败")}}static async changePassword(e){try{const{error:t}=await Mn.auth.updateUser({password:e});if(t)throw t}catch(t){throw console.error("更改密码失败:",t),new Error("更改密码失败")}}static async updateEmail(e){try{const{error:t}=await Mn.auth.updateUser({email:e});if(t)throw t}catch(t){throw console.error("更新邮箱失败:",t),new Error("更新邮箱失败")}}static async verifyEmail(e,t){try{const{error:r}=await Mn.auth.verifyOtp({token_hash:e,type:t});if(r)throw r}catch(r){throw console.error("验证邮箱失败:",r),new Error("验证邮箱失败")}}static async resendVerificationEmail(){try{const{data:{user:e}}=await Mn.auth.getUser();if(!e)throw new Error("用户未登录");const{error:t}=await Mn.auth.resend({type:"signup",email:e.email});if(t)throw t}catch(e){throw console.error("重新发送验证邮件失败:",e),new Error("重新发送验证邮件失败")}}static async getSession(){try{const{data:{session:e}}=await Mn.auth.getSession();return e}catch(e){return console.error("获取会话失败:",e),null}}static async refreshSession(){try{const{data:e,error:t}=await Mn.auth.refreshSession();if(t)throw t;return e.session}catch(e){throw console.error("刷新会话失败:",e),new Error("刷新会话失败")}}static async isAuthenticated(){try{const{data:{user:e}}=await Mn.auth.getUser();return!!e}catch(e){return!1}}static onAuthStateChange(e){return Mn.auth.onAuthStateChange(e)}static async signInWithGoogle(){try{const{error:e}=await Mn.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(e)throw e}catch(e){throw console.error("Google 登录失败:",e),new Error("Google 登录失败")}}static async signInWithGitHub(){try{const{error:e}=await Mn.auth.signInWithOAuth({provider:"github",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(e)throw e}catch(e){throw console.error("GitHub 登录失败:",e),new Error("GitHub 登录失败")}}static async deleteAccount(){try{const{data:{user:e}}=await Mn.auth.getUser();if(!e)throw new Error("用户未登录");await Wn.deleteAccount(e.id)}catch(e){throw console.error("删除账户失败:",e),new Error("删除账户失败")}}}const Kn=p("auth",()=>{const e=l(null),t=l(null),r=l(!1),s=l(null),a=l(!1),n=o(()=>!!e.value&&!!t.value),i=o(()=>{var t,r;return"admin"===(null==(t=e.value)?void 0:t.role)||"super_admin"===(null==(r=e.value)?void 0:r.role)}),c=o(()=>{var t;return(null==(t=e.value)?void 0:t.fullName)?e.value.fullName.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2):"U"}),u=async()=>{try{const r=await Wn.getCurrentUser(),s=await Hn.getSession();return r&&s?(e.value=r,t.value=s,!0):(e.value=null,t.value=null,!1)}catch(r){return console.error("检查认证状态失败:",r),e.value=null,t.value=null,!1}},d=()=>{if(!n.value)throw new Error("需要登录才能访问")};return{user:e,session:t,loading:r,error:s,initialized:a,isAuthenticated:n,isAdmin:i,userInitials:c,login:async a=>{try{r.value=!0,s.value=null;const n=await Hn.login(a);return e.value=n.user,t.value=n.session,n}catch(n){throw s.value=n instanceof Error?n.message:"登录失败",n}finally{r.value=!1}},register:async a=>{try{r.value=!0,s.value=null;const n=await Hn.register(a);return e.value=n.user,t.value=n.session,n}catch(n){throw s.value=n instanceof Error?n.message:"注册失败",n}finally{r.value=!1}},logout:async()=>{try{r.value=!0,s.value=null,await Hn.logout(),e.value=null,t.value=null}catch(a){throw s.value=a instanceof Error?a.message:"登出失败",a}finally{r.value=!1}},forgotPassword:async e=>{try{r.value=!0,s.value=null,await Hn.forgotPassword(e)}catch(t){throw s.value=t instanceof Error?t.message:"发送重置邮件失败",t}finally{r.value=!1}},resetPassword:async e=>{try{r.value=!0,s.value=null,await Hn.resetPassword(e)}catch(t){throw s.value=t instanceof Error?t.message:"重置密码失败",t}finally{r.value=!1}},updateProfile:async t=>{try{if(r.value=!0,s.value=null,!e.value)throw new Error("用户未登录");const a=await Wn.updateProfile(e.value.id,t);return e.value=a,a}catch(a){throw s.value=a instanceof Error?a.message:"更新资料失败",a}finally{r.value=!1}},refreshSession:async()=>{try{const e=await Hn.refreshSession();return t.value=e,e}catch(r){throw console.error("刷新会话失败:",r),e.value=null,t.value=null,r}},checkAuth:u,initialize:async()=>{if(!a.value)try{r.value=!0,await u(),Hn.onAuthStateChange((t,r)=>{"SIGNED_IN"===t?u():"SIGNED_OUT"===t?(e.value=null,r.value=null):"TOKEN_REFRESHED"===t&&(r.value=r)}),a.value=!0}catch(t){console.error("初始化认证状态失败:",t)}finally{r.value=!1}},clearError:()=>{s.value=null},requireAuth:d,requireAdmin:()=>{if(d(),!i.value)throw new Error("需要管理员权限")}}});function Qn(e){return!!v()&&(g(e),!0)}function Yn(e){return"function"==typeof e?e():a(e)}const Zn="undefined"!=typeof window&&"undefined"!=typeof document;"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const Xn=Object.prototype.toString,eo=()=>{};function to(e,t){return function(...r){return new Promise((s,a)=>{Promise.resolve(e(()=>t.apply(this,r),{fn:t,thisArg:this,args:r})).then(s).catch(a)})}}const ro=e=>e();function so(e,t=200,r={}){return to(function(e,t={}){let r,s,a=eo;const n=e=>{clearTimeout(e),a(),a=eo};return o=>{const i=Yn(e),l=Yn(t.maxWait);return r&&n(r),i<=0||void 0!==l&&l<=0?(s&&(n(s),s=null),Promise.resolve(o())):new Promise((e,c)=>{a=t.rejectOnCancel?c:e,l&&!s&&(s=setTimeout(()=>{r&&n(r),s=null,e(o())},l)),r=setTimeout(()=>{s&&n(s),s=null,e(o())},i)})}}(t,r),e)}function ao(e,t,r={}){const{eventFilter:s,...a}=r,{eventFilter:n,pause:o,resume:i,isActive:u}=function(e=ro){const t=l(!0);return{isActive:y(t),pause:function(){t.value=!1},resume:function(){t.value=!0},eventFilter:(...r)=>{t.value&&e(...r)}}}(s),d=function(e,t,r={}){const{eventFilter:s=ro,...a}=r;return c(e,to(s,t),a)}(e,t,{...a,eventFilter:n});return{stop:d,pause:o,resume:i,isActive:u}}function no(e,t=!0,r){w()?m(e,r):t?e():d(e)}function oo(e){var t;const r=Yn(e);return null!=(t=null==r?void 0:r.$el)?t:r}const io=Zn?window:void 0;function lo(...e){let t,r,s,a;if("string"==typeof e[0]||Array.isArray(e[0])?([r,s,a]=e,t=io):[t,r,s,a]=e,!t)return eo;Array.isArray(r)||(r=[r]),Array.isArray(s)||(s=[s]);const n=[],o=()=>{n.forEach(e=>e()),n.length=0},i=c(()=>[oo(t),Yn(a)],([e,t])=>{if(o(),!e)return;const a=(i=t,"[object Object]"===Xn.call(i)?{...t}:t);var i;n.push(...r.flatMap(t=>s.map(r=>((e,t,r,s)=>(e.addEventListener(t,r,s),()=>e.removeEventListener(t,r,s)))(e,t,r,a))))},{immediate:!0,flush:"post"}),l=()=>{i(),o()};return Qn(l),l}function co(e){const t=function(){const e=l(!1),t=w();return t&&m(()=>{e.value=!0},t),e}();return o(()=>(t.value,Boolean(e())))}function uo(e,t={}){const{window:r=io}=t,s=co(()=>r&&"matchMedia"in r&&"function"==typeof r.matchMedia);let a;const n=l(!1),o=e=>{n.value=e.matches},i=()=>{a&&("removeEventListener"in a?a.removeEventListener("change",o):a.removeListener(o))},c=_(()=>{s.value&&(i(),a=r.matchMedia(Yn(e)),"addEventListener"in a?a.addEventListener("change",o):a.addListener(o),n.value=a.matches)});return Qn(()=>{c(),i(),a=void 0}),n}const ho="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},fo="__vueuse_ssr_handlers__",po=mo();function mo(){return fo in ho||(ho[fo]=ho[fo]||{}),ho[fo]}const vo={boolean:{read:e=>"true"===e,write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},go="vueuse-storage";function yo(e,t,s,a={}){var n;const{flush:o="pre",deep:i=!0,listenToStorageChanges:c=!0,writeDefaults:u=!0,mergeDefaults:h=!1,shallow:f,window:p=io,eventFilter:m,onError:v=e=>{console.error(e)},initOnMounted:g}=a,y=(f?r:l)("function"==typeof t?t():t);if(!s)try{s=function(e,t){return po[e]||t}("getDefaultStorage",()=>{var e;return null==(e=io)?void 0:e.localStorage})()}catch(C){v(C)}if(!s)return y;const w=Yn(t),_=function(e){return null==e?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":"boolean"==typeof e?"boolean":"string"==typeof e?"string":"object"==typeof e?"object":Number.isNaN(e)?"any":"number"}(w),b=null!=(n=a.serializer)?n:vo[_],{pause:k,resume:S}=ao(y,()=>function(t){try{const r=s.getItem(e);if(null==t)E(r,null),s.removeItem(e);else{const a=b.write(t);r!==a&&(s.setItem(e,a),E(r,a))}}catch(C){v(C)}}(y.value),{flush:o,deep:i,eventFilter:m});function E(t,r){p&&p.dispatchEvent(new CustomEvent(go,{detail:{key:e,oldValue:t,newValue:r,storageArea:s}}))}function T(t){if(!t||t.storageArea===s)if(t&&null==t.key)y.value=w;else if(!t||t.key===e){k();try{(null==t?void 0:t.newValue)!==b.write(y.value)&&(y.value=function(t){const r=t?t.newValue:s.getItem(e);if(null==r)return u&&null!=w&&s.setItem(e,b.write(w)),w;if(!t&&h){const e=b.read(r);return"function"==typeof h?h(e,w):"object"!==_||Array.isArray(e)?e:{...w,...e}}return"string"!=typeof r?r:b.read(r)}(t))}catch(C){v(C)}finally{t?d(S):S()}}}function O(e){T(e.detail)}return p&&c&&no(()=>{lo(p,"storage",T),lo(p,go,O),g&&T()}),g||T(),y}const wo={mode:"auto",primaryColor:"#3b82f6",accentColor:"#10b981",borderRadius:"medium",fontSize:"medium",fontFamily:"system",compactMode:!1,highContrast:!1,reducedMotion:!1},_o=[{id:"default",name:"默认主题",description:"经典的蓝色主题，适合大多数用户",config:{primaryColor:"#3b82f6",accentColor:"#10b981"},preview:{primary:"#3b82f6",secondary:"#10b981",background:"#ffffff",surface:"#f8fafc"}},{id:"purple",name:"紫色主题",description:"优雅的紫色配色方案",config:{primaryColor:"#8b5cf6",accentColor:"#f59e0b"},preview:{primary:"#8b5cf6",secondary:"#f59e0b",background:"#ffffff",surface:"#faf5ff"}},{id:"green",name:"绿色主题",description:"清新的绿色主题，护眼舒适",config:{primaryColor:"#059669",accentColor:"#dc2626"},preview:{primary:"#059669",secondary:"#dc2626",background:"#ffffff",surface:"#f0fdf4"}},{id:"orange",name:"橙色主题",description:"温暖的橙色主题，充满活力",config:{primaryColor:"#ea580c",accentColor:"#7c3aed"},preview:{primary:"#ea580c",secondary:"#7c3aed",background:"#ffffff",surface:"#fff7ed"}},{id:"dark-blue",name:"深蓝主题",description:"专业的深蓝色主题",config:{mode:"dark",primaryColor:"#60a5fa",accentColor:"#34d399"},preview:{primary:"#60a5fa",secondary:"#34d399",background:"#0f172a",surface:"#1e293b"}},{id:"high-contrast",name:"高对比度",description:"高对比度主题，提升可访问性",config:{primaryColor:"#000000",accentColor:"#ffffff",highContrast:!0,borderRadius:"none"},preview:{primary:"#000000",secondary:"#ffffff",background:"#ffffff",surface:"#f5f5f5"}}];function bo(){const e=function(e){const t=uo("(prefers-color-scheme: light)",e),r=uo("(prefers-color-scheme: dark)",e);return o(()=>r.value?"dark":t.value?"light":"no-preference")}(),t=yo("theme-config",wo),r=o(()=>"auto"===t.value.mode?"dark"===e.value?"dark":"light":"dark"===t.value.mode?"dark":"light"),s=o(()=>"dark"===r.value),a=o(()=>{const e=t.value,s=r.value,a=e.primaryColor,n=e.accentColor,o={"--color-primary":a,"--color-primary-rgb":l(a),"--color-accent":n,"--color-accent-rgb":l(n),"--border-radius-sm":u(e.borderRadius,"sm"),"--border-radius-md":u(e.borderRadius,"md"),"--border-radius-lg":u(e.borderRadius,"lg"),"--border-radius-xl":u(e.borderRadius,"xl"),"--font-size-xs":d(e.fontSize,"xs"),"--font-size-sm":d(e.fontSize,"sm"),"--font-size-base":d(e.fontSize,"base"),"--font-size-lg":d(e.fontSize,"lg"),"--font-size-xl":d(e.fontSize,"xl"),"--font-size-2xl":d(e.fontSize,"2xl"),"--font-size-3xl":d(e.fontSize,"3xl"),"--font-family":h(e.fontFamily),"--spacing-scale":e.compactMode?"0.875":"1","--transition-duration":e.reducedMotion?"0ms":"150ms","--animation-duration":e.reducedMotion?"0ms":"300ms"};return"dark"===s?Object.assign(o,{"--color-background":e.highContrast?"#000000":"#0f172a","--color-surface":e.highContrast?"#1a1a1a":"#1e293b","--color-surface-hover":e.highContrast?"#333333":"#334155","--color-border":e.highContrast?"#666666":"#475569","--color-text":e.highContrast?"#ffffff":"#f1f5f9","--color-text-secondary":e.highContrast?"#cccccc":"#cbd5e1","--color-text-muted":e.highContrast?"#999999":"#94a3b8"}):Object.assign(o,{"--color-background":(e.highContrast,"#ffffff"),"--color-surface":e.highContrast?"#f5f5f5":"#f8fafc","--color-surface-hover":e.highContrast?"#e5e5e5":"#f1f5f9","--color-border":e.highContrast?"#333333":"#e2e8f0","--color-text":e.highContrast?"#000000":"#1e293b","--color-text-secondary":e.highContrast?"#333333":"#475569","--color-text-muted":e.highContrast?"#666666":"#64748b"}),o}),n=()=>{const e=document.documentElement;e.setAttribute("data-theme",r.value),e.style.colorScheme=r.value,Object.entries(a.value).forEach(([t,r])=>{e.style.setProperty(t,r)}),e.classList.toggle("theme-dark",s.value),e.classList.toggle("theme-light",!s.value),e.classList.toggle("theme-compact",t.value.compactMode),e.classList.toggle("theme-high-contrast",t.value.highContrast),e.classList.toggle("theme-reduced-motion",t.value.reducedMotion)},i=e=>{t.value.mode=e},l=e=>{const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);if(t){return`${parseInt(t[1],16)}, ${parseInt(t[2],16)}, ${parseInt(t[3],16)}`}return"0, 0, 0"},u=(e,t)=>({none:{sm:"0",md:"0",lg:"0",xl:"0"},small:{sm:"2px",md:"4px",lg:"6px",xl:"8px"},medium:{sm:"4px",md:"6px",lg:"8px",xl:"12px"},large:{sm:"6px",md:"8px",lg:"12px",xl:"16px"}}[e][t]||"4px"),d=(e,t)=>({small:{xs:"0.6875rem",sm:"0.75rem",base:"0.8125rem",lg:"0.9375rem",xl:"1.0625rem","2xl":"1.3125rem","3xl":"1.6875rem"},medium:{xs:"0.75rem",sm:"0.875rem",base:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem"},large:{xs:"0.8125rem",sm:"0.9375rem",base:"1.0625rem",lg:"1.1875rem",xl:"1.3125rem","2xl":"1.5625rem","3xl":"1.9375rem"}}[e][t]||"1rem"),h=e=>{const t={system:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',inter:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif',roboto:'"Roboto", -apple-system, BlinkMacSystemFont, sans-serif',poppins:'"Poppins", -apple-system, BlinkMacSystemFont, sans-serif'};return t[e]||t.system},f=e=>"object"==typeof e&&null!==e&&["light","dark","auto"].includes(e.mode)&&"string"==typeof e.primaryColor&&"string"==typeof e.accentColor;return c([t,e],n,{deep:!0,immediate:!1}),m(()=>{n()}),{themeConfig:t,activeColorScheme:r,isDark:s,cssVariables:a,themePresets:_o,setThemeMode:i,toggleDark:()=>{"auto"===t.value.mode?i("dark"===e.value?"light":"dark"):i("dark"===t.value.mode?"light":"dark")},setPrimaryColor:e=>{t.value.primaryColor=e},setAccentColor:e=>{t.value.accentColor=e},applyPreset:e=>{const r=_o.find(t=>t.id===e);r&&Object.assign(t.value,r.config)},resetTheme:()=>{t.value={...wo}},exportTheme:()=>JSON.stringify(t.value,null,2),importTheme:e=>{try{const r=JSON.parse(e);return!!f(r)&&(t.value={...wo,...r},!0)}catch{return!1}},getThemeInfo:()=>({mode:t.value.mode,colorScheme:r.value,isDark:s.value,config:{...t.value}}),applyTheme:n}}const ko={class:"theme-selector"},So=["title"],Eo={class:"panel-header"},To={class:"panel-content"},Oo={class:"setting-group"},Co={class:"theme-mode-options"},Po=["onClick"],Ao={class:"setting-group"},jo={class:"theme-presets"},Io=["onClick"],Ro={class:"preset-preview"},xo={class:"preset-info"},$o={class:"preset-name"},Lo={class:"preset-description"},Uo={class:"setting-group"},Do={class:"color-settings"},qo={class:"color-setting"},No={class:"color-input-wrapper"},Mo={class:"color-value"},Bo={class:"color-setting"},Fo={class:"color-input-wrapper"},Vo={class:"color-value"},zo={class:"setting-group"},Go={class:"advanced-settings"},Jo={class:"setting-row"},Wo={class:"setting-row"},Ho={class:"setting-row"},Ko={class:"setting-row"},Qo={class:"setting-checkbox"},Yo={class:"setting-row"},Zo={class:"setting-checkbox"},Xo={class:"setting-row"},ei={class:"setting-checkbox"},ti={class:"panel-actions"},ri={key:0,class:"export-import-section"},si={class:"export-section"},ai=["value"],ni={class:"import-section"},oi=["disabled"],ii={key:0,class:"import-error"},li=(e,t)=>{const r=e.__vccOpts||e;for(const[s,a]of t)r[s]=a;return r},ci=li(s({__name:"ThemeSelector",setup(e){const{themeConfig:t,isDark:r,themePresets:s,setThemeMode:n,setPrimaryColor:o,setAccentColor:i,applyPreset:c,resetTheme:u,exportTheme:d,importTheme:h}=bo(),f=l(!1),p=l(!1),m=l(""),v=l(""),g=l(!1),y=[{value:"light",label:"浅色",icon:T},{value:"dark",label:"深色",icon:O},{value:"auto",label:"自动",icon:M}],w=e=>e.config.primaryColor===t.value.primaryColor&&e.config.accentColor===t.value.accentColor,_=async()=>{try{await navigator.clipboard.writeText(d()),g.value=!0,setTimeout(()=>{g.value=!1},2e3)}catch(e){console.error("复制失败:",e)}},F=()=>{if(v.value="",!m.value.trim())return void(v.value="请输入主题配置");h(m.value)?(m.value="",p.value=!1,f.value=!1):v.value="主题配置格式错误"};return(e,l)=>(D(),b("div",ko,[k("button",{onClick:l[0]||(l[0]=e=>f.value=!f.value),class:"theme-toggle-button",title:a(r)?"切换到浅色主题":"切换到深色主题"},[a(r)?(D(),E(a(T),{key:0,class:"icon"})):(D(),E(a(O),{key:1,class:"icon"}))],8,So),f.value?(D(),b("div",{key:0,class:"theme-panel",onClick:l[16]||(l[16]=C(()=>{},["stop"]))},[k("div",Eo,[l[18]||(l[18]=k("h3",{class:"panel-title"},"主题设置",-1)),k("button",{onClick:l[1]||(l[1]=e=>f.value=!1),class:"close-button"},[P(a(A),{class:"icon"})])]),k("div",To,[k("div",Oo,[l[19]||(l[19]=k("label",{class:"setting-label"},"主题模式",-1)),k("div",Co,[(D(),b(j,null,I(y,e=>k("button",{key:e.value,onClick:t=>a(n)(e.value),class:q(["mode-button",{active:a(t).mode===e.value}])},[(D(),E(N(e.icon),{class:"mode-icon"})),k("span",null,$(e.label),1)],10,Po)),64))])]),k("div",Ao,[l[20]||(l[20]=k("label",{class:"setting-label"},"预设主题",-1)),k("div",jo,[(D(!0),b(j,null,I(a(s),e=>(D(),b("div",{key:e.id,onClick:t=>a(c)(e.id),class:q(["preset-card",{active:w(e)}])},[k("div",Ro,[k("div",{class:"preview-color primary",style:B({backgroundColor:e.preview.primary})},null,4),k("div",{class:"preview-color secondary",style:B({backgroundColor:e.preview.secondary})},null,4),k("div",{class:"preview-color background",style:B({backgroundColor:e.preview.background})},null,4),k("div",{class:"preview-color surface",style:B({backgroundColor:e.preview.surface})},null,4)]),k("div",xo,[k("h4",$o,$(e.name),1),k("p",Lo,$(e.description),1)])],10,Io))),128))])]),k("div",Uo,[l[23]||(l[23]=k("label",{class:"setting-label"},"自定义颜色",-1)),k("div",Do,[k("div",qo,[l[21]||(l[21]=k("label",{class:"color-label"},"主色调",-1)),k("div",No,[R(k("input",{"onUpdate:modelValue":l[2]||(l[2]=e=>a(t).primaryColor=e),type:"color",class:"color-input",onChange:l[3]||(l[3]=e=>a(o)(a(t).primaryColor))},null,544),[[x,a(t).primaryColor]]),k("span",Mo,$(a(t).primaryColor),1)])]),k("div",Bo,[l[22]||(l[22]=k("label",{class:"color-label"},"强调色",-1)),k("div",Fo,[R(k("input",{"onUpdate:modelValue":l[4]||(l[4]=e=>a(t).accentColor=e),type:"color",class:"color-input",onChange:l[5]||(l[5]=e=>a(i)(a(t).accentColor))},null,544),[[x,a(t).accentColor]]),k("span",Vo,$(a(t).accentColor),1)])])])]),k("div",zo,[l[33]||(l[33]=k("label",{class:"setting-label"},"高级设置",-1)),k("div",Go,[k("div",Jo,[l[25]||(l[25]=k("label",{class:"setting-item-label"},"边框圆角",-1)),R(k("select",{"onUpdate:modelValue":l[6]||(l[6]=e=>a(t).borderRadius=e),class:"setting-select"},l[24]||(l[24]=[k("option",{value:"none"},"无圆角",-1),k("option",{value:"small"},"小圆角",-1),k("option",{value:"medium"},"中等圆角",-1),k("option",{value:"large"},"大圆角",-1)]),512),[[L,a(t).borderRadius]])]),k("div",Wo,[l[27]||(l[27]=k("label",{class:"setting-item-label"},"字体大小",-1)),R(k("select",{"onUpdate:modelValue":l[7]||(l[7]=e=>a(t).fontSize=e),class:"setting-select"},l[26]||(l[26]=[k("option",{value:"small"},"小",-1),k("option",{value:"medium"},"中",-1),k("option",{value:"large"},"大",-1)]),512),[[L,a(t).fontSize]])]),k("div",Ho,[l[29]||(l[29]=k("label",{class:"setting-item-label"},"字体",-1)),R(k("select",{"onUpdate:modelValue":l[8]||(l[8]=e=>a(t).fontFamily=e),class:"setting-select"},l[28]||(l[28]=[k("option",{value:"system"},"系统字体",-1),k("option",{value:"inter"},"Inter",-1),k("option",{value:"roboto"},"Roboto",-1),k("option",{value:"poppins"},"Poppins",-1)]),512),[[L,a(t).fontFamily]])]),k("div",Ko,[k("label",Qo,[R(k("input",{"onUpdate:modelValue":l[9]||(l[9]=e=>a(t).compactMode=e),type:"checkbox"},null,512),[[U,a(t).compactMode]]),l[30]||(l[30]=k("span",{class:"checkbox-label"},"紧凑模式",-1))])]),k("div",Yo,[k("label",Zo,[R(k("input",{"onUpdate:modelValue":l[10]||(l[10]=e=>a(t).highContrast=e),type:"checkbox"},null,512),[[U,a(t).highContrast]]),l[31]||(l[31]=k("span",{class:"checkbox-label"},"高对比度",-1))])]),k("div",Xo,[k("label",ei,[R(k("input",{"onUpdate:modelValue":l[11]||(l[11]=e=>a(t).reducedMotion=e),type:"checkbox"},null,512),[[U,a(t).reducedMotion]]),l[32]||(l[32]=k("span",{class:"checkbox-label"},"减少动画",-1))])])])]),k("div",ti,[k("button",{onClick:l[12]||(l[12]=(...e)=>a(u)&&a(u)(...e)),class:"action-button secondary"}," 重置主题 "),k("button",{onClick:l[13]||(l[13]=e=>p.value=!p.value),class:"action-button secondary"}," 导入/导出 ")]),p.value?(D(),b("div",ri,[k("div",si,[l[34]||(l[34]=k("label",{class:"setting-label"},"导出主题",-1)),k("textarea",{value:a(d)(),readonly:"",class:"export-textarea",onClick:l[14]||(l[14]=e=>e.target.select())},null,8,ai),k("button",{onClick:_,class:"copy-button"},$(g.value?"已复制":"复制配置"),1)]),k("div",ni,[l[35]||(l[35]=k("label",{class:"setting-label"},"导入主题",-1)),R(k("textarea",{"onUpdate:modelValue":l[15]||(l[15]=e=>m.value=e),placeholder:"粘贴主题配置...",class:"import-textarea"},null,512),[[x,m.value]]),k("button",{onClick:F,disabled:!m.value.trim(),class:"import-button"}," 导入主题 ",8,oi),v.value?(D(),b("p",ii,$(v.value),1)):S("",!0)])])):S("",!0)])])):S("",!0),f.value?(D(),b("div",{key:1,class:"panel-overlay",onClick:l[17]||(l[17]=e=>f.value=!1)})):S("",!0)]))}}),[["__scopeId","data-v-655bd5d1"]]);const ui=new class{constructor(){t(this,"eventSource")}async getUserNotifications(e,t={}){try{const{page:r=1,limit:s=20,type:a,unread_only:n,important_only:o}=t;let i=Mn.from("notifications").select("*",{count:"exact"}).eq("user_id",e);a&&(i=i.eq("type",a)),n&&(i=i.eq("is_read",!1)),o&&(i=i.eq("is_important",!0)),i=i.or("expires_at.is.null,expires_at.gt."+(new Date).toISOString()),i=i.order("is_important",{ascending:!1}).order("created_at",{ascending:!1}).range((r-1)*s,r*s-1);const{data:l,error:c,count:u}=await i;if(c)throw c;return{notifications:l||[],total:u||0,stats:await this.getNotificationStats(e)}}catch(r){throw console.error("获取通知失败:",r),r}}async getNotificationStats(e){try{const{data:t,error:r}=await Mn.from("notifications").select("type, is_read, is_important").eq("user_id",e).or("expires_at.is.null,expires_at.gt."+(new Date).toISOString());if(r)throw r;const s={total:(null==t?void 0:t.length)||0,unread:0,important:0,by_type:{}};return null==t||t.forEach(e=>{e.is_read||s.unread++,e.is_important&&s.important++;const t=e.type;s.by_type[t]=(s.by_type[t]||0)+1}),s}catch(t){throw console.error("获取通知统计失败:",t),t}}async createNotification(e){try{const{data:t,error:r}=await Mn.from("notifications").insert({...e,is_read:!1,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}).select().single();if(r)throw r;return this.sendRealTimeNotification(t),t}catch(t){throw console.error("创建通知失败:",t),t}}async createBulkNotifications(e){try{const t=e.map(e=>({...e,is_read:!1,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()})),{data:r,error:s}=await Mn.from("notifications").insert(t).select();if(s)throw s;return null==r||r.forEach(e=>{this.sendRealTimeNotification(e)}),r||[]}catch(t){throw console.error("批量创建通知失败:",t),t}}async markAsRead(e,t){try{const{error:r}=await Mn.from("notifications").update({is_read:!0,updated_at:(new Date).toISOString()}).eq("id",e).eq("user_id",t);if(r)throw r}catch(r){throw console.error("标记通知已读失败:",r),r}}async markAllAsRead(e,t){try{let r=Mn.from("notifications").update({is_read:!0,updated_at:(new Date).toISOString()}).eq("user_id",e).eq("is_read",!1);t&&(r=r.eq("type",t));const{error:s}=await r;if(s)throw s}catch(r){throw console.error("批量标记已读失败:",r),r}}async deleteNotification(e,t){try{const{error:r}=await Mn.from("notifications").delete().eq("id",e).eq("user_id",t);if(r)throw r}catch(r){throw console.error("删除通知失败:",r),r}}async cleanupExpiredNotifications(){try{const{error:e}=await Mn.from("notifications").delete().lt("expires_at",(new Date).toISOString());if(e)throw e}catch(e){throw console.error("清理过期通知失败:",e),e}}async getUserPreferences(e){try{const{data:t,error:r}=await Mn.from("notification_preferences").select("*").eq("user_id",e).single();if(r){if("PGRST116"===r.code)return await this.createDefaultPreferences(e);throw r}return t}catch(t){throw console.error("获取通知偏好失败:",t),t}}async createDefaultPreferences(e){try{const t={user_id:e,email_notifications:!0,push_notifications:!0,system_notifications:!0,product_notifications:!0,order_notifications:!0,marketing_notifications:!1,notification_frequency:"immediate",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},{data:r,error:s}=await Mn.from("notification_preferences").insert(t).select().single();if(s)throw s;return r}catch(t){throw console.error("创建默认通知偏好失败:",t),t}}async updatePreferences(e,t){try{const{data:r,error:s}=await Mn.from("notification_preferences").update({...t,updated_at:(new Date).toISOString()}).eq("user_id",e).select().single();if(s)throw s;return r}catch(r){throw console.error("更新通知偏好失败:",r),r}}sendRealTimeNotification(e){Mn.channel("notifications").send({type:"broadcast",event:"new_notification",payload:e})}subscribeToNotifications(e,t){const r=Mn.channel(`notifications:${e}`).on("postgres_changes",{event:"INSERT",schema:"public",table:"notifications",filter:`user_id=eq.${e}`},e=>{t(e.new)}).subscribe();return()=>{Mn.removeChannel(r)}}async requestNotificationPermission(){if(!("Notification"in window))throw new Error("此浏览器不支持桌面通知");if("granted"===Notification.permission)return"granted";if("denied"!==Notification.permission){return await Notification.requestPermission()}return Notification.permission}async showBrowserNotification(e,t={}){try{if("granted"===await this.requestNotificationPermission()){const r=new Notification(e,{icon:"/favicon.ico",badge:"/favicon.ico",...t});setTimeout(()=>{r.close()},5e3)}}catch(r){console.error("显示浏览器通知失败:",r)}}async sendSystemNotification(e,t,r,s={}){try{const a=e.map(e=>({user_id:e,type:s.type||"system",title:t,message:r,action_url:s.action_url,action_text:s.action_text,is_important:s.is_important||!1,expires_at:s.expires_at}));await this.createBulkNotifications(a)}catch(a){throw console.error("发送系统通知失败:",a),a}}},di={class:"notification-content"},hi={class:"notification-header"},fi={class:"notification-icon"},pi={class:"notification-meta"},mi={class:"notification-title"},vi={class:"notification-time"},gi={class:"notification-actions"},yi={class:"notification-body"},wi={class:"notification-message"},_i={key:0,class:"notification-action"},bi={key:0,class:"unread-indicator"},ki=li(s({__name:"NotificationItem",props:{notification:{}},emits:["read","delete","action"],setup(e,{emit:t}){const r=e,s=t,n=()=>{switch(r.notification.type){case"success":return Y;case"warning":return Q;case"error":return K;case"system":return H;case"product":return W;case"order":return J;default:return G}},o=e=>{const t=new Date(e),r=(new Date).getTime()-t.getTime(),s=Math.floor(r/6e4),a=Math.floor(r/36e5),n=Math.floor(r/864e5);return s<1?"刚刚":s<60?`${s}分钟前`:a<24?`${a}小时前`:n<7?`${n}天前`:t.toLocaleDateString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},i=()=>{s("read",r.notification.id)},l=()=>{s("delete",r.notification.id)},c=()=>{s("action",r.notification)};return(e,t)=>(D(),b("div",{class:q(["notification-item",{"is-unread":!e.notification.is_read,"is-important":e.notification.is_important,[`type-${e.notification.type}`]:!0}])},[k("div",di,[k("div",hi,[k("div",fi,[(D(),E(N(n()),{class:"icon"}))]),k("div",pi,[k("h4",mi,$(e.notification.title),1),k("span",vi,$(o(e.notification.created_at)),1)]),k("div",gi,[e.notification.is_read?S("",!0):(D(),b("button",{key:0,onClick:i,class:"action-button read-button",title:"标记为已读"},[P(a(F),{class:"icon"})])),k("button",{onClick:l,class:"action-button delete-button",title:"删除通知"},[P(a(A),{class:"icon"})])])]),k("div",yi,[k("p",wi,$(e.notification.message),1),e.notification.action_url?(D(),b("div",_i,[k("button",{onClick:c,class:"action-link"},[V($(e.notification.action_text||"查看详情")+" ",1),P(a(z),{class:"icon"})])])):S("",!0)])]),e.notification.is_read?S("",!0):(D(),b("div",bi))],2))}}),[["__scopeId","data-v-3b3f7e34"]]),Si={class:"notification-settings-modal"},Ei={class:"modal-content"},Ti={class:"modal-header"},Oi={class:"modal-body"},Ci={key:0,class:"loading-state"},Pi={key:1,class:"settings-form"},Ai={class:"setting-section"},ji={class:"setting-grid"},Ii={class:"setting-item"},Ri={class:"setting-label"},xi={class:"setting-item"},$i={class:"setting-label"},Li={class:"setting-item"},Ui={class:"setting-label"},Di={class:"setting-item"},qi={class:"setting-label"},Ni={class:"setting-item"},Mi={class:"setting-label"},Bi={class:"setting-item"},Fi={class:"setting-label"},Vi={class:"setting-section"},zi={class:"frequency-options"},Gi=["value"],Ji={class:"option-content"},Wi={class:"option-title"},Hi={class:"option-description"},Ki={class:"setting-section"},Qi={class:"quiet-hours"},Yi={class:"time-input-group"},Zi={class:"time-input-group"},Xi={class:"modal-footer"},el=["disabled"],tl=li(s({__name:"NotificationSettings",emits:["close","updated"],setup(e,{emit:t}){const r=t,s=Kn(),n=l(!0),o=l(!1),i=l({id:"",user_id:"",email_notifications:!0,push_notifications:!0,system_notifications:!0,product_notifications:!0,order_notifications:!0,marketing_notifications:!1,notification_frequency:"immediate",quiet_hours_start:"22:00",quiet_hours_end:"08:00",created_at:"",updated_at:""}),c=[{value:"immediate",label:"立即通知",description:"收到通知后立即推送"},{value:"daily",label:"每日汇总",description:"每天汇总一次发送"},{value:"weekly",label:"每周汇总",description:"每周汇总一次发送"},{value:"never",label:"从不",description:"不接收任何通知"}],u=async()=>{if(s.user&&!o.value)try{o.value=!0,await ui.updatePreferences(s.user.id,i.value),r("updated"),r("close")}catch(e){console.error("保存通知设置失败:",e)}finally{o.value=!1}};return m(()=>{(async()=>{if(s.user)try{n.value=!0;const e=await ui.getUserPreferences(s.user.id);e&&(i.value=e)}catch(e){console.error("加载通知偏好失败:",e)}finally{n.value=!1}})()}),(e,t)=>(D(),b("div",Si,[k("div",{class:"modal-overlay",onClick:t[0]||(t[0]=t=>e.$emit("close"))}),k("div",Ei,[k("div",Ti,[t[12]||(t[12]=k("h2",{class:"modal-title"},"通知设置",-1)),k("button",{onClick:t[1]||(t[1]=t=>e.$emit("close")),class:"close-button"},[P(a(A),{class:"icon"})])]),k("div",Oi,[n.value?(D(),b("div",Ci,t[13]||(t[13]=[k("div",{class:"loading-spinner"},null,-1),k("p",null,"加载设置中...",-1)]))):(D(),b("div",Pi,[k("div",Ai,[t[26]||(t[26]=k("h3",{class:"section-title"},"通知类型",-1)),k("div",ji,[k("div",Ii,[k("label",Ri,[R(k("input",{"onUpdate:modelValue":t[2]||(t[2]=e=>i.value.email_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,i.value.email_notifications]]),t[14]||(t[14]=k("span",{class:"checkbox-text"},"邮件通知",-1))]),t[15]||(t[15]=k("p",{class:"setting-description"},"通过邮件接收重要通知",-1))]),k("div",xi,[k("label",$i,[R(k("input",{"onUpdate:modelValue":t[3]||(t[3]=e=>i.value.push_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,i.value.push_notifications]]),t[16]||(t[16]=k("span",{class:"checkbox-text"},"浏览器通知",-1))]),t[17]||(t[17]=k("p",{class:"setting-description"},"在浏览器中显示桌面通知",-1))]),k("div",Li,[k("label",Ui,[R(k("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>i.value.system_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,i.value.system_notifications]]),t[18]||(t[18]=k("span",{class:"checkbox-text"},"系统通知",-1))]),t[19]||(t[19]=k("p",{class:"setting-description"},"接收系统更新和维护通知",-1))]),k("div",Di,[k("label",qi,[R(k("input",{"onUpdate:modelValue":t[5]||(t[5]=e=>i.value.product_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,i.value.product_notifications]]),t[20]||(t[20]=k("span",{class:"checkbox-text"},"产品通知",-1))]),t[21]||(t[21]=k("p",{class:"setting-description"},"接收新产品和更新通知",-1))]),k("div",Ni,[k("label",Mi,[R(k("input",{"onUpdate:modelValue":t[6]||(t[6]=e=>i.value.order_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,i.value.order_notifications]]),t[22]||(t[22]=k("span",{class:"checkbox-text"},"订单通知",-1))]),t[23]||(t[23]=k("p",{class:"setting-description"},"接收订单状态更新通知",-1))]),k("div",Bi,[k("label",Fi,[R(k("input",{"onUpdate:modelValue":t[7]||(t[7]=e=>i.value.marketing_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,i.value.marketing_notifications]]),t[24]||(t[24]=k("span",{class:"checkbox-text"},"营销通知",-1))]),t[25]||(t[25]=k("p",{class:"setting-description"},"接收促销和营销活动通知",-1))])])]),k("div",Vi,[t[27]||(t[27]=k("h3",{class:"section-title"},"通知频率",-1)),k("div",zi,[(D(),b(j,null,I(c,e=>k("label",{key:e.value,class:q(["frequency-option",{active:i.value.notification_frequency===e.value}])},[R(k("input",{"onUpdate:modelValue":t[8]||(t[8]=e=>i.value.notification_frequency=e),value:e.value,type:"radio",class:"frequency-radio"},null,8,Gi),[[Z,i.value.notification_frequency]]),k("div",Ji,[k("span",Wi,$(e.label),1),k("span",Hi,$(e.description),1)])],2)),64))])]),k("div",Ki,[t[31]||(t[31]=k("h3",{class:"section-title"},"免打扰时间",-1)),k("div",Qi,[k("div",Yi,[t[28]||(t[28]=k("label",{class:"time-label"},"开始时间",-1)),R(k("input",{"onUpdate:modelValue":t[9]||(t[9]=e=>i.value.quiet_hours_start=e),type:"time",class:"time-input"},null,512),[[x,i.value.quiet_hours_start]])]),t[30]||(t[30]=k("div",{class:"time-separator"},"-",-1)),k("div",Zi,[t[29]||(t[29]=k("label",{class:"time-label"},"结束时间",-1)),R(k("input",{"onUpdate:modelValue":t[10]||(t[10]=e=>i.value.quiet_hours_end=e),type:"time",class:"time-input"},null,512),[[x,i.value.quiet_hours_end]])])]),t[32]||(t[32]=k("p",{class:"setting-description"},"在此时间段内不会收到通知",-1))])]))]),k("div",Xi,[k("button",{onClick:t[11]||(t[11]=t=>e.$emit("close")),class:"button button-secondary"}," 取消 "),k("button",{onClick:u,disabled:o.value,class:"button button-primary"},$(o.value?"保存中...":"保存设置"),9,el)])])]))}}),[["__scopeId","data-v-8365f081"]]),rl={class:"notification-center"},sl={key:0,class:"notification-badge"},al={class:"panel-header"},nl={class:"panel-actions"},ol=["disabled"],il={class:"notification-filters"},ll=["onClick"],cl={key:0,class:"filter-count"},ul={class:"notification-list"},dl={key:0,class:"loading-state"},hl={key:1,class:"empty-state"},fl={key:2,class:"notifications"},pl={key:0,class:"load-more"},ml=["disabled"],vl=s({__name:"NotificationCenter",setup(e){const t=Kn(),r=l(!1),s=l(!1),n=l(!1),i=l(!1),u=l(!1),d=l([]),h=l({total:0,unread:0,important:0,by_type:{}}),f=l(1),p=l(!1),v=l("all");let g=null;const y=o(()=>[{key:"all",label:"全部",count:h.value.total},{key:"unread",label:"未读",count:h.value.unread},{key:"important",label:"重要",count:h.value.important},{key:"system",label:"系统",count:h.value.by_type.system||0},{key:"product",label:"产品",count:h.value.by_type.product||0},{key:"order",label:"订单",count:h.value.by_type.order||0}]),w=o(()=>{switch(v.value){case"unread":return d.value.filter(e=>!e.is_read);case"important":return d.value.filter(e=>e.is_important);case"system":case"product":case"order":return d.value.filter(e=>e.type===v.value);default:return d.value}}),_=()=>{r.value=!r.value,r.value&&0===d.value.length&&O()},T=()=>{r.value=!1},O=async(e=!0)=>{if(t.user)try{e?(n.value=!0,f.value=1,d.value=[]):i.value=!0;const r={page:f.value,limit:20};"unread"===v.value?r.unread_only=!0:"important"===v.value?r.important_only=!0:["system","product","order"].includes(v.value)&&(r.type=v.value);const s=await ui.getUserNotifications(t.user.id,r);e?d.value=s.notifications:d.value.push(...s.notifications),h.value=s.stats,p.value=d.value.length<s.total}catch(r){console.error("加载通知失败:",r)}finally{n.value=!1,i.value=!1}},R=async()=>{p.value&&!i.value&&(f.value++,await O(!1))},x=async()=>{if(t.user&&!u.value)try{u.value=!0;const e=["system","product","order"].includes(v.value)?v.value:void 0;await ui.markAllAsRead(t.user.id,e),d.value.forEach(t=>{e&&t.type!==e||(t.is_read=!0)}),await O()}catch(e){console.error("标记全部已读失败:",e)}finally{u.value=!1}},L=async e=>{if(t.user)try{await ui.markAsRead(e,t.user.id);const r=d.value.find(t=>t.id===e);r&&(r.is_read=!0,h.value.unread=Math.max(0,h.value.unread-1))}catch(r){console.error("标记已读失败:",r)}},U=async e=>{if(t.user)try{await ui.deleteNotification(e,t.user.id);const r=d.value.findIndex(t=>t.id===e);if(-1!==r){const e=d.value[r];d.value.splice(r,1),h.value.total--,e.is_read||h.value.unread--,e.is_important&&h.value.important--;const t=h.value.by_type[e.type]||0;h.value.by_type[e.type]=Math.max(0,t-1)}}catch(r){console.error("删除通知失败:",r)}},N=e=>{e.action_url&&window.open(e.action_url,"_blank"),e.is_read||L(e.id)},M=()=>{s.value=!1},B=()=>{switch(v.value){case"unread":return"所有通知都已阅读";case"important":return"暂无重要通知";case"system":return"暂无系统通知";case"product":return"暂无产品通知";case"order":return"暂无订单通知";default:return"您还没有收到任何通知"}},F=()=>{t.user&&(g=ui.subscribeToNotifications(t.user.id,e=>{d.value.unshift(e),h.value.total++,h.value.unread++,e.is_important&&h.value.important++;const t=h.value.by_type[e.type]||0;h.value.by_type[e.type]=t+1,ui.showBrowserNotification(e.title,{body:e.message,tag:e.id,requireInteraction:e.is_important})}))};return m(()=>{F(),t.user&&ui.getNotificationStats(t.user.id).then(e=>{h.value=e}).catch(e=>{console.error("加载通知统计失败:",e)})}),X(()=>{g&&g()}),c(v,()=>{O()}),c(()=>t.user,e=>{e?(F(),r.value&&O()):(g&&(g(),g=null),d.value=[],h.value={total:0,unread:0,important:0,by_type:{}})}),(e,t)=>(D(),b("div",rl,[k("div",{class:"notification-trigger",onClick:_},[k("button",{class:q(["notification-button",{"has-unread":h.value.unread>0}])},[P(a(H),{class:"icon"}),h.value.unread>0?(D(),b("span",sl,$(h.value.unread>99?"99+":h.value.unread),1)):S("",!0)],2)]),r.value?(D(),b("div",{key:0,class:"notification-panel",onClick:t[1]||(t[1]=C(()=>{},["stop"]))},[k("div",al,[t[3]||(t[3]=k("h3",{class:"panel-title"},"通知中心",-1)),k("div",nl,[h.value.unread>0?(D(),b("button",{key:0,onClick:x,class:"mark-all-read-button",disabled:u.value},$(u.value?"标记中...":"全部已读"),9,ol)):S("",!0),k("button",{onClick:t[0]||(t[0]=e=>s.value=!0),class:"settings-button"},[P(a(ee),{class:"icon"})]),k("button",{onClick:T,class:"close-button"},[P(a(A),{class:"icon"})])])]),k("div",il,[(D(!0),b(j,null,I(y.value,e=>(D(),b("button",{key:e.key,onClick:t=>v.value=e.key,class:q(["filter-button",{active:v.value===e.key}])},[V($(e.label)+" ",1),e.count>0?(D(),b("span",cl,$(e.count),1)):S("",!0)],10,ll))),128))]),k("div",ul,[n.value?(D(),b("div",dl,t[4]||(t[4]=[k("div",{class:"loading-spinner"},null,-1),k("p",null,"加载通知中...",-1)]))):0===w.value.length?(D(),b("div",hl,[t[5]||(t[5]=k("div",{class:"empty-icon"},"🔔",-1)),t[6]||(t[6]=k("h4",null,"暂无通知",-1)),k("p",null,$(B()),1)])):(D(),b("div",fl,[(D(!0),b(j,null,I(w.value,e=>(D(),E(ki,{key:e.id,notification:e,onRead:L,onDelete:U,onAction:N},null,8,["notification"]))),128)),p.value?(D(),b("div",pl,[k("button",{onClick:R,disabled:i.value,class:"load-more-button"},$(i.value?"加载中...":"加载更多"),9,ml)])):S("",!0)]))])])):S("",!0),s.value?(D(),E(tl,{key:1,onClose:t[2]||(t[2]=e=>s.value=!1),onUpdated:M})):S("",!0),r.value?(D(),b("div",{key:2,class:"panel-overlay",onClick:T})):S("",!0)]))}}),gl=li(vl,[["__scopeId","data-v-b040a3a2"]]),yl={class:"app-header"},wl={class:"header-content"},_l={class:"header-left"},bl={class:"header-center"},kl={class:"search-container"},Sl={class:"search-actions"},El={class:"header-right"},Tl={key:0,class:"user-menu"},Ol=["src","alt"],Cl={key:1,class:"avatar-placeholder"},Pl={key:0,class:"user-dropdown"},Al={class:"user-info"},jl={class:"user-name"},Il={class:"user-email"},Rl=li(s({__name:"AppHeader",setup(e){const t=tr(),r=Jn(),s=Kn(),n=l(),o=l(!1),i=()=>{r.searchQuery.trim()&&t.push({name:"Tools",query:{search:r.searchQuery.trim()}})},c=()=>{var e;r.searchQuery="",null==(e=n.value)||e.focus()},u=()=>{o.value=!o.value},d=()=>{o.value=!1},h=async()=>{try{await s.logout(),d(),t.push("/")}catch(e){console.error("退出登录失败:",e)}},f=e=>{var t;(e.ctrlKey||e.metaKey)&&"k"===e.key&&(e.preventDefault(),null==(t=n.value)||t.focus()),"Escape"===e.key&&(o.value=!1)},p=e=>{e.target.closest(".user-menu")||(o.value=!1)};return m(()=>{document.addEventListener("keydown",f),document.addEventListener("click",p)}),X(()=>{document.removeEventListener("keydown",f),document.removeEventListener("click",p)}),(e,t)=>{var l,f,p,m,v;const g=re("router-link");return D(),b("header",yl,[k("div",wl,[k("div",_l,[k("button",{class:q(["sidebar-toggle",{active:!a(r).sidebarCollapsed}]),onClick:t[0]||(t[0]=e=>a(r).toggleSidebar())},[P(a(te),{class:"icon"})],2),P(g,{to:"/",class:"app-title"},{default:se(()=>t[2]||(t[2]=[k("div",{class:"title-icon"},"🚀",-1),k("div",{class:"title-text"},[k("h1",null,"工具导航站"),k("span",null,"让工作更高效")],-1)])),_:1,__:[2]})]),k("div",bl,[k("div",kl,[P(a(ae),{class:"search-icon"}),R(k("input",{ref_key:"searchInput",ref:n,"onUpdate:modelValue":t[1]||(t[1]=e=>a(r).searchQuery=e),type:"text",placeholder:"搜索工具、分类或功能...",class:"search-input",onKeydown:[ne(i,["enter"]),ne(c,["esc"])]},null,544),[[x,a(r).searchQuery]]),k("div",Sl,[a(r).searchQuery?(D(),b("button",{key:0,class:"clear-search",onClick:c},[P(a(A),{class:"icon"})])):S("",!0),t[3]||(t[3]=k("div",{class:"search-shortcut"},"Ctrl+K",-1))])])]),k("div",El,[P(ci),P(gl),a(s).isAuthenticated?(D(),b("div",Tl,[k("button",{class:"user-avatar",onClick:u},[(null==(l=a(s).user)?void 0:l.avatar_url)?(D(),b("img",{key:0,src:a(s).user.avatar_url,alt:a(s).user.username,class:"avatar-image"},null,8,Ol)):(D(),b("div",Cl,$(null==(p=null==(f=a(s).user)?void 0:f.username)?void 0:p.charAt(0).toUpperCase()),1))]),o.value?(D(),b("div",Pl,[k("div",Al,[k("div",jl,$(null==(m=a(s).user)?void 0:m.username),1),k("div",Il,$(null==(v=a(s).user)?void 0:v.email),1)]),t[9]||(t[9]=k("div",{class:"menu-divider"},null,-1)),P(g,{to:"/user/profile",class:"menu-item",onClick:d},{default:se(()=>[P(a(ie),{class:"icon"}),t[4]||(t[4]=V(" 个人资料 "))]),_:1,__:[4]}),P(g,{to:"/user/favorites",class:"menu-item",onClick:d},{default:se(()=>[P(a(le),{class:"icon"}),t[5]||(t[5]=V(" 我的收藏 "))]),_:1,__:[5]}),P(g,{to:"/user/orders",class:"menu-item",onClick:d},{default:se(()=>[P(a(W),{class:"icon"}),t[6]||(t[6]=V(" 我的订单 "))]),_:1,__:[6]}),t[10]||(t[10]=k("div",{class:"menu-divider"},null,-1)),a(s).isAdmin?(D(),E(g,{key:0,to:"/admin",class:"menu-item",onClick:d},{default:se(()=>[P(a(ee),{class:"icon"}),t[7]||(t[7]=V(" 管理后台 "))]),_:1,__:[7]})):S("",!0),k("button",{class:"menu-item logout",onClick:h},[P(a(oe),{class:"icon"}),t[8]||(t[8]=V(" 退出登录 "))])])):S("",!0)])):(D(),E(g,{key:1,to:"/auth/login",class:"login-btn"},{default:se(()=>[P(a(ie),{class:"icon"}),t[11]||(t[11]=V(" 登录 "))]),_:1,__:[11]}))])])])}}}),[["__scopeId","data-v-060a5a19"]]),xl={class:"app-footer"},$l={class:"footer-content"},Ll={class:"footer-main"},Ul={class:"footer-section company-info"},Dl={class:"social-links"},ql={href:"#",class:"social-link",title:"微信"},Nl={href:"#",class:"social-link",title:"微博"},Ml={href:"#",class:"social-link",title:"GitHub"},Bl={href:"#",class:"social-link",title:"邮箱"},Fl={class:"footer-section"},Vl={class:"footer-links"},zl={class:"footer-section contact-info"},Gl={class:"contact-item"},Jl={class:"contact-item"},Wl={class:"contact-item"},Hl={class:"contact-item"},Kl=li(s({__name:"AppFooter",setup:e=>(e,t)=>{const r=re("router-link");return D(),b("footer",xl,[k("div",$l,[k("div",Ll,[k("div",Ul,[t[0]||(t[0]=k("div",{class:"company-logo"},[k("div",{class:"logo-icon"},"🚀"),k("div",{class:"logo-text"},[k("h3",null,"工具导航站"),k("p",null,"让工作更高效")])],-1)),t[1]||(t[1]=k("p",{class:"company-description"}," 专注于为用户提供优质的工具导航和产品展示服务，致力于提升工作效率，让每个人都能找到最适合的工具和产品。 ",-1)),k("div",Dl,[k("a",ql,[P(a(ue),{class:"icon"})]),k("a",Nl,[P(a(de),{class:"icon"})]),k("a",Ml,[P(a(he),{class:"icon"})]),k("a",Bl,[P(a(fe),{class:"icon"})])])]),k("div",Fl,[t[7]||(t[7]=k("h4",null,"快速导航",-1)),k("ul",Vl,[k("li",null,[P(r,{to:"/"},{default:se(()=>t[2]||(t[2]=[V("首页")])),_:1,__:[2]})]),k("li",null,[P(r,{to:"/tools"},{default:se(()=>t[3]||(t[3]=[V("工具导航")])),_:1,__:[3]})]),k("li",null,[P(r,{to:"/products"},{default:se(()=>t[4]||(t[4]=[V("产品展示")])),_:1,__:[4]})]),k("li",null,[P(r,{to:"/user/favorites"},{default:se(()=>t[5]||(t[5]=[V("我的收藏")])),_:1,__:[5]})]),k("li",null,[P(r,{to:"/admin"},{default:se(()=>t[6]||(t[6]=[V("管理后台")])),_:1,__:[6]})])])]),t[13]||(t[13]=ce('<div class="footer-section" data-v-88f57f9e><h4 data-v-88f57f9e>产品服务</h4><ul class="footer-links" data-v-88f57f9e><li data-v-88f57f9e><a href="#" data-v-88f57f9e>开发工具</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>设计工具</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>效率工具</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>AI工具</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>学习资源</a></li></ul></div><div class="footer-section" data-v-88f57f9e><h4 data-v-88f57f9e>帮助支持</h4><ul class="footer-links" data-v-88f57f9e><li data-v-88f57f9e><a href="#" data-v-88f57f9e>使用指南</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>常见问题</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>意见反馈</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>联系我们</a></li><li data-v-88f57f9e><a href="#" data-v-88f57f9e>服务条款</a></li></ul></div>',2)),k("div",zl,[t[12]||(t[12]=k("h4",null,"联系我们",-1)),k("div",Gl,[P(a(pe),{class:"contact-icon"}),t[8]||(t[8]=k("span",null,"400-123-4567",-1))]),k("div",Jl,[P(a(fe),{class:"contact-icon"}),t[9]||(t[9]=k("span",null,"<EMAIL>",-1))]),k("div",Wl,[P(a(me),{class:"contact-icon"}),t[10]||(t[10]=k("span",null,"北京市朝阳区科技园区",-1))]),k("div",Hl,[P(a(ve),{class:"contact-icon"}),t[11]||(t[11]=k("span",null,"工作时间：9:00-18:00",-1))])])]),t[14]||(t[14]=ce('<div class="footer-bottom" data-v-88f57f9e><div class="footer-bottom-content" data-v-88f57f9e><div class="copyright" data-v-88f57f9e><p data-v-88f57f9e>© 2024 工具导航站. 保留所有权利.</p><p data-v-88f57f9e><a href="#" data-v-88f57f9e>隐私政策</a> | <a href="#" data-v-88f57f9e>服务条款</a> | <a href="#" data-v-88f57f9e>网站地图</a></p></div><div class="footer-stats" data-v-88f57f9e><div class="stat-item" data-v-88f57f9e><span class="stat-number" data-v-88f57f9e>1000+</span><span class="stat-label" data-v-88f57f9e>精选工具</span></div><div class="stat-item" data-v-88f57f9e><span class="stat-number" data-v-88f57f9e>50+</span><span class="stat-label" data-v-88f57f9e>工具分类</span></div><div class="stat-item" data-v-88f57f9e><span class="stat-number" data-v-88f57f9e>10000+</span><span class="stat-label" data-v-88f57f9e>用户使用</span></div></div></div></div>',1))])])}}),[["__scopeId","data-v-88f57f9e"]]),Ql={class:"simple-status-bar"},Yl={class:"simple-status-content"},Zl=li(s({__name:"SimpleStatusBar",setup(e){const t=l(""),r=()=>{const e=new Date;t.value=e.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"})};let s=null;return m(()=>{r(),s=setInterval(r,1e3)}),X(()=>{s&&clearInterval(s)}),(e,r)=>(D(),b("div",Ql,[k("div",Yl,[r[0]||(r[0]=k("span",null,"🚀 工具导航站",-1)),k("span",null,$(t.value),1),r[1]||(r[1]=k("span",null,"状态栏测试",-1))])]))}}),[["__scopeId","data-v-a0f496da"]]),Xl={class:"feedback-widget"},ec={key:0,class:"feedback-badge"},tc={key:1,class:"feedback-panel"},rc={class:"panel-header"},sc={class:"panel-tabs"},ac=["onClick"],nc={class:"panel-content"},oc={key:0,class:"submit-feedback"},ic={class:"form-group"},lc={class:"form-group"},cc={class:"form-group"},uc={class:"form-group"},dc={class:"form-group"},hc={class:"form-label"},fc={class:"form-actions"},pc=["disabled"],mc={key:1,class:"feedback-history"},vc={key:0,class:"loading-state"},gc={key:1,class:"empty-state"},yc={key:2,class:"feedback-list"},wc={class:"feedback-header"},_c={class:"feedback-date"},bc={class:"feedback-title"},kc={class:"feedback-content"},Sc={key:0,class:"feedback-response"},Ec={class:"response-date"},Tc={key:2,class:"feedback-stats"},Oc={class:"stats-grid"},Cc={class:"stat-card"},Pc={class:"stat-number"},Ac={class:"stat-card"},jc={class:"stat-number"},Ic={class:"stat-card"},Rc={class:"stat-number"},xc={class:"stat-card"},$c={class:"stat-number"},Lc={class:"stats-charts"},Uc={class:"chart-section"},Dc={class:"type-distribution"},qc={class:"type-label"},Nc={class:"type-bar"},Mc={class:"type-count"},Bc={key:2,class:"success-message"},Fc=li(s({__name:"FeedbackWidget",setup(e){const t=Kn(),r=l(!1),s=l("submit"),n=l(!1),i=l(!1),c=l(!1),u=f({type:"",title:"",content:"",priority:"medium",includeSystemInfo:!0}),d=l([]),h=l({total:0,pending:0,resolved:0,response_rate:0,by_type:{}}),p=[{id:"submit",label:"提交反馈"},{id:"history",label:"反馈历史"},{id:"stats",label:"反馈统计"}],v=o(()=>d.value.filter(e=>!e.is_read).length),g=()=>{r.value=!r.value,r.value&&"history"===s.value&&w()},y=async()=>{if(t.user)try{i.value=!0;const e={...u,user_id:t.user.id,system_info:u.includeSystemInfo?_():null};console.log("提交反馈:",e),await new Promise(e=>setTimeout(e,1e3)),Object.assign(u,{type:"",title:"",content:"",priority:"medium",includeSystemInfo:!0}),c.value=!0,setTimeout(()=>{c.value=!1},3e3),r.value=!1}catch(e){console.error("提交反馈失败:",e),alert("提交反馈失败，请稍后重试")}finally{i.value=!1}else alert("请先登录后再提交反馈")},w=async()=>{if(t.user)try{n.value=!0,console.log("加载反馈历史"),d.value=[{id:"1",type:"feature",title:"希望增加暗色主题",content:"建议增加暗色主题选项，方便夜间使用",priority:"medium",status:"resolved",response:"感谢您的建议！暗色主题功能已经在最新版本中上线。",response_at:"2024-12-25T10:00:00Z",is_read:!0,created_at:"2024-12-24T15:30:00Z"}],h.value={total:5,pending:2,resolved:3,response_rate:80,by_type:{bug:2,feature:2,improvement:1}}}catch(e){console.error("加载反馈历史失败:",e)}finally{n.value=!1}},_=()=>({userAgent:navigator.userAgent,platform:navigator.platform,language:navigator.language,screenResolution:`${screen.width}x${screen.height}`,timestamp:(new Date).toISOString()}),E=e=>({bug:"Bug 报告",feature:"功能建议",improvement:"改进建议",question:"问题咨询",other:"其他"}[e]||e),T=e=>new Date(e).toLocaleString("zh-CN");return m(()=>{t.user&&w()}),(e,t)=>(D(),b("div",Xl,[r.value?S("",!0):(D(),b("button",{key:0,onClick:g,class:q(["feedback-button",{"has-feedback":v.value>0}]),title:"用户反馈"},[P(a(ue),{class:"icon"}),v.value>0?(D(),b("span",ec,$(v.value),1)):S("",!0)],2)),r.value?(D(),b("div",tc,[k("div",rc,[t[5]||(t[5]=k("h3",{class:"panel-title"},"用户反馈",-1)),k("button",{onClick:g,class:"close-button"},[P(a(A),{class:"icon"})])]),k("div",sc,[(D(),b(j,null,I(p,e=>k("button",{key:e.id,onClick:t=>s.value=e.id,class:q(["tab-button",{active:s.value===e.id}])},$(e.label),11,ac)),64))]),k("div",nc,["submit"===s.value?(D(),b("div",oc,[k("form",{onSubmit:C(y,["prevent"]),class:"feedback-form"},[k("div",ic,[t[7]||(t[7]=k("label",{for:"feedback-type",class:"form-label"},"反馈类型",-1)),R(k("select",{id:"feedback-type","onUpdate:modelValue":t[0]||(t[0]=e=>u.type=e),class:"form-select",required:""},t[6]||(t[6]=[ce('<option value="" data-v-5862e757>请选择反馈类型</option><option value="bug" data-v-5862e757>Bug 报告</option><option value="feature" data-v-5862e757>功能建议</option><option value="improvement" data-v-5862e757>改进建议</option><option value="question" data-v-5862e757>问题咨询</option><option value="other" data-v-5862e757>其他</option>',6)]),512),[[L,u.type]])]),k("div",lc,[t[8]||(t[8]=k("label",{for:"feedback-title",class:"form-label"},"标题",-1)),R(k("input",{id:"feedback-title","onUpdate:modelValue":t[1]||(t[1]=e=>u.title=e),type:"text",class:"form-input",placeholder:"简要描述您的反馈",required:""},null,512),[[x,u.title]])]),k("div",cc,[t[9]||(t[9]=k("label",{for:"feedback-content",class:"form-label"},"详细描述",-1)),R(k("textarea",{id:"feedback-content","onUpdate:modelValue":t[2]||(t[2]=e=>u.content=e),class:"form-textarea",rows:"4",placeholder:"请详细描述您的反馈内容",required:""},null,512),[[x,u.content]])]),k("div",uc,[t[11]||(t[11]=k("label",{for:"feedback-priority",class:"form-label"},"优先级",-1)),R(k("select",{id:"feedback-priority","onUpdate:modelValue":t[3]||(t[3]=e=>u.priority=e),class:"form-select"},t[10]||(t[10]=[k("option",{value:"low"},"低",-1),k("option",{value:"medium"},"中",-1),k("option",{value:"high"},"高",-1),k("option",{value:"urgent"},"紧急",-1)]),512),[[L,u.priority]])]),k("div",dc,[k("label",hc,[R(k("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>u.includeSystemInfo=e),type:"checkbox",class:"form-checkbox"},null,512),[[U,u.includeSystemInfo]]),t[12]||(t[12]=V(" 包含系统信息（浏览器、设备等） "))])]),k("div",fc,[k("button",{type:"submit",disabled:i.value,class:"submit-button"},$(i.value?"提交中...":"提交反馈"),9,pc)])],32)])):"history"===s.value?(D(),b("div",mc,[n.value?(D(),b("div",vc,t[13]||(t[13]=[k("div",{class:"loading-spinner"},null,-1),k("p",null,"加载反馈历史...",-1)]))):0===d.value.length?(D(),b("div",gc,[P(a(ue),{class:"empty-icon"}),t[14]||(t[14]=k("p",null,"暂无反馈记录",-1))])):(D(),b("div",yc,[(D(!0),b(j,null,I(d.value,e=>{return D(),b("div",{key:e.id,class:q(["feedback-item",{unread:!e.is_read}])},[k("div",wc,[k("span",{class:q(["feedback-type",e.type])},$(E(e.type)),3),k("span",{class:q(["feedback-priority",e.priority])},$((r=e.priority,{low:"低",medium:"中",high:"高",urgent:"紧急"}[r]||r)),3),k("span",_c,$(T(e.created_at)),1)]),k("h4",bc,$(e.title),1),k("p",kc,$(e.content),1),e.response?(D(),b("div",Sc,[t[15]||(t[15]=k("h5",null,"开发者回复：",-1)),k("p",null,$(e.response),1),k("span",Ec,$(T(e.response_at)),1)])):S("",!0)],2);var r}),128))]))])):"stats"===s.value?(D(),b("div",Tc,[k("div",Oc,[k("div",Cc,[k("div",Pc,$(h.value.total),1),t[16]||(t[16]=k("div",{class:"stat-label"},"总反馈数",-1))]),k("div",Ac,[k("div",jc,$(h.value.pending),1),t[17]||(t[17]=k("div",{class:"stat-label"},"待处理",-1))]),k("div",Ic,[k("div",Rc,$(h.value.resolved),1),t[18]||(t[18]=k("div",{class:"stat-label"},"已解决",-1))]),k("div",xc,[k("div",$c,$(h.value.response_rate)+"%",1),t[19]||(t[19]=k("div",{class:"stat-label"},"回复率",-1))])]),k("div",Lc,[k("div",Uc,[t[20]||(t[20]=k("h4",null,"反馈类型分布",-1)),k("div",Dc,[(D(!0),b(j,null,I(h.value.by_type,(e,t)=>(D(),b("div",{key:t,class:"type-item"},[k("span",qc,$(E(t)),1),k("div",Nc,[k("div",{class:"type-fill",style:B({width:e/h.value.total*100+"%"})},null,4)]),k("span",Mc,$(e),1)]))),128))])])])])):S("",!0)])])):S("",!0),c.value?(D(),b("div",Bc,[P(a(Y),{class:"success-icon"}),t[21]||(t[21]=k("span",null,"反馈提交成功！我们会尽快处理您的反馈。",-1))])):S("",!0)]))}}),[["__scopeId","data-v-5862e757"]]),Vc={id:"app"},zc={key:0,class:"global-error"},Gc={class:"error-content"},Jc={class:"error-message"},Wc=li(s({__name:"App",setup(e){const t=l(null),r=()=>{t.value=null};return ge((e,r,s)=>(console.error("全局错误:",e,s),t.value=e instanceof Error?e.message:"发生未知错误",!1)),m(()=>{window.addEventListener("error",e=>{var r;console.error("页面加载错误:",e.error),t.value=(null==(r=e.error)?void 0:r.message)||"页面加载失败"}),window.addEventListener("offline",()=>{t.value="网络连接已断开"})}),(e,s)=>{const n=re("RouterView");return D(),b("div",Vc,[t.value?(D(),b("div",zc,[k("div",Gc,[k("span",Jc,$(t.value),1),k("button",{class:"error-close",onClick:r},"×")])])):S("",!0),P(Rl),P(n,null,{default:se(({Component:e})=>[P(ye,{name:"fade",mode:"out-in"},{default:se(()=>[(D(),E(N(e)))]),_:2},1024)]),_:1}),P(Kl),a(false)?(D(),E(Zl,{key:1})):S("",!0),P(Fc)])}}}),[["__scopeId","data-v-4b922332"]]),Hc=[{path:"/",name:"Home",component:()=>ar(()=>import("./EnhancedHomeView-D9tysLox.js"),__vite__mapDeps([2,1,3,4]),import.meta.url),meta:{title:"工具导航站",description:"高效的工具导航和产品展示平台"}},{path:"/legacy",name:"LegacyHome",component:()=>ar(()=>import("./HomeView-BpCYTNJB.js"),__vite__mapDeps([5,1,6]),import.meta.url),meta:{title:"传统版工具导航站",description:"传统版本的工具导航和产品展示平台"}},{path:"/comparison",name:"Comparison",component:()=>ar(()=>import("./ComparisonView-Dvi63WL6.js"),__vite__mapDeps([7,1,8]),import.meta.url),meta:{title:"代码优化对比",description:"展示原HTML代码与Vue组件化改造后的效果对比"}},{path:"/test-statusbar",name:"TestStatusBar",component:()=>ar(()=>import("./TestStatusBar-BSgIjrnl.js"),__vite__mapDeps([9,1,10]),import.meta.url),meta:{title:"状态栏测试",description:"测试底部状态栏组件功能"}},{path:"/tools",name:"Tools",component:()=>ar(()=>import("./ToolsView-K3Vb-3Wc.js"),__vite__mapDeps([11,1,12]),import.meta.url),meta:{title:"工具导航",description:"发现和管理您的常用工具"}},{path:"/products",name:"Products",component:()=>ar(()=>import("./ProductsView-BzprdfvT.js"),__vite__mapDeps([13,1,14]),import.meta.url),meta:{title:"产品展示",description:"浏览和购买优质产品"}},{path:"/product/:id",name:"ProductDetail",component:()=>ar(()=>import("./ProductDetailView-U0E56uPS.js"),__vite__mapDeps([15,1,16]),import.meta.url),meta:{title:"产品详情",description:"查看产品详细信息"}},{path:"/user",name:"User",component:()=>ar(()=>import("./UserView-DTOTqe8H.js"),__vite__mapDeps([17,1,18]),import.meta.url),meta:{title:"个人中心",description:"管理您的账户和偏好设置",requiresAuth:!0},children:[{path:"profile",name:"UserProfile",component:()=>ar(()=>import("./ProfileView-BPVWElh8.js"),__vite__mapDeps([19,1,20]),import.meta.url),meta:{title:"个人资料",requiresAuth:!0}},{path:"favorites",name:"UserFavorites",component:()=>ar(()=>import("./FavoritesView-DLwbXd_E.js"),__vite__mapDeps([21,1,22]),import.meta.url),meta:{title:"我的收藏",requiresAuth:!0}},{path:"orders",name:"UserOrders",component:()=>ar(()=>import("./OrdersView-BWnDGqel.js"),__vite__mapDeps([23,1,24]),import.meta.url),meta:{title:"我的订单",requiresAuth:!0}}]},{path:"/auth",name:"Auth",component:()=>ar(()=>import("./AuthView-CVZYkuZm.js"),__vite__mapDeps([25,1,26]),import.meta.url),meta:{title:"登录注册",description:"登录或注册您的账户"},children:[{path:"login",name:"Login",component:()=>ar(()=>import("./LoginView-DWiq3o6Q.js"),__vite__mapDeps([27,1,28]),import.meta.url),meta:{title:"登录"}},{path:"register",name:"Register",component:()=>ar(()=>import("./RegisterView-jB4aOFAd.js"),__vite__mapDeps([29,1,30]),import.meta.url),meta:{title:"注册"}},{path:"forgot-password",name:"ForgotPassword",component:()=>ar(()=>import("./ForgotPasswordView-Dp_FYhbU.js"),__vite__mapDeps([31,1,32]),import.meta.url),meta:{title:"忘记密码"}}]},{path:"/admin",name:"Admin",component:()=>ar(()=>import("./AdminView-CKcVIYJV.js"),__vite__mapDeps([33,1,34]),import.meta.url),meta:{title:"管理后台",description:"系统管理和数据统计",requiresAuth:!0,requiresAdmin:!0},children:[{path:"dashboard",name:"AdminDashboard",component:()=>ar(()=>import("./DashboardView-CS5iCQ6c.js"),__vite__mapDeps([35,1,36]),import.meta.url),meta:{title:"仪表盘",requiresAuth:!0,requiresAdmin:!0}},{path:"tools",name:"AdminTools",component:()=>ar(()=>import("./AdminToolsView-CMiBhoo5.js"),__vite__mapDeps([37,3,1,38]),import.meta.url),meta:{title:"工具管理",requiresAuth:!0,requiresAdmin:!0}},{path:"products",name:"AdminProducts",component:()=>ar(()=>import("./ProductsManageView-BlGcTlSA.js"),__vite__mapDeps([39,1,40]),import.meta.url),meta:{title:"产品管理",requiresAuth:!0,requiresAdmin:!0}},{path:"local",name:"AdminLocal",component:()=>ar(()=>import("./LocalManagementView-BEHQLmBq.js"),__vite__mapDeps([41,1,42]),import.meta.url),meta:{title:"本地管理",requiresAuth:!0,requiresAdmin:!0}},{path:"local-test",name:"AdminLocalTest",component:()=>ar(()=>import("./LocalManagementTestView-CWc-a4IA.js"),__vite__mapDeps([43,1,44]),import.meta.url),meta:{title:"本地管理测试",requiresAuth:!0,requiresAdmin:!0}}]},{path:"/payment",name:"Payment",component:()=>ar(()=>import("./PaymentView-CkXy32NF.js"),__vite__mapDeps([45,1,46]),import.meta.url),meta:{title:"支付页面",description:"安全的支付处理",requiresAuth:!0}},{path:"/payment/success",name:"PaymentSuccess",component:()=>ar(()=>import("./PaymentSuccessView-cOBijyKY.js"),__vite__mapDeps([47,1,48]),import.meta.url),meta:{title:"支付成功",description:"支付完成确认"}},{path:"/payment/cancel",name:"PaymentCancel",component:()=>ar(()=>import("./PaymentCancelView-D-y4Rq7n.js"),__vite__mapDeps([49,1,50]),import.meta.url),meta:{title:"支付取消",description:"支付已取消"}},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>ar(()=>import("./NotFoundView-DxjTcsqR.js"),__vite__mapDeps([51,1,52]),import.meta.url),meta:{title:"页面未找到",description:"您访问的页面不存在"}}],Kc=function(e){const t=At(e.routes,e),s=e.parseQuery||Dt,o=e.stringifyQuery||qt,i=e.history,l=Gt(),c=Gt(),u=Gt(),h=r(Xe);let f=Xe;be&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=Ee.bind(null,e=>""+e),m=Ee.bind(null,ze),v=Ee.bind(null,Ge);function g(e,r){if(r=Se({},r||h.value),"string"==typeof e){const a=We(s,e,r.path),n=t.resolve({path:a.path},r),o=i.createHref(a.fullPath);return Se(a,n,{params:v(n.params),hash:Ge(a.hash),redirectedFrom:void 0,href:o})}let a;if(null!=e.path)a=Se({},e,{path:We(s,e.path,r.path).path});else{const t=Se({},e.params);for(const e in t)null==t[e]&&delete t[e];a=Se({},e,{params:m(t)}),r.params=m(r.params)}const n=t.resolve(a,r),l=e.hash||"";n.params=p(v(n.params));const c=function(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}(o,Se({},e,{hash:(u=l,Be(u).replace(De,"{").replace(Ne,"}").replace(Le,"^")),path:n.path}));var u;const d=i.createHref(c);return Se({fullPath:c,hash:l,query:o===qt?Nt(e.query):e.query||{}},n,{redirectedFrom:void 0,href:d})}function y(e){return"string"==typeof e?We(s,e,h.value.path):Se({},e)}function w(e,t){if(f!==e)return yt(8,{from:t,to:e})}function _(e){return k(e)}function b(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:r}=t;let s="function"==typeof r?r(e):r;return"string"==typeof s&&(s=s.includes("?")||s.includes("#")?s=y(s):{path:s},s.params={}),Se({query:e.query,hash:e.hash,params:null!=s.path?{}:e.params},s)}}function k(e,t){const r=f=g(e),s=h.value,a=e.state,n=e.force,i=!0===e.replace,l=b(r);if(l)return k(Se(y(l),{state:"object"==typeof l?Se({},a,l.state):a,force:n,replace:i}),t||r);const c=r;let u;return c.redirectedFrom=t,!n&&function(e,t,r){const s=t.matched.length-1,a=r.matched.length-1;return s>-1&&s===a&&Ke(t.matched[s],r.matched[a])&&Qe(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}(o,s,r)&&(u=yt(16,{to:c,from:s}),L(s,s,!0,!1)),(u?Promise.resolve(u):T(c,s)).catch(e=>wt(e)?wt(e,2)?e:$(e):x(e,c,s)).then(e=>{if(e){if(wt(e,2))return k(Se({replace:i},y(e.to),{state:"object"==typeof e.to?Se({},a,e.to.state):a,force:n}),t||c)}else e=C(c,s,!0,i,a);return O(c,s,e),e})}function S(e,t){const r=w(e,t);return r?Promise.reject(r):Promise.resolve()}function E(e){const t=q.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function T(e,t){let r;const[s,a,n]=function(e,t){const r=[],s=[],a=[],n=Math.max(t.matched.length,e.matched.length);for(let o=0;o<n;o++){const n=t.matched[o];n&&(e.matched.find(e=>Ke(e,n))?s.push(n):r.push(n));const i=e.matched[o];i&&(t.matched.find(e=>Ke(e,i))||a.push(i))}return[r,s,a]}(e,t);r=Wt(s.reverse(),"beforeRouteLeave",e,t);for(const i of s)i.leaveGuards.forEach(s=>{r.push(Jt(s,e,t))});const o=S.bind(null,e,t);return r.push(o),M(r).then(()=>{r=[];for(const s of l.list())r.push(Jt(s,e,t));return r.push(o),M(r)}).then(()=>{r=Wt(a,"beforeRouteUpdate",e,t);for(const s of a)s.updateGuards.forEach(s=>{r.push(Jt(s,e,t))});return r.push(o),M(r)}).then(()=>{r=[];for(const s of n)if(s.beforeEnter)if(Oe(s.beforeEnter))for(const a of s.beforeEnter)r.push(Jt(a,e,t));else r.push(Jt(s.beforeEnter,e,t));return r.push(o),M(r)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),r=Wt(n,"beforeRouteEnter",e,t,E),r.push(o),M(r))).then(()=>{r=[];for(const s of c.list())r.push(Jt(s,e,t));return r.push(o),M(r)}).catch(e=>wt(e,8)?e:Promise.reject(e))}function O(e,t,r){u.list().forEach(s=>E(()=>s(e,t,r)))}function C(e,t,r,s,a){const n=w(e,t);if(n)return n;const o=t===Xe,l=be?history.state:{};r&&(s||o?i.replace(e.fullPath,Se({scroll:o&&l&&l.scroll},a)):i.push(e.fullPath,a)),h.value=e,L(e,t,r,o),$()}let P;function A(){P||(P=i.listen((e,t,r)=>{if(!N.listening)return;const s=g(e),a=b(s);if(a)return void k(Se(a,{replace:!0,force:!0}),s).catch(Te);f=s;const n=h.value;var o,l;be&&(o=ct(n.fullPath,r.delta),l=it(),ut.set(o,l)),T(s,n).catch(e=>wt(e,12)?e:wt(e,2)?(k(Se(y(e.to),{force:!0}),s).then(e=>{wt(e,20)&&!r.delta&&r.type===et.pop&&i.go(-1,!1)}).catch(Te),Promise.reject()):(r.delta&&i.go(-r.delta,!1),x(e,s,n))).then(e=>{(e=e||C(s,n,!1))&&(r.delta&&!wt(e,8)?i.go(-r.delta,!1):r.type===et.pop&&wt(e,20)&&i.go(-1,!1)),O(s,n,e)}).catch(Te)}))}let j,I=Gt(),R=Gt();function x(e,t,r){$(e);const s=R.list();return s.length?s.forEach(s=>s(e,t,r)):console.error(e),Promise.reject(e)}function $(e){return j||(j=!e,A(),I.list().forEach(([t,r])=>e?r(e):t()),I.reset()),e}function L(t,r,s,a){const{scrollBehavior:n}=e;if(!be||!n)return Promise.resolve();const o=!s&&function(e){const t=ut.get(e);return ut.delete(e),t}(ct(t.fullPath,0))||(a||!s)&&history.state&&history.state.scroll||null;return d().then(()=>n(t,r,o)).then(e=>e&&lt(e)).catch(e=>x(e,t,r))}const U=e=>i.go(e);let D;const q=new Set,N={currentRoute:h,listening:!0,addRoute:function(e,r){let s,a;return pt(e)?(s=t.getRecordMatcher(e),a=r):a=e,t.addRoute(a,s)},removeRoute:function(e){const r=t.getRecordMatcher(e);r&&t.removeRoute(r)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map(e=>e.record)},resolve:g,options:e,push:_,replace:function(e){return _(Se(y(e),{replace:!0}))},go:U,back:()=>U(-1),forward:()=>U(1),beforeEach:l.add,beforeResolve:c.add,afterEach:u.add,onError:R.add,isReady:function(){return j&&h.value!==Xe?Promise.resolve():new Promise((e,t)=>{I.add([e,t])})},install(e){e.component("RouterLink",Qt),e.component("RouterView",er),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>a(h)}),be&&!D&&h.value===Xe&&(D=!0,_(i.location).catch(e=>{}));const t={};for(const s in Xe)Object.defineProperty(t,s,{get:()=>h.value[s],enumerable:!0});e.provide(Ft,this),e.provide(Vt,n(t)),e.provide(zt,h);const r=e.unmount;q.add(e),e.unmount=function(){q.delete(e),q.size<1&&(f=Xe,P&&P(),P=null,h.value=Xe,D=!1,j=!1),r()}}};function M(e){return e.reduce((e,t)=>e.then(()=>E(t)),Promise.resolve())}return N}({history:function(e){const t=ft(e=at(e)),r=function(e,t,r,s){let a=[],n=[],o=null;const i=({state:n})=>{const i=dt(e,location),l=r.value,c=t.value;let u=0;if(n){if(r.value=i,t.value=n,o&&o===l)return void(o=null);u=c?n.position-c.position:0}else s(i);a.forEach(e=>{e(r.value,l,{delta:u,type:et.pop,direction:u?u>0?rt.forward:rt.back:rt.unknown})})};function l(){const{history:e}=window;e.state&&e.replaceState(Se({},e.state,{scroll:it()}),"")}return window.addEventListener("popstate",i),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){o=r.value},listen:function(e){a.push(e);const t=()=>{const t=a.indexOf(e);t>-1&&a.splice(t,1)};return n.push(t),t},destroy:function(){for(const e of n)e();n=[],window.removeEventListener("popstate",i),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace),s=Se({location:"",base:e,go:function(e,t=!0){t||r.pauseListeners(),history.go(e)},createHref:ot.bind(null,e)},t,r);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}(),routes:Hc});const Qc={TOOLS:"local_tools",CATEGORIES:"local_categories",USER_PREFERENCES:"user_preferences",THEME_CONFIG:"theme_config",OFFLINE_QUEUE:"offline_queue",LAST_SYNC:"last_sync_time",APP_CONFIG:"app_config"};class Yc{static getLocalTools(){try{const e=localStorage.getItem(Qc.TOOLS);return e?JSON.parse(e):[]}catch(e){return console.error("获取本地工具数据失败:",e),[]}}static saveLocalTools(e){try{localStorage.setItem(Qc.TOOLS,JSON.stringify(e))}catch(t){console.error("保存本地工具数据失败:",t)}}static addLocalTool(e){const t={...e,localId:`local_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,isLocal:!0,lastModified:(new Date).toISOString(),syncStatus:"pending"},r=this.getLocalTools();return r.push(t),this.saveLocalTools(r),this.addOfflineAction({type:"create",entity:"tool",data:t}),t}static updateLocalTool(e,t){try{const r=this.getLocalTools(),s=r.findIndex(t=>t.id===e||t.localId===e);return-1!==s&&(r[s]={...r[s],...t,lastModified:(new Date).toISOString(),syncStatus:"pending"},this.saveLocalTools(r),this.addOfflineAction({type:"update",entity:"tool",data:{id:e,updates:t}}),!0)}catch(r){return console.error("更新本地工具失败:",r),!1}}static deleteLocalTool(e){try{const t=this.getLocalTools(),r=t.findIndex(t=>t.id===e||t.localId===e);if(-1===r)return!1;const s=t[r];return t.splice(r,1),this.saveLocalTools(t),this.addOfflineAction({type:"delete",entity:"tool",data:{id:s.id||s.localId}}),!0}catch(t){return console.error("删除本地工具失败:",t),!1}}static getLocalCategories(){try{const e=localStorage.getItem(Qc.CATEGORIES);return e?JSON.parse(e):[]}catch(e){return console.error("获取本地分类数据失败:",e),[]}}static saveLocalCategories(e){try{localStorage.setItem(Qc.CATEGORIES,JSON.stringify(e))}catch(t){console.error("保存本地分类数据失败:",t)}}static getUserPreferences(){try{const e=localStorage.getItem(Qc.USER_PREFERENCES),t={theme:"auto",language:"zh-CN",sidebarCollapsed:!1,defaultView:"grid",autoSync:!0,offlineMode:!1};return e?{...t,...JSON.parse(e)}:t}catch(e){return console.error("获取用户偏好设置失败:",e),{theme:"auto",language:"zh-CN",sidebarCollapsed:!1,defaultView:"grid",autoSync:!0,offlineMode:!1}}}static saveUserPreferences(e){try{const t={...this.getUserPreferences(),...e};localStorage.setItem(Qc.USER_PREFERENCES,JSON.stringify(t))}catch(t){console.error("保存用户偏好设置失败:",t)}}static getOfflineQueue(){try{const e=localStorage.getItem(Qc.OFFLINE_QUEUE);return e?JSON.parse(e):[]}catch(e){return console.error("获取离线队列失败:",e),[]}}static addOfflineAction(e){try{const t=this.getOfflineQueue(),r={...e,id:`action_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,timestamp:(new Date).toISOString()};t.push(r),localStorage.setItem(Qc.OFFLINE_QUEUE,JSON.stringify(t))}catch(t){console.error("添加离线操作失败:",t)}}static clearOfflineQueue(){try{localStorage.removeItem(Qc.OFFLINE_QUEUE)}catch(e){console.error("清空离线队列失败:",e)}}static getLastSyncTime(){return localStorage.getItem(Qc.LAST_SYNC)}static setLastSyncTime(e=(new Date).toISOString()){localStorage.setItem(Qc.LAST_SYNC,e)}static clearAllLocalData(){try{Object.values(Qc).forEach(e=>{localStorage.removeItem(e)})}catch(e){console.error("清空本地数据失败:",e)}}static getStorageInfo(){try{let e=0;for(const r in localStorage)localStorage.hasOwnProperty(r)&&(e+=localStorage[r].length);const t=5242880;return{used:e,total:t,percentage:e/t*100}}catch(e){return console.error("获取存储信息失败:",e),{used:0,total:0,percentage:0}}}}const Zc=p("localManagement",()=>{const e=l(!1),t=l(!1),r=l(null),s=l(null),a=l(Yc.getUserPreferences()),n=l([]),i=l([]),c=l([]),u=o(()=>c.value.length),d=o(()=>n.value.some(e=>"pending"===e.syncStatus)||i.value.some(e=>"pending"===e.syncStatus)),h=o(()=>Yc.getStorageInfo()),f=o(()=>navigator.onLine&&!e.value),p=async()=>{e.value=!1,a.value.autoSync&&u.value>0&&await g()},m=()=>{e.value=!0},v=e=>{try{Yc.saveUserPreferences(e),a.value=Yc.getUserPreferences()}catch(t){throw console.error("更新用户偏好失败:",t),t}},g=async()=>{if(!t.value&&f.value)try{t.value=!0,r.value=null;const e=Yc.getOfflineQueue();for(const t of e)await y(t);Yc.clearOfflineQueue(),c.value=[];const a=(new Date).toISOString();Yc.setLastSyncTime(a),s.value=a,b()}catch(e){throw console.error("数据同步失败:",e),r.value=e instanceof Error?e.message:"同步失败",e}finally{t.value=!1}},y=async e=>{try{switch(e.entity){case"tool":await w(e);break;case"category":await _(e);break;default:console.warn("未知的离线操作类型:",e)}}catch(t){throw console.error("处理离线操作失败:",e,t),t}},w=async e=>{switch(e.type){case"create":await zn.createTool(e.data);break;case"update":await zn.updateTool(e.data.id,e.data.updates);break;case"delete":await zn.deleteTool(e.data.id)}},_=async e=>{switch(e.type){case"create":await Gn.createCategory(e.data);break;case"update":await Gn.updateCategory(e.data.id,e.data.updates);break;case"delete":await Gn.deleteCategory(e.data.id)}},b=()=>{n.value=n.value.map(e=>({...e,syncStatus:"synced"})),Yc.saveLocalTools(n.value),i.value=i.value.map(e=>({...e,syncStatus:"synced"})),Yc.saveLocalCategories(i.value)};return{isOfflineMode:e,isSyncing:t,syncError:r,lastSyncTime:s,userPreferences:a,localTools:n,localCategories:i,offlineQueue:c,pendingSyncCount:u,hasLocalChanges:d,storageInfo:h,isOnline:f,initialize:async()=>{try{n.value=Yc.getLocalTools(),i.value=Yc.getLocalCategories(),c.value=Yc.getOfflineQueue(),s.value=Yc.getLastSyncTime(),window.addEventListener("online",p),window.addEventListener("offline",m),f.value&&a.value.autoSync&&u.value>0&&await g()}catch(e){console.error("初始化本地管理失败:",e)}},addLocalTool:e=>{try{const t=Yc.addLocalTool(e);return n.value.push(t),c.value=Yc.getOfflineQueue(),t}catch(t){throw console.error("添加本地工具失败:",t),t}},updateLocalTool:(e,t)=>{try{const r=Yc.updateLocalTool(e,t);return r&&(n.value=Yc.getLocalTools(),c.value=Yc.getOfflineQueue()),r}catch(r){throw console.error("更新本地工具失败:",r),r}},deleteLocalTool:e=>{try{const t=Yc.deleteLocalTool(e);return t&&(n.value=Yc.getLocalTools(),c.value=Yc.getOfflineQueue()),t}catch(t){throw console.error("删除本地工具失败:",t),t}},updateUserPreferences:v,syncData:g,forceSyncData:async()=>{if(!f.value)throw new Error("网络不可用，无法同步数据");await g()},toggleOfflineMode:()=>{e.value=!e.value,v({offlineMode:e.value})},clearLocalData:()=>{try{Yc.clearAllLocalData(),n.value=[],i.value=[],c.value=[],s.value=null,a.value=Yc.getUserPreferences()}catch(e){throw console.error("清空本地数据失败:",e),e}},exportLocalData:()=>{try{const e={tools:n.value,categories:i.value,preferences:a.value,exportTime:(new Date).toISOString()},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),r=URL.createObjectURL(t),s=document.createElement("a");s.href=r,s.download=`local-data-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(r)}catch(e){throw console.error("导出本地数据失败:",e),e}},importLocalData:async e=>{try{const t=await e.text(),r=JSON.parse(t);r.tools&&(n.value=r.tools,Yc.saveLocalTools(r.tools)),r.categories&&(i.value=r.categories,Yc.saveLocalCategories(r.categories)),r.preferences&&v(r.preferences)}catch(t){throw console.error("导入本地数据失败:",t),t}}}}),Xc=we(Wc),eu=_e();Xc.use(eu),Xc.use(Kc),Kc.beforeEach(async(e,t,r)=>{const s=Kn(),a=Jn(),n=["Home","EnhancedHome","Products","ProductDetail","NotFound"].includes(e.name);try{const t=e.meta.title;if(t&&(document.title=`${t} - 工具导航站`),n&&!e.meta.requiresAuth&&!e.meta.requiresAdmin)return r();if(e.meta.requiresAuth||e.meta.requiresAdmin){if(await s.initialize(),e.meta.requiresAuth&&!s.isAuthenticated)return r({name:"Login",query:{redirect:e.fullPath},replace:!0});if(e.meta.requiresAdmin&&!s.isAdmin)return r({name:"Home",replace:!0})}if(!a.initialized)try{await a.initialize()}catch(o){console.error("初始化工具数据失败:",o)}r()}catch(o){console.error("路由守卫错误:",o),r({name:"Home",replace:!0})}}),Kc.afterEach(()=>{});Zc().initialize(),Xc.mount("#app");export{Hn as A,Gn as C,Yc as L,Bn as T,Wn as U,li as _,so as a,Jn as b,Kn as c,rr as d,Zc as e,pr as g,Mn as s,tr as u};
