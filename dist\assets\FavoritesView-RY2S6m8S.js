import{u as e,_ as a}from"./index-BWTdsPe5.js";import{d as s,i as t,j as i,c,a as l,p as o,F as n,s as r,q as d,H as u,h as v,J as p,t as m,ae as y,af as k,e as g,u as b,X as h,a8 as f,w as C,r as _,a7 as w,ab as A,o as S}from"./vendor-DhoxJlSg.js";const O={class:"favorites-view"},D={class:"favorites-tabs"},j=["onClick"],F={class:"tab-count"},I={class:"favorites-content"},P={key:0,class:"tab-panel"},x={key:0,class:"favorites-grid"},V={class:"item-header"},$={class:"item-icon"},q=["onClick"],B={class:"item-content"},H={class:"item-name"},J={class:"item-description"},N={class:"item-meta"},T={class:"category"},Y={class:"clicks"},z={class:"item-actions"},E=["onClick"],G={key:1,class:"empty-state"},K={key:1,class:"tab-panel"},L={key:0,class:"favorites-grid"},M={class:"item-header"},Q={class:"item-image"},R=["src","alt"],U=["onClick"],W={class:"item-content"},X={class:"item-name"},Z={class:"item-description"},ee={class:"item-price"},ae={class:"current-price"},se={key:0,class:"original-price"},te={class:"item-actions"},ie=["onClick"],ce=["onClick"],le={key:1,class:"empty-state"},oe={key:0,class:"loading-state"},ne=a(s({__name:"FavoritesView",setup(a){const s=e(),ne=t(!0),re=t("tools"),de=t([]),ue=t([]),ve=[{key:"tools",label:"工具",icon:y},{key:"products",label:"产品",icon:k}],pe=async(e,a)=>{try{"tool"===e?de.value=de.value.filter(e=>e.id!==a):ue.value=ue.value.filter(e=>e.id!==a)}catch(s){console.error("取消收藏失败:",s)}};return i(()=>{(async()=>{try{ne.value=!0,await new Promise(e=>setTimeout(e,1e3)),de.value=[{id:"1",name:"VS Code",description:"强大的代码编辑器",url:"https://code.visualstudio.com",icon:"💻",category:{id:"1",name:"开发工具",icon:"💻",color:"#0078d4",count:0,sortOrder:0,isActive:!0,createdAt:"",updatedAt:""},tags:[],isFavorite:!0,clickCount:150,isFeature:!0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),sortOrder:0}],ue.value=[{id:"1",name:"高效办公套件",description:"提升办公效率的完整解决方案",shortDescription:"办公效率工具",price:299,originalPrice:399,currency:"CNY",category:{id:"1",name:"办公软件",icon:"📊",color:"#0078d4",count:0,sortOrder:0,isActive:!0,createdAt:"",updatedAt:""},images:["/placeholder.jpg"],features:["文档处理","项目管理"],isFeatured:!0,isDigital:!0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),createdBy:"user1",sortOrder:0}]}catch(e){console.error("加载收藏失败:",e)}finally{ne.value=!1}})()}),(e,a)=>{const t=_("router-link");return S(),c("div",O,[a[12]||(a[12]=l("div",{class:"favorites-header"},[l("h1",null,"我的收藏"),l("p",null,"管理您收藏的工具和产品")],-1)),l("div",D,[(S(),c(n,null,r(ve,e=>{return l("button",{key:e.key,class:d(["tab",{active:re.value===e.key}]),onClick:a=>re.value=e.key},[(S(),u(p(e.icon),{class:"tab-icon"})),v(" "+m(e.label)+" ",1),l("span",F,m((a=e.key,"tools"===a?de.value.length:"products"===a?ue.value.length:0)),1)],10,j);var a}),64))]),l("div",I,["tools"===re.value?(S(),c("div",P,[de.value.length>0?(S(),c("div",x,[(S(!0),c(n,null,r(de.value,e=>(S(),c("div",{key:e.id,class:"favorite-item tool-item"},[l("div",V,[l("div",$,m(e.icon||"🔧"),1),l("button",{class:"remove-btn",onClick:a=>pe("tool",e.id)},[g(b(h),{class:"icon"})],8,q)]),l("div",B,[l("h3",H,m(e.name),1),l("p",J,m(e.description),1),l("div",N,[l("span",T,m(e.category.name),1),l("span",Y,m(e.clickCount)+" 次访问",1)])]),l("div",z,[l("button",{class:"action-btn primary",onClick:a=>(e=>{window.open(e.url,"_blank","noopener,noreferrer")})(e)},[g(b(f),{class:"icon"}),a[0]||(a[0]=v(" 打开工具 "))],8,E)])]))),128))])):(S(),c("div",G,[a[2]||(a[2]=l("div",{class:"empty-icon"},"🔧",-1)),a[3]||(a[3]=l("h3",null,"暂无收藏的工具",-1)),a[4]||(a[4]=l("p",null,"去发现一些有用的工具并收藏它们吧！",-1)),g(t,{to:"/tools",class:"empty-action"},{default:C(()=>a[1]||(a[1]=[v("浏览工具")])),_:1,__:[1]})]))])):o("",!0),"products"===re.value?(S(),c("div",K,[ue.value.length>0?(S(),c("div",L,[(S(!0),c(n,null,r(ue.value,e=>(S(),c("div",{key:e.id,class:"favorite-item product-item"},[l("div",M,[l("div",Q,[l("img",{src:e.images[0]||"/placeholder.jpg",alt:e.name},null,8,R)]),l("button",{class:"remove-btn",onClick:a=>pe("product",e.id)},[g(b(h),{class:"icon"})],8,U)]),l("div",W,[l("h3",X,m(e.name),1),l("p",Z,m(e.shortDescription||e.description),1),l("div",ee,[l("span",ae,"¥"+m(e.price),1),e.originalPrice&&e.originalPrice>e.price?(S(),c("span",se," ¥"+m(e.originalPrice),1)):o("",!0)])]),l("div",te,[l("button",{class:"action-btn secondary",onClick:a=>(e=>{s.push(`/product/${e.id}`)})(e)},[g(b(w),{class:"icon"}),a[5]||(a[5]=v(" 查看详情 "))],8,ie),l("button",{class:"action-btn primary",onClick:a=>(e=>{s.push(`/payment?product=${e.id}`)})(e)},[g(b(A),{class:"icon"}),a[6]||(a[6]=v(" 立即购买 "))],8,ce)])]))),128))])):(S(),c("div",le,[a[8]||(a[8]=l("div",{class:"empty-icon"},"🛍️",-1)),a[9]||(a[9]=l("h3",null,"暂无收藏的产品",-1)),a[10]||(a[10]=l("p",null,"去发现一些优质产品并收藏它们吧！",-1)),g(t,{to:"/products",class:"empty-action"},{default:C(()=>a[7]||(a[7]=[v("浏览产品")])),_:1,__:[7]})]))])):o("",!0)]),ne.value?(S(),c("div",oe,a[11]||(a[11]=[l("div",{class:"loading-spinner"},null,-1),l("p",null,"正在加载收藏...",-1)]))):o("",!0)])}}}),[["__scopeId","data-v-e6287a0c"]]);export{ne as default};
