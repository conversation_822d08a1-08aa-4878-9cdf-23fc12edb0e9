import{d as s,c as e,a,e as r,r as o,o as t}from"./vendor-gTrNmiX-.js";import{_ as n}from"./index-BBkjtX5T.js";const i={class:"user-view"},c={class:"container"},d=n(s({__name:"UserView",setup:s=>(s,n)=>{const d=o("router-view");return t(),e("div",i,[a("div",c,[n[0]||(n[0]=a("h1",null,"个人中心",-1)),n[1]||(n[1]=a("p",null,"用户功能正在开发中...",-1)),r(d)])])}}),[["__scopeId","data-v-9b4e7b4c"]]);export{d as default};
