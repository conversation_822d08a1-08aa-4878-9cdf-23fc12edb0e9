import{d as s,i as a,m as e,j as l,c,a as i,p as t,e as o,h as n,u as d,aa as r,x as u,Y as p,z as v,F as m,s as g,q as h,t as b,a0 as f,a9 as k,v as y,ab as C,a1 as w,X as x,o as P}from"./vendor-gTrNmiX-.js";import{a as _,_ as L}from"./index-BBkjtX5T.js";const j={class:"products-view"},I={class:"page-header"},U={class:"header-content"},V={class:"header-right"},q={class:"filters-section"},z={class:"filters-content"},F={class:"search-box"},Y={class:"filter-tabs"},$=["onClick"],A={class:"count"},B={class:"view-controls"},D={class:"products-section"},E={key:0,class:"products-container"},G=["onClick"],H={class:"product-image"},J=["src","alt"],K={class:"product-price"},M={class:"price"},N={key:0,class:"original-price"},O={class:"product-content"},Q={class:"product-name"},R={class:"product-description"},S={class:"product-tags"},T={key:0,class:"tag more"},W={class:"product-footer"},X=["onClick"],Z=["onClick"],ss={key:1,class:"empty-state"},as={class:"modal-header"},es=L(s({__name:"ProductsView",setup(s){const L=_(),es=a(""),ls=a("all"),cs=a("grid"),is=a(!1),ts=a([{id:1,name:"高效办公套件",description:"提升办公效率的完整解决方案，包含文档处理、项目管理等功能",price:299,originalPrice:399,image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",category:"office",tags:["办公","效率","文档","项目管理"]},{id:2,name:"设计师工具包",description:"专业设计师必备工具集合，包含UI设计、图标制作等",price:199,image:"https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=400&h=300&fit=crop",category:"design",tags:["设计","UI","图标","创意"]},{id:3,name:"开发者助手",description:"程序员开发必备工具，代码编辑、调试、部署一站式解决",price:399,originalPrice:499,image:"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop",category:"development",tags:["开发","编程","调试","部署"]}]),os=a([{id:"all",name:"全部",count:3},{id:"office",name:"办公工具",count:1},{id:"design",name:"设计工具",count:1},{id:"development",name:"开发工具",count:1}]),ns=e(()=>{let s=ts.value;if("all"!==ls.value&&(s=s.filter(s=>s.category===ls.value)),es.value.trim()){const a=es.value.toLowerCase();s=s.filter(s=>s.name.toLowerCase().includes(a)||s.description.toLowerCase().includes(a)||s.tags.some(s=>s.toLowerCase().includes(a)))}return s});return l(()=>{console.log("产品页面已加载")}),(s,a)=>(P(),c("div",j,[i("div",I,[i("div",U,[a[9]||(a[9]=i("div",{class:"header-left"},[i("h1",{class:"page-title"},"产品展示"),i("p",{class:"page-description"},"发现优质产品，提升工作效率")],-1)),i("div",V,[i("button",{class:"add-product-btn",onClick:a[0]||(a[0]=s=>is.value=!0)},[o(d(r),{class:"icon"}),a[8]||(a[8]=n(" 添加产品 "))])])])]),i("div",q,[i("div",z,[i("div",F,[o(d(p),{class:"search-icon"}),u(i("input",{"onUpdate:modelValue":a[1]||(a[1]=s=>es.value=s),type:"text",placeholder:"搜索产品...",class:"search-input"},null,512),[[v,es.value]])]),i("div",Y,[(P(!0),c(m,null,g(os.value,s=>(P(),c("button",{key:s.id,class:h(["filter-tab",{active:ls.value===s.id}]),onClick:a=>ls.value=s.id},[n(b(s.name)+" ",1),i("span",A,b(s.count),1)],10,$))),128))]),i("div",B,[i("button",{class:h(["view-btn",{active:"grid"===cs.value}]),onClick:a[2]||(a[2]=s=>cs.value="grid")},[o(d(f),{class:"icon"})],2),i("button",{class:h(["view-btn",{active:"list"===cs.value}]),onClick:a[3]||(a[3]=s=>cs.value="list")},[o(d(k),{class:"icon"})],2)])])]),i("div",D,[ns.value.length>0?(P(),c("div",E,[i("div",{class:h(["products-grid",{"list-view":"list"===cs.value}])},[(P(!0),c(m,null,g(ns.value,s=>(P(),c("div",{key:s.id,class:"product-card",onClick:a=>{return e=s.id,void L.push(`/product/${e}`);var e}},[i("div",H,[i("img",{src:s.image,alt:s.name},null,8,J),i("div",K,[i("span",M,"¥"+b(s.price),1),s.originalPrice?(P(),c("span",N," ¥"+b(s.originalPrice),1)):t("",!0)])]),i("div",O,[i("h3",Q,b(s.name),1),i("p",R,b(s.description),1),i("div",S,[(P(!0),c(m,null,g(s.tags.slice(0,3),s=>(P(),c("span",{key:s,class:"tag"},b(s),1))),128)),s.tags.length>3?(P(),c("span",T," +"+b(s.tags.length-3),1)):t("",!0)])]),i("div",W,[i("button",{class:"buy-btn",onClick:y(a=>(s=>{console.log("购买产品:",s.name)})(s),["stop"])},[o(d(C),{class:"icon"}),a[10]||(a[10]=n(" 立即购买 "))],8,X),i("button",{class:"demo-btn",onClick:y(a=>(s=>{console.log("预览产品:",s.name)})(s),["stop"])},[o(d(w),{class:"icon"}),a[11]||(a[11]=n(" 预览 "))],8,Z)])],8,G))),128))],2)])):(P(),c("div",ss,[a[12]||(a[12]=i("div",{class:"empty-icon"},"📦",-1)),a[13]||(a[13]=i("h3",null,"暂无产品",-1)),a[14]||(a[14]=i("p",null,"当前分类下没有找到相关产品",-1)),i("button",{class:"empty-action",onClick:a[4]||(a[4]=s=>ls.value="all")}," 查看全部产品 ")]))]),is.value?(P(),c("div",{key:0,class:"modal-overlay",onClick:a[7]||(a[7]=s=>is.value=!1)},[i("div",{class:"modal-content",onClick:a[6]||(a[6]=y(()=>{},["stop"]))},[i("div",as,[a[15]||(a[15]=i("h3",null,"添加新产品",-1)),i("button",{class:"close-btn",onClick:a[5]||(a[5]=s=>is.value=!1)},[o(d(x),{class:"icon"})])]),a[16]||(a[16]=i("div",{class:"modal-body"},[i("p",null,"产品添加功能正在开发中...")],-1))])])):t("",!0)]))}}),[["__scopeId","data-v-632a3abb"]]);export{es as default};
