import{c as s,u as a,_ as c}from"./index-BWTdsPe5.js";import{d as t,i as l,j as e,c as n,a as o,p as i,e as p,u,ax as d,t as r,au as v,ab as m,w as _,h as f,r as h,aC as b,f as y,P as x,o as w}from"./vendor-DhoxJlSg.js";const C={class:"payment-cancel-view"},j={class:"cancel-container"},k={class:"cancel-content"},g={class:"cancel-icon"},P={key:0,class:"order-info"},q={class:"info-item"},z={class:"value"},D={class:"info-item"},I={class:"value"},L={class:"next-steps"},N={class:"steps-list"},S={class:"step-item"},V={class:"step-item"},$={class:"step-content"},A={class:"step-item"},B={class:"action-buttons"},E={class:"support-info"},F={class:"contact-methods"},G={href:"mailto:<EMAIL>",class:"contact-item"},H={href:"tel:************",class:"contact-item"},J=c(t({__name:"PaymentCancelView",setup(c){const t=s(),J=a(),K=l(""),M=l(""),O=()=>{K.value?J.push(`/payment?order=${K.value}`):J.push("/payment")};return e(()=>{(()=>{const s=t.query.order;s&&(K.value=s),M.value=(new Date).toLocaleString("zh-CN")})()}),(s,a)=>{const c=h("router-link");return w(),n("div",C,[o("div",j,[o("div",k,[o("div",g,[p(u(d),{class:"icon"})]),a[14]||(a[14]=o("h1",{class:"cancel-title"},"支付已取消",-1)),a[15]||(a[15]=o("p",{class:"cancel-message"},"您的支付已被取消，订单未完成",-1)),K.value?(w(),n("div",P,[o("div",q,[a[0]||(a[0]=o("span",{class:"label"},"订单号:",-1)),o("span",z,r(K.value),1)]),o("div",D,[a[1]||(a[1]=o("span",{class:"label"},"取消时间:",-1)),o("span",I,r(M.value),1)])])):i("",!0),o("div",L,[a[8]||(a[8]=o("h3",null,"接下来您可以：",-1)),o("div",N,[o("div",S,[p(u(v),{class:"step-icon"}),o("div",{class:"step-content"},[a[2]||(a[2]=o("h4",null,"重新支付",-1)),a[3]||(a[3]=o("p",null,"返回支付页面完成订单支付",-1)),o("button",{class:"step-action",onClick:O}," 重新支付 ")])]),o("div",V,[p(u(m),{class:"step-icon"}),o("div",$,[a[5]||(a[5]=o("h4",null,"继续购物",-1)),a[6]||(a[6]=o("p",null,"浏览更多优质产品",-1)),p(c,{to:"/products",class:"step-action"},{default:_(()=>a[4]||(a[4]=[f(" 继续购物 ")])),_:1,__:[4]})])]),o("div",A,[p(u(b),{class:"step-icon"}),a[7]||(a[7]=o("div",{class:"step-content"},[o("h4",null,"联系客服"),o("p",null,"如有疑问，请联系我们的客服团队")],-1))])])]),o("div",B,[p(c,{to:"/",class:"btn btn-secondary"},{default:_(()=>a[9]||(a[9]=[f(" 返回首页 ")])),_:1,__:[9]}),p(c,{to:"/products",class:"btn btn-primary"},{default:_(()=>a[10]||(a[10]=[f(" 继续购物 ")])),_:1,__:[10]})]),o("div",E,[a[13]||(a[13]=o("p",null,"如需帮助，请联系我们的客服团队",-1)),o("div",F,[o("a",G,[p(u(y),{class:"contact-icon"}),a[11]||(a[11]=f(" <EMAIL> "))]),o("a",H,[p(u(x),{class:"contact-icon"}),a[12]||(a[12]=f(" ************ "))])])])])])])}}}),[["__scopeId","data-v-2c046c66"]]);export{J as default};
