import{U as a,u as s,A as n,_ as c}from"./index-BLSkTtYG.js";import{d as t,j as i,c as e,a as l,e as o,w as v,h as d,u as r,aj as m,r as u,ae as _,af as f,ak as p,al as h,am as w,an as y,ao as g,o as j}from"./vendor-DhoxJlSg.js";const b={class:"admin-view"},k={class:"admin-content"},x={class:"container"},A={class:"admin-layout"},C={class:"admin-nav"},U={class:"admin-main"},I=c(t({__name:"AdminView",setup(c){const t=s(),I=async()=>{try{await n.logout(),t.push("/")}catch(a){console.error("退出登录失败:",a)}};return i(()=>{(async()=>{try{const s=await a.getCurrentUser();if(!s||"admin"!==s.role&&"super_admin"!==s.role)return void t.push("/")}catch(s){console.error("检查管理员权限失败:",s),t.push("/auth/login")}})()}),(a,s)=>{const n=u("router-link"),c=u("router-view");return j(),e("div",b,[s[9]||(s[9]=l("div",{class:"admin-header"},[l("div",{class:"container"},[l("h1",null,"管理后台"),l("p",null,"系统管理和数据统计")])],-1)),l("div",k,[l("div",x,[l("div",A,[l("nav",C,[o(n,{to:"/admin/dashboard",class:"nav-item","active-class":"active"},{default:v(()=>[o(r(m),{class:"nav-icon"}),s[0]||(s[0]=d(" 仪表盘 "))]),_:1,__:[0]}),o(n,{to:"/admin/tools",class:"nav-item","active-class":"active"},{default:v(()=>[o(r(_),{class:"nav-icon"}),s[1]||(s[1]=d(" 工具管理 "))]),_:1,__:[1]}),o(n,{to:"/admin/products",class:"nav-item","active-class":"active"},{default:v(()=>[o(r(f),{class:"nav-icon"}),s[2]||(s[2]=d(" 产品管理 "))]),_:1,__:[2]}),o(n,{to:"/admin/users",class:"nav-item","active-class":"active"},{default:v(()=>[o(r(p),{class:"nav-icon"}),s[3]||(s[3]=d(" 用户管理 "))]),_:1,__:[3]}),o(n,{to:"/admin/orders",class:"nav-item","active-class":"active"},{default:v(()=>[o(r(h),{class:"nav-icon"}),s[4]||(s[4]=d(" 订单管理 "))]),_:1,__:[4]}),o(n,{to:"/admin/local",class:"nav-item","active-class":"active"},{default:v(()=>[o(r(w),{class:"nav-icon"}),s[5]||(s[5]=d(" 本地管理 "))]),_:1,__:[5]}),s[8]||(s[8]=l("div",{class:"nav-divider"},null,-1)),o(n,{to:"/",class:"nav-item"},{default:v(()=>[o(r(y),{class:"nav-icon"}),s[6]||(s[6]=d(" 返回首页 "))]),_:1,__:[6]}),l("button",{class:"nav-item logout-btn",onClick:I},[o(r(g),{class:"nav-icon"}),s[7]||(s[7]=d(" 退出登录 "))])]),l("main",U,[o(c)])])])])])}}}),[["__scopeId","data-v-2090faf2"]]);export{I as default};
