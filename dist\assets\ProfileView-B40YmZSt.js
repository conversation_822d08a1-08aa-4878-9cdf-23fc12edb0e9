import{d as e,i as a,m as l,j as s,c as u,a as i,e as o,u as t,ad as r,t as n,v,p as d,x as c,z as m,o as p}from"./vendor-DhoxJlSg.js";import{_ as f}from"./index-Dhbj_B_-.js";const b={class:"profile-view"},y={class:"profile-content"},g={class:"avatar-section"},h={class:"avatar-container"},w=["src","alt"],U={class:"avatar-info"},x={class:"form-section"},N={class:"form-row"},k={class:"form-group"},V=["disabled"],j={class:"form-group"},_=["disabled"],C={class:"form-group"},E={class:"form-group"},P=["disabled"],T={class:"form-row"},I={class:"form-group"},L=["disabled"],O={class:"form-group"},R=["disabled"],z={class:"form-actions"},S=["disabled"],q=["disabled"],A={key:0,class:"loading-spinner"},B={key:0,class:"error-message"},D={key:1,class:"success-message"},F=f(e({__name:"ProfileView",setup(e){const f=a(!1),F=a(null),G=a(!1),H=a(),J=a({id:"",email:"",username:"",fullName:"",avatarUrl:"",bio:"",website:"",location:""}),K=a({fullName:"",username:"",email:"",bio:"",website:"",location:""}),M=a({fullName:"",username:"",email:"",bio:"",website:"",location:""}),Q=l(()=>Object.keys(K.value).some(e=>K.value[e]!==M.value[e])),W=async()=>{try{f.value=!0,F.value=null,G.value=!1,await new Promise(e=>setTimeout(e,1e3)),J.value={...J.value,...K.value},M.value={...K.value},G.value=!0,setTimeout(()=>{G.value=!1},3e3)}catch(e){F.value=e instanceof Error?e.message:"保存失败，请重试"}finally{f.value=!1}},X=()=>{K.value={...M.value},F.value=null,G.value=!1},Y=()=>{var e;null==(e=H.value)||e.click()},Z=async e=>{var a;const l=null==(a=e.target.files)?void 0:a[0];if(l)try{f.value=!0,F.value=null,await new Promise(e=>setTimeout(e,1e3));const e=URL.createObjectURL(l);J.value.avatarUrl=e}catch(s){F.value=s instanceof Error?s.message:"头像上传失败"}finally{f.value=!1}};return s(()=>{(async()=>{try{f.value=!0,F.value=null;const e={id:"user-1",email:"<EMAIL>",username:"user123",fullName:"张三",avatarUrl:"",bio:"这是我的个人简介",website:"https://example.com",location:"北京"};J.value=e,K.value={...e},M.value={...e}}catch(e){F.value=e instanceof Error?e.message:"加载用户资料失败"}finally{f.value=!1}})()}),(e,a)=>(p(),u("div",b,[a[14]||(a[14]=i("div",{class:"profile-header"},[i("h1",null,"个人资料"),i("p",null,"管理您的个人信息和偏好设置")],-1)),i("div",y,[i("div",g,[i("div",h,[i("img",{src:J.value.avatarUrl||"/placeholder-user.jpg",alt:J.value.fullName,class:"avatar"},null,8,w),i("button",{class:"avatar-upload",onClick:Y},[o(t(r),{class:"icon"})]),i("input",{ref_key:"fileInput",ref:H,type:"file",accept:"image/*",style:{display:"none"},onChange:Z},null,544)]),i("div",U,[i("h3",null,n(J.value.fullName||"未设置姓名"),1),i("p",null,n(J.value.email),1)])]),i("form",{class:"profile-form",onSubmit:v(W,["prevent"])},[i("div",x,[a[13]||(a[13]=i("h3",null,"基本信息",-1)),i("div",N,[i("div",k,[a[6]||(a[6]=i("label",{for:"fullName"},"姓名",-1)),c(i("input",{id:"fullName","onUpdate:modelValue":a[0]||(a[0]=e=>K.value.fullName=e),type:"text",placeholder:"请输入您的姓名",disabled:f.value},null,8,V),[[m,K.value.fullName]])]),i("div",j,[a[7]||(a[7]=i("label",{for:"username"},"用户名",-1)),c(i("input",{id:"username","onUpdate:modelValue":a[1]||(a[1]=e=>K.value.username=e),type:"text",placeholder:"请输入用户名",disabled:f.value},null,8,_),[[m,K.value.username]])])]),i("div",C,[a[8]||(a[8]=i("label",{for:"email"},"邮箱地址",-1)),c(i("input",{id:"email","onUpdate:modelValue":a[2]||(a[2]=e=>K.value.email=e),type:"email",disabled:"",class:"readonly"},null,512),[[m,K.value.email]]),a[9]||(a[9]=i("small",{class:"form-hint"},"邮箱地址不可修改",-1))]),i("div",E,[a[10]||(a[10]=i("label",{for:"bio"},"个人简介",-1)),c(i("textarea",{id:"bio","onUpdate:modelValue":a[3]||(a[3]=e=>K.value.bio=e),rows:"4",placeholder:"介绍一下您自己...",disabled:f.value},null,8,P),[[m,K.value.bio]])]),i("div",T,[i("div",I,[a[11]||(a[11]=i("label",{for:"website"},"个人网站",-1)),c(i("input",{id:"website","onUpdate:modelValue":a[4]||(a[4]=e=>K.value.website=e),type:"url",placeholder:"https://example.com",disabled:f.value},null,8,L),[[m,K.value.website]])]),i("div",O,[a[12]||(a[12]=i("label",{for:"location"},"所在地",-1)),c(i("input",{id:"location","onUpdate:modelValue":a[5]||(a[5]=e=>K.value.location=e),type:"text",placeholder:"请输入您的所在地",disabled:f.value},null,8,R),[[m,K.value.location]])])])]),i("div",z,[i("button",{type:"button",class:"btn btn-secondary",disabled:f.value,onClick:X}," 重置 ",8,S),i("button",{type:"submit",class:"btn btn-primary",disabled:f.value||!Q.value},[f.value?(p(),u("div",A)):d("",!0),i("span",null,n(f.value?"保存中...":"保存更改"),1)],8,q)]),F.value?(p(),u("div",B,n(F.value),1)):d("",!0),G.value?(p(),u("div",D,"个人资料已成功更新！")):d("",!0)],32)])]))}}),[["__scopeId","data-v-96327e47"]]);export{F as default};
