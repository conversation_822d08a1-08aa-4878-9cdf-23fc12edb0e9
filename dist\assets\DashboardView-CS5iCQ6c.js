import{d as s,r as a,o as t,m as d,q as l,y as i,u as e,au as c,C as o,av as v,aA as n,aE as r,a7 as u,A as h,D as m,ak as p,a1 as b,N as f,a0 as w,F as T,z as _,H as g,G as M}from"./vendor-CSeT1gXp.js";import{_ as k}from"./index-BKFWLFbU.js";const C={class:"dashboard-view"},U={class:"dashboard-content"},N={class:"stats-grid"},y={class:"stat-card"},P={class:"stat-icon tools"},A={class:"stat-info"},G={class:"stat-number"},F={class:"stat-change positive"},R={class:"stat-card"},V={class:"stat-icon products"},j={class:"stat-info"},x={class:"stat-number"},z={class:"stat-change positive"},D={class:"stat-card"},I={class:"stat-icon users"},q={class:"stat-info"},E={class:"stat-number"},H={class:"stat-change positive"},S={class:"stat-card"},B={class:"stat-icon revenue"},J={class:"stat-info"},K={class:"stat-number"},L={class:"stat-change positive"},O={class:"dashboard-grid"},Q={class:"dashboard-card"},W={class:"card-header"},X={class:"card-content"},Y={class:"chart-placeholder"},Z={class:"dashboard-card"},$={class:"card-header"},ss={class:"card-content"},as={class:"popular-items"},ts={class:"item-icon"},ds={class:"item-info"},ls={class:"item-name"},is={class:"item-stats"},es={class:"item-trend"},cs={class:"dashboard-card"},os={class:"card-header"},vs={class:"card-content"},ns={class:"recent-orders"},rs={class:"order-info"},us={class:"order-id"},hs={class:"order-user"},ms={class:"order-amount"},ps=k(s({__name:"DashboardView",setup(s){const k=a("30d"),ps=a({totalTools:0,totalProducts:0,totalUsers:0,totalRevenue:0,newToolsThisMonth:0,newProductsThisMonth:0,newUsersThisMonth:0,revenueGrowth:0}),bs=a([{id:"1",name:"VS Code",icon:"💻",clickCount:1250},{id:"2",name:"Figma",icon:"🎨",clickCount:980},{id:"3",name:"Notion",icon:"📝",clickCount:756}]),fs=a([{id:"order-1",user:{fullName:"张三"},totalAmount:299,status:"paid"},{id:"order-2",user:{fullName:"李四"},totalAmount:199,status:"pending"}]);return t(()=>{(async()=>{try{ps.value={totalTools:156,totalProducts:42,totalUsers:1284,totalRevenue:125680,newToolsThisMonth:12,newProductsThisMonth:5,newUsersThisMonth:89,revenueGrowth:15.6}}catch(s){console.error("加载仪表盘数据失败:",s)}})()}),(s,a)=>{const t=w("router-link");return M(),d("div",C,[a[13]||(a[13]=l("div",{class:"dashboard-header"},[l("h1",null,"仪表盘"),l("p",null,"系统概览和关键指标")],-1)),l("div",U,[l("div",N,[l("div",y,[l("div",P,[i(e(c),{class:"icon"})]),l("div",A,[l("div",G,o(ps.value.totalTools),1),a[1]||(a[1]=l("div",{class:"stat-label"},"工具总数",-1)),l("div",F," +"+o(ps.value.newToolsThisMonth)+" 本月新增 ",1)])]),l("div",R,[l("div",V,[i(e(v),{class:"icon"})]),l("div",j,[l("div",x,o(ps.value.totalProducts),1),a[2]||(a[2]=l("div",{class:"stat-label"},"产品总数",-1)),l("div",z," +"+o(ps.value.newProductsThisMonth)+" 本月新增 ",1)])]),l("div",D,[l("div",I,[i(e(n),{class:"icon"})]),l("div",q,[l("div",E,o(ps.value.totalUsers),1),a[3]||(a[3]=l("div",{class:"stat-label"},"用户总数",-1)),l("div",H," +"+o(ps.value.newUsersThisMonth)+" 本月新增 ",1)])]),l("div",S,[l("div",B,[i(e(r),{class:"icon"})]),l("div",J,[l("div",K," ¥"+o((ws=ps.value.totalRevenue,new Intl.NumberFormat("zh-CN").format(ws))),1),a[4]||(a[4]=l("div",{class:"stat-label"},"总收入",-1)),l("div",L," +"+o(ps.value.revenueGrowth)+"% 本月增长 ",1)])])]),l("div",O,[l("div",Q,[l("div",W,[a[6]||(a[6]=l("h3",null,"访问趋势",-1)),h(l("select",{"onUpdate:modelValue":a[0]||(a[0]=s=>k.value=s),class:"period-select"},a[5]||(a[5]=[l("option",{value:"7d"},"最近7天",-1),l("option",{value:"30d"},"最近30天",-1),l("option",{value:"90d"},"最近90天",-1)]),512),[[m,k.value]])]),l("div",X,[l("div",Y,[i(e(p),{class:"chart-icon"}),a[7]||(a[7]=l("p",null,"访问量图表",-1)),l("small",null,o("7d"===k.value?"7天":"30d"===k.value?"30天":"90天")+"内的访问趋势",1)])])]),l("div",Z,[l("div",$,[a[9]||(a[9]=l("h3",null,"热门工具",-1)),i(t,{to:"/admin/tools",class:"view-all"},{default:b(()=>a[8]||(a[8]=[f("查看全部")])),_:1,__:[8]})]),l("div",ss,[l("div",as,[(M(!0),d(T,null,_(bs.value,s=>(M(),d("div",{key:s.id,class:"popular-item"},[l("div",ts,o(s.icon||"🔧"),1),l("div",ds,[l("div",ls,o(s.name),1),l("div",is,o(s.clickCount)+" 次访问",1)]),l("div",es,[i(e(p),{class:"trend-icon"})])]))),128))])])]),l("div",cs,[l("div",os,[a[11]||(a[11]=l("h3",null,"最新订单",-1)),i(t,{to:"/admin/orders",class:"view-all"},{default:b(()=>a[10]||(a[10]=[f("查看全部")])),_:1,__:[10]})]),l("div",vs,[l("div",ns,[(M(!0),d(T,null,_(fs.value,s=>{var a,t;return M(),d("div",{key:s.id,class:"order-item"},[l("div",rs,[l("div",us," #"+o(s.id.slice(-8).toUpperCase()),1),l("div",hs,o((null==(a=s.user)?void 0:a.fullName)||"匿名用户"),1)]),l("div",ms,"¥"+o(s.totalAmount),1),l("div",{class:g(["order-status",s.status])},o((t=s.status,{pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[t]||t)),3)])}),128))])])]),a[12]||(a[12]=u('<div class="dashboard-card" data-v-53d93607><div class="card-header" data-v-53d93607><h3 data-v-53d93607>系统状态</h3></div><div class="card-content" data-v-53d93607><div class="system-status" data-v-53d93607><div class="status-item" data-v-53d93607><div class="status-indicator online" data-v-53d93607></div><div class="status-info" data-v-53d93607><div class="status-label" data-v-53d93607>数据库</div><div class="status-value" data-v-53d93607>正常</div></div></div><div class="status-item" data-v-53d93607><div class="status-indicator online" data-v-53d93607></div><div class="status-info" data-v-53d93607><div class="status-label" data-v-53d93607>存储服务</div><div class="status-value" data-v-53d93607>正常</div></div></div><div class="status-item" data-v-53d93607><div class="status-indicator online" data-v-53d93607></div><div class="status-info" data-v-53d93607><div class="status-label" data-v-53d93607>支付服务</div><div class="status-value" data-v-53d93607>正常</div></div></div></div></div></div>',1))])])]);var ws}}}),[["__scopeId","data-v-53d93607"]]);export{ps as default};
