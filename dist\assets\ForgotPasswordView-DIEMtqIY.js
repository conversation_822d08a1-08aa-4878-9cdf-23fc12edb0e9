import{d as a,i as e,k as l,c as s,a as t,v as u,p as n,x as o,z as r,t as i,h as v,e as c,w as d,r as m,o as f}from"./vendor-gTrNmiX-.js";import{_ as p}from"./index-BBkjtX5T.js";const b={class:"forgot-password-view"},g={class:"form-group"},y=["disabled"],_=["disabled"],h={key:0,class:"loading-spinner"},k={key:0,class:"error-message"},w={key:1,class:"success-state"},x={class:"success-actions"},I=["disabled"],P={class:"forgot-footer"},j=p(a({__name:"ForgotPasswordView",setup(a){const p=e(!1),j=e(null),E=e(""),T=e(!1),V=e(0);let q=null;const z=async()=>{try{p.value=!0,j.value=null,await new Promise(a=>setTimeout(a,1e3)),T.value=!0,F()}catch(a){j.value=a instanceof Error?a.message:"发送失败，请重试"}finally{p.value=!1}},C=async()=>{if(!(V.value>0))try{p.value=!0,j.value=null,await new Promise(a=>setTimeout(a,500)),F()}catch(a){j.value=a instanceof Error?a.message:"重新发送失败，请重试"}finally{p.value=!1}},F=()=>{V.value=60,q=setInterval(()=>{V.value--,V.value<=0&&(clearInterval(q),q=null)},1e3)};return l(()=>{q&&clearInterval(q)}),(a,e)=>{const l=m("router-link");return f(),s("div",b,[e[10]||(e[10]=t("div",{class:"forgot-header"},[t("h1",null,"忘记密码"),t("p",null,"输入您的邮箱地址，我们将发送重置密码的链接")],-1)),T.value?(f(),s("div",w,[e[5]||(e[5]=t("div",{class:"success-icon"},"✅",-1)),e[6]||(e[6]=t("h3",null,"邮件已发送",-1)),t("p",null,[e[2]||(e[2]=v(" 我们已向 ")),t("strong",null,i(E.value),1),e[3]||(e[3]=v(" 发送了重置密码的链接 "))]),e[7]||(e[7]=t("p",{class:"help-text"}," 请检查您的邮箱（包括垃圾邮件文件夹），并点击链接重置密码 ",-1)),t("div",x,[t("button",{class:"resend-btn",disabled:V.value>0,onClick:C},i(V.value>0?`${V.value}秒后可重发`:"重新发送"),9,I),c(l,{to:"/auth/login",class:"back-btn"},{default:d(()=>e[4]||(e[4]=[v("返回登录")])),_:1,__:[4]})])])):(f(),s("form",{key:0,class:"forgot-form",onSubmit:u(z,["prevent"])},[t("div",g,[e[1]||(e[1]=t("label",{for:"email"},"邮箱地址",-1)),o(t("input",{id:"email","onUpdate:modelValue":e[0]||(e[0]=a=>E.value=a),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:p.value},null,8,y),[[r,E.value]])]),t("button",{type:"submit",class:"submit-btn",disabled:p.value||!E.value},[p.value?(f(),s("div",h)):n("",!0),t("span",null,i(p.value?"发送中...":"发送重置链接"),1)],8,_),j.value?(f(),s("div",k,i(j.value),1)):n("",!0)],32)),t("div",P,[t("p",null,[e[9]||(e[9]=v(" 记起密码了？ ")),c(l,{to:"/auth/login",class:"login-link"},{default:d(()=>e[8]||(e[8]=[v("立即登录")])),_:1,__:[8]})])])])}}}),[["__scopeId","data-v-5fa570f1"]]);export{j as default};
