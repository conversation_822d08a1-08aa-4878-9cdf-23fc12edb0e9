name: Deploy to Production

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    name: Build and Deploy

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: "yarn"

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run linting
        run: yarn lint

      - name: Run type checking
        run: yarn type-check

      - name: Build project
        run: yarn build
        env:
          NODE_ENV: production

      - name: Verify build output
        run: |
          if [ ! -d "dist" ]; then
            echo "❌ Build failed: dist directory not found"
            exit 1
          fi
          if [ ! -f "dist/index.html" ]; then
            echo "❌ Build failed: index.html not found"
            exit 1
          fi
          echo "✅ Build verification successful"
          echo "📁 Build output:"
          ls -la dist/

      # Netlify 自动部署
      - name: Deploy to Netlify
        if: github.ref == 'refs/heads/main'
        run: |
          echo "🚀 Deploying to Netlify..."
          echo "📦 Site URL: https://ramusi.cn"
          echo "⏱️  Netlify will automatically deploy this build"
          echo "🔗 Monitor deployment: https://app.netlify.com/sites/spiffy-torrone-5454e1/deploys"

      # 注意：如需启用其他部署方式，请参考 docs/DEPLOYMENT.md

      # 部署状态通知
      - name: Deployment Status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Deployment successful!"
            echo "🌐 Site is live at: https://ramusi.cn"
          else
            echo "❌ Deployment failed!"
            echo "🔍 Check the logs above for details"
          fi

  # 可选：运行部署后测试
  post-deploy-tests:
    runs-on: ubuntu-latest
    needs: build-and-deploy
    if: github.ref == 'refs/heads/main'
    name: Post-deployment Tests

    steps:
      - name: Wait for deployment
        run: sleep 30 # 等待部署完成

      - name: Test site availability
        run: |
          echo "🔍 Testing site availability..."

          # 测试主页
          if curl -f -s https://ramusi.cn > /dev/null; then
            echo "✅ Main site is accessible"
          else
            echo "❌ Main site is not accessible"
            exit 1
          fi

          # 测试关键页面
          pages=("/" "/tools" "/products")
          for page in "${pages[@]}"; do
            if curl -f -s "https://ramusi.cn${page}" > /dev/null; then
              echo "✅ Page ${page} is accessible"
            else
              echo "⚠️  Page ${page} might have issues"
            fi
          done

      - name: Performance check
        run: |
          echo "⚡ Running basic performance check..."
          response_time=$(curl -o /dev/null -s -w '%{time_total}' https://ramusi.cn)
          echo "📊 Response time: ${response_time}s"

          if (( $(echo "$response_time < 3.0" | bc -l) )); then
            echo "✅ Performance is good"
          else
            echo "⚠️  Site might be slow (>${response_time}s)"
          fi

# 环境变量说明
# 如果要启用自定义服务器部署，需要在 GitHub 仓库设置中添加以下 Secrets：
# - DEPLOY_HOST: 服务器 IP 地址或域名
# - DEPLOY_USERNAME: SSH 用户名
# - DEPLOY_SSH_KEY: SSH 私钥
# - DEPLOY_PORT: SSH 端口（可选，默认 22）
