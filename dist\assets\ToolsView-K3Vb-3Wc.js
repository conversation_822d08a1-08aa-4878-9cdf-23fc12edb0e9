import{d as a,r as s,c as e,w as l,o as t,m as c,q as o,y as i,A as n,t as r,u,a2 as v,B as d,X as p,D as y,H as g,N as k,a6 as m,an as h,aq as f,C as b,F as C,z as w,x as _,ap as q,am as x,O as F,G as z}from"./vendor-CSeT1gXp.js";import{b as S,d as T,u as A,_ as D}from"./index-BKFWLFbU.js";const I={class:"tools-view"},Q={class:"filters-bar"},V={class:"filters-content"},j={class:"filter-group search-group"},U={class:"search-container"},B={class:"filter-group"},E={class:"filter-group"},G={class:"filter-group"},H={class:"view-options"},N={class:"results-info"},O={class:"results-count"},J={class:"tools-content"},K={key:0,class:"loading-state"},L={key:1,class:"error-state"},M={key:2,class:"tools-grid"},P=["onClick"],R={class:"card-header"},W={class:"tool-icon"},X=["onClick"],Y={class:"card-content"},Z={class:"tool-name"},$={class:"tool-description"},aa={class:"tool-tags"},sa={key:0,class:"tag more"},ea={class:"card-footer"},la={class:"tool-stats"},ta={class:"stat"},ca={class:"stat"},oa={key:3,class:"tools-list"},ia=["onClick"],na={class:"item-left"},ra={class:"tool-icon"},ua={class:"tool-info"},va={class:"tool-name"},da={class:"tool-description"},pa={class:"tool-meta"},ya={class:"category"},ga={class:"clicks"},ka={class:"item-right"},ma={class:"tool-tags"},ha={class:"tool-actions"},fa=["onClick"],ba={key:4,class:"empty-state"},Ca={key:0},wa={key:1},_a={key:2},qa={class:"empty-actions"},xa=D(a({__name:"ToolsView",setup(a){const D=T(),xa=A(),Fa=S(),za=s(),Sa=s(""),Ta=s("all"),Aa=s("name"),Da=s(!1),Ia=s("grid"),Qa=e(()=>{let a=Fa.filteredTools;return Da.value&&(a=a.filter(a=>a.isFavorite)),a=[...a].sort((a,s)=>{switch(Aa.value){case"click_count":return s.clickCount-a.clickCount;case"created_at":return new Date(s.createdAt).getTime()-new Date(a.createdAt).getTime();default:return a.name.localeCompare(s.name)}}),a}),Va=()=>{Fa.setSearchQuery(Sa.value)},ja=()=>{Sa.value="",Fa.setSearchQuery("")},Ua=async a=>{await Fa.incrementClickCount(a.id),window.open(a.url,"_blank","noopener,noreferrer")},Ba=async()=>{Fa.clearError(),await Fa.initialize()};return l(()=>D.query,a=>{a.category&&"string"==typeof a.category&&(Ta.value=a.category),a.search&&"string"==typeof a.search&&(Sa.value=a.search,Fa.setSearchQuery(a.search))},{immediate:!0}),l(Ta,a=>{Fa.setSelectedCategory(a);const s={...D.query};"all"===a?delete s.category:s.category=a,xa.replace({query:s})}),l(Sa,a=>{const s={...D.query};a?s.search=a:delete s.search,xa.replace({query:s})}),t(async()=>{Fa.initialized||await Fa.initialize()}),(a,s)=>(z(),c("div",I,[o("div",Q,[o("div",V,[o("div",j,[o("div",U,[i(u(v),{class:"search-icon"}),n(o("input",{ref_key:"searchInput",ref:za,"onUpdate:modelValue":s[0]||(s[0]=a=>Sa.value=a),type:"text",placeholder:"搜索工具...",class:"search-input",onInput:Va},null,544),[[d,Sa.value]]),Sa.value?(z(),c("button",{key:0,class:"clear-search",onClick:ja},[i(u(p),{class:"icon"})])):r("",!0)])]),o("div",B,[s[7]||(s[7]=o("label",{class:"filter-label"},"排序：",-1)),n(o("select",{"onUpdate:modelValue":s[1]||(s[1]=a=>Aa.value=a),class:"filter-select"},s[6]||(s[6]=[o("option",{value:"name"},"名称",-1),o("option",{value:"click_count"},"热度",-1),o("option",{value:"created_at"},"最新",-1)]),512),[[y,Aa.value]])]),o("div",E,[o("button",{class:g(["filter-button",{active:Da.value}]),onClick:s[2]||(s[2]=a=>Da.value=!Da.value)},[i(u(m),{class:"icon"}),s[8]||(s[8]=k(" 只看收藏 "))],2)]),o("div",G,[o("div",H,[o("button",{class:g(["view-button",{active:"grid"===Ia.value}]),onClick:s[3]||(s[3]=a=>Ia.value="grid")},[i(u(h),{class:"icon"})],2),o("button",{class:g(["view-button",{active:"list"===Ia.value}]),onClick:s[4]||(s[4]=a=>Ia.value="list")},[i(u(f),{class:"icon"})],2)])]),o("div",N,[o("span",O," 找到 "+b(Qa.value.length)+" 个工具 ",1)])])]),o("div",J,[u(Fa).loading?(z(),c("div",K,s[9]||(s[9]=[o("div",{class:"loading-spinner"},null,-1),o("p",null,"正在加载工具...",-1)]))):u(Fa).error?(z(),c("div",L,[s[10]||(s[10]=o("div",{class:"error-icon"},"❌",-1)),s[11]||(s[11]=o("h3",null,"加载失败",-1)),o("p",null,b(u(Fa).error),1),o("button",{class:"retry-button",onClick:Ba},"重试")])):Qa.value.length>0&&"grid"===Ia.value?(z(),c("div",M,[(z(!0),c(C,null,w(Qa.value,a=>(z(),c("div",{key:a.id,class:"tool-card",onClick:s=>Ua(a)},[o("div",R,[o("div",W,b(a.icon||"🔧"),1),o("button",{class:g(["favorite-button",{active:a.isFavorite}]),onClick:_(s=>u(Fa).toggleFavorite(a.id),["stop"])},[i(u(m),{class:"icon"})],10,X)]),o("div",Y,[o("h3",Z,b(a.name),1),o("p",$,b(a.description),1),o("div",aa,[(z(!0),c(C,null,w(a.tags.slice(0,3),a=>(z(),c("span",{key:a.id,class:"tag"},b(a.name),1))),128)),a.tags.length>3?(z(),c("span",sa," +"+b(a.tags.length-3),1)):r("",!0)])]),o("div",ea,[o("div",la,[o("span",ta,[i(u(q),{class:"stat-icon"}),k(" "+b(a.clickCount),1)]),o("span",ca,[i(u(x),{class:"stat-icon"}),k(" "+b(a.category.name),1)])]),i(u(F),{class:"external-icon"})])],8,P))),128))])):Qa.value.length>0&&"list"===Ia.value?(z(),c("div",oa,[(z(!0),c(C,null,w(Qa.value,a=>(z(),c("div",{key:a.id,class:"tool-item",onClick:s=>Ua(a)},[o("div",na,[o("div",ra,b(a.icon||"🔧"),1),o("div",ua,[o("h3",va,b(a.name),1),o("p",da,b(a.description),1),o("div",pa,[o("span",ya,b(a.category.name),1),s[12]||(s[12]=o("span",{class:"separator"},"•",-1)),o("span",ga,b(a.clickCount)+" 次访问",1)])])]),o("div",ka,[o("div",ma,[(z(!0),c(C,null,w(a.tags.slice(0,2),a=>(z(),c("span",{key:a.id,class:"tag"},b(a.name),1))),128))]),o("div",ha,[o("button",{class:g(["favorite-button",{active:a.isFavorite}]),onClick:_(s=>u(Fa).toggleFavorite(a.id),["stop"])},[i(u(m),{class:"icon"})],10,fa),i(u(F),{class:"external-icon"})])])],8,ia))),128))])):(z(),c("div",ba,[s[13]||(s[13]=o("div",{class:"empty-icon"},"🔍",-1)),s[14]||(s[14]=o("h3",null,"未找到相关工具",-1)),Sa.value?(z(),c("p",Ca,' 没有找到包含 "'+b(Sa.value)+'" 的工具，尝试使用其他关键词搜索 ',1)):"all"!==Ta.value?(z(),c("p",wa," 该分类下暂无工具，请选择其他分类 ")):(z(),c("p",_a,"暂无工具数据，请稍后再试")),o("div",qa,[Sa.value?(z(),c("button",{key:0,class:"btn btn-primary",onClick:ja}," 清除搜索条件 ")):r("",!0),"all"!==Ta.value?(z(),c("button",{key:1,class:"btn btn-secondary",onClick:s[5]||(s[5]=a=>Ta.value="all")}," 查看全部分类 ")):r("",!0)])]))])]))}}),[["__scopeId","data-v-9999f25e"]]);export{xa as default};
