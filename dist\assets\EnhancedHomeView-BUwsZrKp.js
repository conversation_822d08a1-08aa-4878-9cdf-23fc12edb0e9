var e=Object.defineProperty,a=(a,t,s)=>((a,t,s)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[t]=s)(a,"symbol"!=typeof t?t+"":t,s);import{u as t,d as s,i as l,m as o,j as n,R as i,O as c,c as r,q as u,a as d,p as v,e as p,x as g,W as h,z as m,y,X as f,Y as k,F as w,s as b,H as _,J as C,t as S,C as x,b as T,h as $,D as q,Z as M,_ as H,$ as E,a0 as O,o as P,w as R,a1 as A,r as I,a2 as D,v as B,a3 as U}from"./vendor-DhoxJlSg.js";import{s as F,T as G,u as V,_ as j,a as z,b as Q}from"./index-BLSkTtYG.js";import{u as W}from"./categories-DbHgCJSf.js";function J(e){return"function"==typeof e?e():t(e)}"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const L=()=>{};function N(e,a=200,t={}){return function(e,a){return function(...t){return new Promise((s,l)=>{Promise.resolve(e(()=>a.apply(this,t),{fn:a,thisArg:this,args:t})).then(s).catch(l)})}}(function(e,a={}){let t,s,l=L;const o=e=>{clearTimeout(e),l(),l=L};return n=>{const i=J(e),c=J(a.maxWait);return t&&o(t),i<=0||void 0!==c&&c<=0?(s&&(o(s),s=null),Promise.resolve(n())):new Promise((e,r)=>{l=a.rejectOnCancel?r:e,c&&!s&&(s=setTimeout(()=>{t&&o(t),s=null,e(n())},c)),t=setTimeout(()=>{s&&o(s),s=null,e(n())},i)})}}(a,t),e)}const K=new class{constructor(){a(this,"searchHistory",[]),a(this,"popularQueries",new Map)}async search(e){const a=Date.now(),{query:t,type:s="all",limit:l=20,offset:o=0}=e;try{let l=[],o=0,n={categories:[],tags:[],priceRanges:[]};switch(this.addToHistory(t,s),s){case"tools":const a=await this.searchTools(e);l=a.items,o=a.total,n=a.facets;break;case"products":const t=await this.searchProducts(e);l=t.items,o=t.total,n=t.facets;break;case"categories":const s=await this.searchCategories(e);l=s.items,o=s.total;break;default:const i=await this.searchAll(e);l=i.items,o=i.total,n=i.facets}const i=await this.generateSuggestions(t,s);return{items:l,total:o,query:t,suggestions:i,facets:n,searchTime:Date.now()-a}}catch(n){throw console.error("搜索失败:",n),n}}async searchTools(e){const{query:a,category:t,tags:s,sortBy:l="relevance",sortOrder:o="desc",limit:n=20,offset:i=0}=e;let c=F.from(G.TOOLS).select("\n        *,\n        categories!inner(name, icon, color),\n        tool_tags!inner(tags!inner(name, color))\n      ",{count:"exact"});a&&(c=c.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%,\n        meta_title.ilike.%${a}%,\n        meta_description.ilike.%${a}%\n      `)),t&&(c=c.eq("category_id",t)),s&&s.length>0&&(c=c.in("tool_tags.tag_id",s)),c=c.eq("status","active"),"relevance"===l&&a?(c=c.order("is_featured",{ascending:!1}),c=c.order("click_count",{ascending:!1})):c=c.order(l,{ascending:"asc"===o}),c=c.range(i,i+n-1);const{data:r,error:u,count:d}=await c;if(u)throw u;return{items:r||[],total:d||0,facets:await this.generateToolsFacets(a,t,s)}}async searchProducts(e){const{query:a,category:t,priceRange:s,sortBy:l="relevance",sortOrder:o="desc",limit:n=20,offset:i=0}=e;let c=F.from(G.PRODUCTS).select("\n        *,\n        product_categories!inner(name, icon, color)\n      ",{count:"exact"});a&&(c=c.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%,\n        short_description.ilike.%${a}%,\n        meta_title.ilike.%${a}%,\n        meta_description.ilike.%${a}%\n      `)),t&&(c=c.eq("category_id",t)),s&&(c=c.gte("price",s[0]).lte("price",s[1])),c=c.eq("status","active"),c="relevance"===l&&a?c.order("is_featured",{ascending:!1}):c.order(l,{ascending:"asc"===o}),c=c.range(i,i+n-1);const{data:r,error:u,count:d}=await c;if(u)throw u;return{items:r||[],total:d||0,facets:await this.generateProductsFacets(a,t,s)}}async searchCategories(e){const{query:a,limit:t=20,offset:s=0}=e;let l=F.from(G.CATEGORIES).select("*",{count:"exact"});a&&(l=l.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%\n      `)),l=l.eq("is_active",!0).order("sort_order",{ascending:!0}).range(s,s+t-1);const{data:o,error:n,count:i}=await l;if(n)throw n;return{items:o||[],total:i||0}}async searchAll(e){const{limit:a=20}=e,[t,s,l]=await Promise.all([this.searchTools({...e,limit:Math.ceil(a/3)}),this.searchProducts({...e,limit:Math.ceil(a/3)}),this.searchCategories({...e,limit:Math.ceil(a/3)})]);return{items:[...t.items.map(e=>({...e,_type:"tool"})),...s.items.map(e=>({...e,_type:"product"})),...l.items.map(e=>({...e,_type:"category"}))],total:t.total+s.total+l.total,facets:{categories:[...t.facets.categories,...s.facets.categories],tags:t.facets.tags,priceRanges:s.facets.priceRanges}}}async generateToolsFacets(e,a,t){const s=F.from(G.CATEGORIES).select("\n        id, name,\n        tools!inner(id)\n      ",{count:"exact"}).eq("is_active",!0);e&&s.or(`\n        tools.name.ilike.%${e}%,\n        tools.description.ilike.%${e}%\n      `);const{data:l}=await s,o=(l||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.tools)?void 0:a.length)||0}}),n=F.from(G.TAGS).select("\n        id, name,\n        tool_tags!inner(tools!inner(id))\n      ",{count:"exact"});e&&n.or(`\n        tool_tags.tools.name.ilike.%${e}%,\n        tool_tags.tools.description.ilike.%${e}%\n      `);const{data:i}=await n;return{categories:o,tags:(i||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.tool_tags)?void 0:a.length)||0}}),priceRanges:[]}}async generateProductsFacets(e,a,t){const s=F.from(G.PRODUCT_CATEGORIES).select("\n        id, name,\n        products!inner(id)\n      ",{count:"exact"});e&&s.or(`\n        products.name.ilike.%${e}%,\n        products.description.ilike.%${e}%\n      `);const{data:l}=await s;return{categories:(l||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.products)?void 0:a.length)||0}}),tags:[],priceRanges:[{range:"0-50",count:0},{range:"50-100",count:0},{range:"100-500",count:0},{range:"500+",count:0}]}}async generateSuggestions(e,a){if(!e||e.length<2)return[];const t=[];try{const{data:a}=await F.from(G.TOOLS).select("name").ilike("name",`%${e}%`).eq("status","active").limit(5);null==a||a.forEach(e=>{t.includes(e.name)||t.push(e.name)});const{data:s}=await F.from(G.PRODUCTS).select("name").ilike("name",`%${e}%`).eq("status","active").limit(5);null==s||s.forEach(e=>{t.includes(e.name)||t.push(e.name)});const{data:l}=await F.from(G.CATEGORIES).select("name").ilike("name",`%${e}%`).eq("is_active",!0).limit(3);null==l||l.forEach(e=>{t.includes(e.name)||t.push(e.name)})}catch(s){console.error("生成搜索建议失败:",s)}return t.slice(0,8)}async getPopularSearches(e=10){return Array.from(this.popularQueries.entries()).sort((e,a)=>a[1]-e[1]).slice(0,e).map(([e])=>e)}getSearchHistory(e=10){return this.searchHistory.sort((e,a)=>a.timestamp.getTime()-e.timestamp.getTime()).slice(0,e)}clearSearchHistory(){this.searchHistory=[],localStorage.removeItem("search_history")}saveHistoryToStorage(){try{localStorage.setItem("search_history",JSON.stringify(this.searchHistory))}catch(e){console.error("保存搜索历史失败:",e)}}loadHistoryFromStorage(){try{const e=localStorage.getItem("search_history");if(e){const a=JSON.parse(e);this.searchHistory=a.map(e=>({...e,timestamp:new Date(e.timestamp)}))}}catch(e){console.error("加载搜索历史失败:",e),this.searchHistory=[]}}addToHistory(e,a){const t={id:Date.now().toString(),query:e,type:a,timestamp:new Date,results_count:0};this.searchHistory=this.searchHistory.filter(t=>t.query!==e||t.type!==a),this.searchHistory.unshift(t),this.searchHistory.length>50&&(this.searchHistory=this.searchHistory.slice(0,50));const s=this.popularQueries.get(e)||0;this.popularQueries.set(e,s+1)}async getSmartSuggestions(e){const a=[];if(!e||e.length<2){return(await this.getPopularSearches(5)).map(e=>({text:e,type:"query"}))}try{(await this.generateSuggestions(e,"all")).forEach(e=>{a.push({text:e,type:"query"})});const{data:t}=await F.from(G.CATEGORIES).select("name").ilike("name",`%${e}%`).eq("is_active",!0).limit(3);null==t||t.forEach(e=>{a.push({text:e.name,type:"category"})});const{data:s}=await F.from(G.TAGS).select("name").ilike("name",`%${e}%`).limit(3);null==s||s.forEach(e=>{a.push({text:e.name,type:"tag"})})}catch(t){console.error("获取智能建议失败:",t)}return a.slice(0,10)}},Y={class:"search-input-container"},Z={class:"search-input-wrapper"},X=["placeholder"],ee={class:"search-actions"},ae={key:0,class:"suggestions-dropdown"},te={key:0,class:"suggestions-section"},se=["onClick","onMouseenter"],le={class:"suggestion-text"},oe={class:"suggestion-type"},ne={key:1,class:"suggestions-section"},ie=["onClick","onMouseenter"],ce={class:"suggestion-text"},re={class:"suggestion-meta"},ue={key:2,class:"suggestions-section"},de={class:"popular-searches"},ve=["onClick"],pe={key:0,class:"advanced-search-panel"},ge={class:"advanced-search-content"},he={class:"advanced-row"},me={class:"advanced-group"},ye=["value"],fe={class:"advanced-group"},ke={class:"advanced-group"},we={key:0,class:"advanced-row"},be={class:"advanced-group"},_e={class:"price-range"},Ce={class:"advanced-row"},Se={class:"advanced-group full-width"},xe={class:"tags-input"},Te={class:"selected-tags"},$e=["onClick"],qe={key:0,class:"tags-dropdown"},Me=["onClick"],He={key:1,class:"search-status"},Ee={key:2,class:"search-stats"},Oe=j(s({__name:"EnhancedSearchBox",props:{placeholder:{default:"搜索工具、产品、分类..."},autoFocus:{type:Boolean,default:!1},showAdvanced:{type:Boolean,default:!1},defaultType:{default:"all"}},emits:["search","clear","focus","blur"],setup(e,{expose:a,emit:s}){const R=e,A=s,I=V(),D=W(),B=l(),U=l(""),F=l(R.defaultType),G=l(!1),j=l(!1),z=l(!1),Q=l(R.showAdvanced),J=l(!1),L=l(-1),Oe=l([]),Pe=l([]),Re=l([]),Ae=l({category:"",sortBy:"relevance",sort_order:"desc",priceMin:null,priceMax:null}),Ie=l([]),De=l(""),Be=l([]),Ue=l(null),Fe=o(()=>D.categories),Ge=o(()=>[...Oe.value,...Pe.value.map(e=>({text:e.query,type:"query"}))]),Ve=()=>{G.value=!0,j.value=!0,z.value=!0,A("focus"),We()},je=()=>{setTimeout(()=>{G.value=!1,z.value=!1,L.value=-1},200),A("blur")},ze=()=>{L.value=-1,Je()},Qe=e=>{var a;switch(e.key){case"ArrowDown":e.preventDefault(),L.value=Math.min(L.value+1,Ge.value.length-1);break;case"ArrowUp":e.preventDefault(),L.value=Math.max(L.value-1,-1);break;case"Enter":e.preventDefault(),L.value>=0?Le(Ge.value[L.value].text):Ne();break;case"Escape":null==(a=B.value)||a.blur()}},We=async()=>{try{U.value?Oe.value=await K.getSmartSuggestions(U.value):(Oe.value=[],Pe.value=K.getSearchHistory(5),Re.value=await K.getPopularSearches(8))}catch(e){console.error("加载搜索建议失败:",e)}},Je=N(We,300),Le=e=>{U.value=e,z.value=!1,Ne()},Ne=async()=>{if(U.value.trim())try{J.value=!0;const e={query:U.value.trim(),type:F.value,category:Ae.value.category||void 0,tags:Ie.value.map(e=>e.id),priceRange:Ae.value.priceMin&&Ae.value.priceMax?[Ae.value.priceMin,Ae.value.priceMax]:void 0,sortBy:Ae.value.sortBy,sort_order:Ae.value.sort_order,limit:20},a=await K.search(e);Ue.value=a,A("search",a),I.push({name:"SearchResults",query:{q:U.value,type:F.value,...Ae.value}})}catch(e){console.error("搜索失败:",e)}finally{J.value=!1}},Ke=()=>{var e;U.value="",Ue.value=null,A("clear"),null==(e=B.value)||e.focus()},Ye=()=>{K.clearSearchHistory(),Pe.value=[]},Ze=()=>{Q.value=!Q.value,j.value=Q.value},Xe=()=>{Ae.value={category:"",sortBy:"relevance",sort_order:"desc",priceMin:null,priceMax:null},Ie.value=[]},ea=()=>{U.value&&Ne()},aa=async()=>{De.value||(Be.value=[])},ta=e=>{switch(e){case"category":return O;case"tag":return E;case"tool":case"product":return H;default:return M}},sa=e=>{switch(e){case"category":return"分类";case"tag":return"标签";case"tool":return"工具";case"product":return"产品";default:return"搜索"}},la=e=>{const a=(new Date).getTime()-e.getTime(),t=Math.floor(a/6e4),s=Math.floor(a/36e5),l=Math.floor(a/864e5);return t<1?"刚刚":t<60?`${t}分钟前`:s<24?`${s}小时前`:`${l}天前`};return n(async()=>{var e;R.autoFocus&&(await i(),null==(e=B.value)||e.focus()),0===D.categories.length&&await D.fetchCategories()}),c(U,()=>{U.value||(Ue.value=null)}),a({focus:()=>{var e;return null==(e=B.value)?void 0:e.focus()},clear:Ke,search:Ne}),(e,a)=>(P(),r("div",{class:u(["enhanced-search-box",{"is-focused":G.value,"is-expanded":j.value}])},[d("div",Y,[d("div",Z,[p(t(h),{class:"search-icon"}),g(d("input",{ref_key:"searchInput",ref:B,"onUpdate:modelValue":a[0]||(a[0]=e=>U.value=e),type:"text",class:"search-input",placeholder:e.placeholder,onFocus:Ve,onBlur:je,onKeydown:Qe,onInput:ze},null,40,X),[[m,U.value]]),d("div",ee,[g(d("select",{"onUpdate:modelValue":a[1]||(a[1]=e=>F.value=e),class:"search-type-select"},a[8]||(a[8]=[d("option",{value:"all"},"全部",-1),d("option",{value:"tools"},"工具",-1),d("option",{value:"products"},"产品",-1),d("option",{value:"categories"},"分类",-1)]),512),[[y,F.value]]),U.value?(P(),r("button",{key:0,onClick:Ke,class:"clear-button",type:"button"},[p(t(f),{class:"icon"})])):v("",!0),d("button",{onClick:Ze,class:u(["advanced-button",{active:Q.value}]),type:"button"},[p(t(k),{class:"icon"})],2)])]),z.value&&(Oe.value.length>0||Pe.value.length>0)?(P(),r("div",ae,[Oe.value.length>0?(P(),r("div",te,[a[9]||(a[9]=d("div",{class:"suggestions-header"},"搜索建议",-1)),(P(!0),r(w,null,b(Oe.value,(e,a)=>(P(),r("div",{key:`suggestion-${a}`,class:u(["suggestion-item",{active:L.value===a}]),onClick:a=>Le(e.text),onMouseenter:e=>L.value=a},[(P(),_(C(ta(e.type)),{class:"suggestion-icon"})),d("span",le,S(e.text),1),d("span",oe,S(sa(e.type)),1)],42,se))),128))])):v("",!0),Pe.value.length>0&&!U.value?(P(),r("div",ne,[d("div",{class:"suggestions-header"},[a[10]||(a[10]=d("span",null,"最近搜索",-1)),d("button",{onClick:Ye,class:"clear-history-button"},"清除")]),(P(!0),r(w,null,b(Pe.value,(e,a)=>(P(),r("div",{key:`history-${e.id}`,class:u(["suggestion-item history-item",{active:L.value===Oe.value.length+a}]),onClick:a=>Le(e.query),onMouseenter:e=>L.value=Oe.value.length+a},[p(t(x),{class:"suggestion-icon"}),d("span",ce,S(e.query),1),d("span",re,S(la(e.timestamp)),1)],42,ie))),128))])):v("",!0),Re.value.length>0&&!U.value?(P(),r("div",ue,[a[11]||(a[11]=d("div",{class:"suggestions-header"},"热门搜索",-1)),d("div",de,[(P(!0),r(w,null,b(Re.value,e=>(P(),r("button",{key:e,onClick:a=>Le(e),class:"popular-search-tag"},S(e),9,ve))),128))])])):v("",!0)])):v("",!0)]),Q.value?(P(),r("div",pe,[d("div",ge,[d("div",he,[d("div",me,[a[13]||(a[13]=d("label",{class:"advanced-label"},"分类",-1)),g(d("select",{"onUpdate:modelValue":a[2]||(a[2]=e=>Ae.value.category=e),class:"advanced-select"},[a[12]||(a[12]=d("option",{value:""},"所有分类",-1)),(P(!0),r(w,null,b(Fe.value,e=>(P(),r("option",{key:e.id,value:e.id},S(e.name),9,ye))),128))],512),[[y,Ae.value.category]])]),d("div",fe,[a[15]||(a[15]=d("label",{class:"advanced-label"},"排序",-1)),g(d("select",{"onUpdate:modelValue":a[3]||(a[3]=e=>Ae.value.sortBy=e),class:"advanced-select"},a[14]||(a[14]=[T('<option value="relevance" data-v-b97c2614>相关性</option><option value="name" data-v-b97c2614>名称</option><option value="created_at" data-v-b97c2614>创建时间</option><option value="click_count" data-v-b97c2614>热度</option><option value="price" data-v-b97c2614>价格</option>',5)]),512),[[y,Ae.value.sortBy]])]),d("div",ke,[a[17]||(a[17]=d("label",{class:"advanced-label"},"顺序",-1)),g(d("select",{"onUpdate:modelValue":a[4]||(a[4]=e=>Ae.value.sort_order=e),class:"advanced-select"},a[16]||(a[16]=[d("option",{value:"desc"},"降序",-1),d("option",{value:"asc"},"升序",-1)]),512),[[y,Ae.value.sort_order]])])]),"products"===F.value?(P(),r("div",we,[d("div",be,[a[19]||(a[19]=d("label",{class:"advanced-label"},"价格范围",-1)),d("div",_e,[g(d("input",{"onUpdate:modelValue":a[5]||(a[5]=e=>Ae.value.priceMin=e),type:"number",placeholder:"最低价",class:"price-input",min:"0"},null,512),[[m,Ae.value.priceMin,void 0,{number:!0}]]),a[18]||(a[18]=d("span",{class:"price-separator"},"-",-1)),g(d("input",{"onUpdate:modelValue":a[6]||(a[6]=e=>Ae.value.priceMax=e),type:"number",placeholder:"最高价",class:"price-input",min:"0"},null,512),[[m,Ae.value.priceMax,void 0,{number:!0}]])])])])):v("",!0),d("div",Ce,[d("div",Se,[a[20]||(a[20]=d("label",{class:"advanced-label"},"标签",-1)),d("div",xe,[d("div",Te,[(P(!0),r(w,null,b(Ie.value,e=>(P(),r("span",{key:e.id,class:"selected-tag"},[$(S(e.name)+" ",1),d("button",{onClick:a=>(e=>{Ie.value=Ie.value.filter(a=>a.id!==e.id)})(e),class:"remove-tag"},[p(t(f),{class:"icon"})],8,$e)]))),128))]),g(d("input",{"onUpdate:modelValue":a[7]||(a[7]=e=>De.value=e),type:"text",placeholder:"输入标签名称...",class:"tag-input",onInput:aa},null,544),[[m,De.value]]),Be.value.length>0?(P(),r("div",qe,[(P(!0),r(w,null,b(Be.value,e=>(P(),r("div",{key:e.id,class:"tag-option",onClick:a=>(e=>{Ie.value.find(a=>a.id===e.id)||Ie.value.push(e),De.value="",Be.value=[]})(e)},[d("span",{class:"tag-color",style:q({backgroundColor:e.color})},null,4),$(" "+S(e.name),1)],8,Me))),128))])):v("",!0)])])]),d("div",{class:"advanced-actions"},[d("button",{onClick:Xe,class:"reset-button"},"重置"),d("button",{onClick:ea,class:"apply-button"},"应用筛选")])])])):v("",!0),J.value?(P(),r("div",He,a[21]||(a[21]=[d("div",{class:"search-loading"},[d("div",{class:"loading-spinner"}),d("span",null,"搜索中...")],-1)]))):v("",!0),Ue.value?(P(),r("div",Ee," 找到 "+S(Ue.value.total)+" 个结果，用时 "+S(Ue.value.searchTime)+"ms ",1)):v("",!0)],2))}}),[["__scopeId","data-v-b97c2614"]]),Pe={class:"enhanced-home-view"},Re={class:"hero-section"},Ae={class:"container"},Ie={class:"hero-content"},De={class:"search-section"},Be={class:"main-content"},Ue={class:"container"},Fe={class:"content-layout"},Ge={class:"sidebar"},Ve={class:"sidebar-section"},je={class:"nav-links"},ze={class:"sidebar-section"},Qe={class:"category-filters"},We=["onClick"],Je={class:"tools-grid"},Le=["onClick"],Ne=["onClick"],Ke={class:"tool-header"},Ye={class:"tool-icon"},Ze={class:"tool-title"},Xe={class:"tool-subtitle"},ea={class:"tool-body"},aa={class:"tool-description"},ta={class:"tool-tags"},sa={key:0,class:"tool-tag more"},la={key:0,class:"empty-state"},oa=j(s({__name:"EnhancedHomeView",setup(e){const a=z(),s=W(),i=Q(),c=V(),g=l(null),h=l(null),m=o(()=>s.categories),y=o(()=>{if(h.value)return h.value.items||[];let e=a.tools;return g.value&&(e=e.filter(e=>e.category_id===g.value)),e}),f=e=>{h.value=e},k=()=>{h.value=null},_=()=>{h.value=null,g.value=null};return n(async()=>{a.initialized||await a.initialize(),s.initialized||await s.initialize()}),(e,s)=>{const l=I("router-link");return P(),r("div",Pe,[d("header",Re,[d("div",Ae,[d("div",Ie,[s[0]||(s[0]=d("div",{class:"logo-section"},[d("div",{class:"logo-icon"},"🚀"),d("h1",{class:"main-title"},"高效工具导航站")],-1)),s[1]||(s[1]=d("p",{class:"tagline"},"精心挑选的优质工具，让您的工作效率倍增",-1)),d("div",De,[p(Oe,{placeholder:"搜索工具、分类或功能...","auto-focus":!1,onSearch:f,onClear:k})])])])]),d("main",Be,[d("div",Ue,[d("div",Fe,[d("aside",Ge,[d("div",Ve,[s[5]||(s[5]=d("h3",null,"导航",-1)),d("nav",je,[p(l,{to:"/tools",class:"nav-link active"},{default:R(()=>[p(t(A),{class:"icon"}),s[2]||(s[2]=$(" 全部工具 "))]),_:1,__:[2]}),p(l,{to:"/favorites",class:"nav-link"},{default:R(()=>[p(t(D),{class:"icon"}),s[3]||(s[3]=$(" 我的收藏 "))]),_:1,__:[3]}),p(l,{to:"/products",class:"nav-link"},{default:R(()=>[p(t(H),{class:"icon"}),s[4]||(s[4]=$(" 我的产品 "))]),_:1,__:[4]})])]),d("div",ze,[s[6]||(s[6]=d("h3",null,"分类",-1)),d("div",Qe,[(P(!0),r(w,null,b(m.value,e=>(P(),r("button",{key:e.id,class:u(["category-tag",{active:g.value===e.id}]),onClick:a=>(e.id,void(g.value=g.value===categoryId?null:categoryId))},S(e.name),11,We))),128))])])]),d("section",Je,[(P(!0),r(w,null,b(y.value,(e,s)=>(P(),r("div",{key:e.id,class:"tool-card",style:q({"--index":s}),onClick:t=>(async e=>{await a.incrementClickCount(e.id),window.open(e.url,"_blank","noopener,noreferrer")})(e)},[d("button",{class:u(["favorite-btn",{favorited:e.is_favorite}]),onClick:B(t=>(async e=>{i.isAuthenticated?await a.toggleFavorite(e.id):c.push("/auth/login")})(e),["stop"])},[p(t(U),{class:"icon"})],10,Ne),d("div",Ke,[d("div",Ye,S(e.icon||"🔧"),1),d("h3",Ze,S(e.name),1),d("p",Xe,S(e.short_description||e.description.slice(0,50)+"..."),1)]),d("div",ea,[d("p",aa,S(e.description),1),d("div",ta,[(P(!0),r(w,null,b(e.tags.slice(0,3),e=>(P(),r("span",{key:e.id,class:"tool-tag"},S(e.name),1))),128)),e.tags.length>3?(P(),r("span",sa," +"+S(e.tags.length-3),1)):v("",!0)])])],12,Le))),128)),0===y.value.length?(P(),r("div",la,[s[7]||(s[7]=d("div",{class:"empty-icon"},"🔍",-1)),s[8]||(s[8]=d("h3",null,"未找到相关工具",-1)),s[9]||(s[9]=d("p",null,"尝试使用其他关键词搜索，或浏览其他分类",-1)),d("button",{class:"empty-action",onClick:_}," 清除搜索条件 ")])):v("",!0)])])])]),s[10]||(s[10]=d("footer",{class:"footer"},[d("div",{class:"container"},[d("p",null,"© 2024 高效工具导航站 | 让您的工作更智能、更高效")])],-1))])}}}),[["__scopeId","data-v-5cb3799c"]]);export{oa as default};
