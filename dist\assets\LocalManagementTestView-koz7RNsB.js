import{d as e,L as t,_ as s}from"./index-Dhbj_B_-.js";import{d as a,i as r,m as n,j as o,c,a as l,t as i,F as u,s as d,q as m,p,H as g,u as f,B as y,ax as v,ay as w,az as h,o as b}from"./vendor-DhoxJlSg.js";const k={class:"local-management-test"},L={class:"test-results"},E={class:"test-summary"},S={class:"summary-item"},T={class:"value"},I={class:"summary-item"},C={class:"value success"},U={class:"summary-item"},O={class:"value error"},j={class:"test-list"},D={class:"test-info"},P={class:"test-name"},$={class:"test-description"},_={class:"test-status"},x={key:0,class:"test-error"},F=s(a({__name:"LocalManagementTestView",setup(s){const a=e(),F=r([]),R=r(!1),M=n(()=>F.value.length),q=n(()=>F.value.filter(e=>"passed"===e.status).length),z=n(()=>F.value.filter(e=>"failed"===e.status).length),B=[{name:"本地存储服务初始化",description:"测试本地存储服务是否正确初始化",test:async()=>{const e=t.getUserPreferences();if(!e||"object"!=typeof e)throw new Error("用户偏好设置初始化失败");return!0}},{name:"添加本地工具",description:"测试添加本地工具功能",test:async()=>{const e=a.addLocalTool({name:"测试工具",description:"这是一个测试工具",url:"https://test.com",icon:"🧪",categoryId:"test-category",tags:["test"],isFeatured:!1,clickCount:0});if(!e||!e.localId)throw new Error("添加本地工具失败");return!0}},{name:"更新本地工具",description:"测试更新本地工具功能",test:async()=>{const e=t.getLocalTools();if(0===e.length)throw new Error("没有可更新的工具");const s=e[0];if(!a.updateLocalTool(s.id||s.localId,{name:"更新后的工具名称"}))throw new Error("更新本地工具失败");return!0}},{name:"删除本地工具",description:"测试删除本地工具功能",test:async()=>{const e=t.getLocalTools();if(0===e.length)throw new Error("没有可删除的工具");const s=e[0];if(!a.deleteLocalTool(s.id||s.localId))throw new Error("删除本地工具失败");return!0}},{name:"用户偏好设置",description:"测试用户偏好设置的保存和读取",test:async()=>{const e=t.getUserPreferences();a.updateUserPreferences({theme:"dark",autoSync:!1});const s=t.getUserPreferences();if("dark"!==s.theme||!1!==s.autoSync)throw new Error("用户偏好设置更新失败");return a.updateUserPreferences(e),!0}},{name:"离线队列管理",description:"测试离线操作队列的管理",test:async()=>{const e=t.getOfflineQueue().length;a.addLocalTool({name:"队列测试工具",description:"测试离线队列",url:"https://queue-test.com",icon:"📋",categoryId:"test",tags:[],isFeatured:!1,clickCount:0});if(t.getOfflineQueue().length<=e)throw new Error("离线队列未正确更新");return!0}},{name:"存储使用情况",description:"测试存储使用情况的计算",test:async()=>{const e=t.getStorageInfo();if(!e||"number"!=typeof e.used||"number"!=typeof e.total)throw new Error("存储使用情况计算失败");if(e.percentage<0||e.percentage>100)throw new Error("存储使用百分比计算错误");return!0}},{name:"数据导出功能",description:"测试本地数据导出功能",test:async()=>{try{const e={tools:t.getLocalTools(),categories:t.getLocalCategories(),preferences:t.getUserPreferences(),exportTime:(new Date).toISOString()},s=JSON.stringify(e,null,2);if(!s||s.length<10)throw new Error("导出数据为空或格式错误");return!0}catch(e){throw new Error(`数据导出失败: ${e}`)}}}],J=async()=>{if(!R.value){R.value=!0;for(let t=0;t<B.length;t++){const s=B[t],a=F.value[t];a.status="running",a.error=void 0;const r=Date.now();try{await s.test(),a.status="passed",a.duration=Date.now()-r}catch(e){a.status="failed",a.error=e instanceof Error?e.message:String(e),a.duration=Date.now()-r}await new Promise(e=>setTimeout(e,100))}R.value=!1}},N=()=>{confirm("确定要清空所有测试数据吗？")&&(t.clearAllLocalData(),a.initialize(),alert("测试数据已清空"))},Q=()=>{for(let e=1;e<=3;e++)a.addLocalTool({name:`测试工具 ${e}`,description:`这是第 ${e} 个测试工具`,url:`https://test-tool-${e}.com`,icon:"🔧",categoryId:"test-category",tags:["test",`tool${e}`],isFeatured:1===e,clickCount:Math.floor(100*Math.random())});alert("测试数据已生成")},A=()=>{const e={summary:{total:M.value,passed:q.value,failed:z.value,timestamp:(new Date).toISOString()},tests:F.value},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),s=URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download=`test-results-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)};return o(()=>{F.value=B.map(e=>({name:e.name,description:e.description,status:"pending"}))}),(e,t)=>(b(),c("div",k,[l("div",{class:"test-header"},[t[0]||(t[0]=l("h2",null,"本地管理功能测试",-1)),l("button",{class:"btn primary",onClick:J},"运行所有测试")]),l("div",L,[l("div",E,[l("div",S,[t[1]||(t[1]=l("span",{class:"label"},"总测试数:",-1)),l("span",T,i(M.value),1)]),l("div",I,[t[2]||(t[2]=l("span",{class:"label"},"通过:",-1)),l("span",C,i(q.value),1)]),l("div",U,[t[3]||(t[3]=l("span",{class:"label"},"失败:",-1)),l("span",O,i(z.value),1)])]),l("div",j,[(b(!0),c(u,null,d(F.value,e=>(b(),c("div",{key:e.name,class:m(["test-item",{success:"passed"===e.status,error:"failed"===e.status,running:"running"===e.status}])},[l("div",D,[l("div",P,i(e.name),1),l("div",$,i(e.description),1)]),l("div",_,["passed"===e.status?(b(),g(f(y),{key:0,class:"icon success"})):"failed"===e.status?(b(),g(f(v),{key:1,class:"icon error"})):"running"===e.status?(b(),g(f(w),{key:2,class:"icon spinning"})):(b(),g(f(h),{key:3,class:"icon pending"}))]),e.error?(b(),c("div",x,i(e.error),1)):p("",!0)],2))),128))])]),l("div",{class:"test-actions"},[l("button",{class:"btn secondary",onClick:N},"清空测试数据"),l("button",{class:"btn secondary",onClick:Q},"生成测试数据"),l("button",{class:"btn secondary",onClick:A},"导出测试结果")])]))}}),[["__scopeId","data-v-f27d525f"]]);export{F as default};
