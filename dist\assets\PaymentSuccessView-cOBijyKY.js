import{d as s,_ as a}from"./index-BKFWLFbU.js";import{d as c,r as e,o as l,m as t,q as n,y as o,u as i,W as u,C as p,aw as d,ab as r,aB as v,a1 as m,N as f,a0 as _,ac as b,G as h}from"./vendor-CSeT1gXp.js";const y={class:"payment-success-view"},w={class:"success-container"},x={class:"success-content"},g={class:"success-icon"},k={class:"order-info"},q={class:"info-item"},z={class:"value"},C={class:"info-item"},j={class:"value amount"},D={class:"info-item"},N={class:"value"},S={class:"info-item"},B={class:"value"},E={class:"next-steps"},F={class:"steps-list"},G={class:"step-item"},I={class:"step-item"},L={class:"step-item"},O={class:"step-content"},P={class:"action-buttons"},R={class:"support-info"},V={class:"contact-methods"},W={href:"mailto:<EMAIL>",class:"contact-item"},A={href:"tel:************",class:"contact-item"},H=a(c({__name:"PaymentSuccessView",setup(a){const c=s(),H=e("ORD-20241224-001"),J=e(299),K=e(""),M=e("支付宝"),Q=()=>{console.log("开始下载产品...");const s=document.createElement("a");s.href="/sample-product.zip",s.download="product.zip",s.click()};return l(()=>{(()=>{const s=c.query.order,a=c.query.amount;s&&(H.value=s),a&&(J.value=parseFloat(a)),K.value=(new Date).toLocaleString("zh-CN")})()}),(s,a)=>{const c=_("router-link");return h(),t("div",y,[n("div",w,[n("div",x,[n("div",g,[o(i(u),{class:"icon"})]),a[16]||(a[16]=n("h1",{class:"success-title"},"支付成功！",-1)),a[17]||(a[17]=n("p",{class:"success-message"},"您的订单已成功支付，感谢您的购买",-1)),n("div",k,[n("div",q,[a[0]||(a[0]=n("span",{class:"label"},"订单号:",-1)),n("span",z,p(H.value),1)]),n("div",C,[a[1]||(a[1]=n("span",{class:"label"},"支付金额:",-1)),n("span",j,"¥"+p(J.value),1)]),n("div",D,[a[2]||(a[2]=n("span",{class:"label"},"支付时间:",-1)),n("span",N,p(K.value),1)]),n("div",S,[a[3]||(a[3]=n("span",{class:"label"},"支付方式:",-1)),n("span",B,p(M.value),1)])]),n("div",E,[a[10]||(a[10]=n("h3",null,"接下来您可以：",-1)),n("div",F,[n("div",G,[o(i(d),{class:"step-icon"}),n("div",{class:"step-content"},[a[4]||(a[4]=n("h4",null,"下载产品",-1)),a[5]||(a[5]=n("p",null,"立即下载您购买的数字产品",-1)),n("button",{class:"step-action",onClick:Q}," 立即下载 ")])]),n("div",I,[o(i(r),{class:"step-icon"}),a[6]||(a[6]=n("div",{class:"step-content"},[n("h4",null,"查看邮件"),n("p",null,"我们已向您的邮箱发送了订单确认邮件")],-1))]),n("div",L,[o(i(v),{class:"step-icon"}),n("div",O,[a[8]||(a[8]=n("h4",null,"查看订单",-1)),a[9]||(a[9]=n("p",null,"在个人中心查看完整的订单详情",-1)),o(c,{to:"/user/orders",class:"step-action"},{default:m(()=>a[7]||(a[7]=[f(" 查看订单 ")])),_:1,__:[7]})])])])]),n("div",P,[o(c,{to:"/",class:"btn btn-secondary"},{default:m(()=>a[11]||(a[11]=[f(" 返回首页 ")])),_:1,__:[11]}),o(c,{to:"/products",class:"btn btn-primary"},{default:m(()=>a[12]||(a[12]=[f(" 继续购物 ")])),_:1,__:[12]})]),n("div",R,[a[15]||(a[15]=n("p",null,"如有任何问题，请联系我们的客服团队",-1)),n("div",V,[n("a",W,[o(i(r),{class:"contact-icon"}),a[13]||(a[13]=f(" <EMAIL> "))]),n("a",A,[o(i(b),{class:"contact-icon"}),a[14]||(a[14]=f(" ************ "))])])])])])])}}}),[["__scopeId","data-v-740529e0"]]);export{H as default};
