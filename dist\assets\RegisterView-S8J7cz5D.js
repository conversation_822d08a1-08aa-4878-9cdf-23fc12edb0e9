import{d as a,i as e,m as s,c as l,a as r,p as t,x as o,z as i,ah as d,H as u,u as n,a1 as c,ai as v,D as p,q as m,t as f,h as w,A as g,v as h,e as b,w as y,r as k,o as x}from"./vendor-gTrNmiX-.js";import{a as P,_ as T}from"./index-BBkjtX5T.js";const _={class:"register-view"},V={class:"form-group"},q=["disabled"],N={class:"form-group"},U=["disabled"],z={class:"form-group"},A={class:"password-input"},j=["type","disabled"],Z={class:"password-strength"},C={class:"strength-bar"},D={class:"strength-text"},E={class:"form-group"},H=["disabled"],I={key:0,class:"error-hint"},R={class:"form-group"},S={class:"checkbox-label"},B=["disabled"],F={key:0,class:"loading-spinner"},G={key:0,class:"error-message"},J={class:"register-footer"},K=T(a({__name:"RegisterView",setup(a){const T=P(),K=e(!1),L=e(null),M=e(!1),O=e({fullName:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1}),Q=s(()=>{const a=O.value.password;if(!a)return{class:"",width:"0%",text:""};let e=0;return a.length>=8&&e++,/[a-z]/.test(a)&&e++,/[A-Z]/.test(a)&&e++,/[0-9]/.test(a)&&e++,/[^A-Za-z0-9]/.test(a)&&e++,e<2?{class:"weak",width:"20%",text:"弱"}:e<3?{class:"fair",width:"40%",text:"一般"}:e<4?{class:"good",width:"60%",text:"良好"}:e<5?{class:"strong",width:"80%",text:"强"}:{class:"very-strong",width:"100%",text:"很强"}}),W=s(()=>O.value.email&&O.value.password&&O.value.password===O.value.confirmPassword&&O.value.agreeToTerms&&O.value.password.length>=8),X=async()=>{try{K.value=!0,L.value=null,await new Promise(a=>setTimeout(a,1500)),console.log("注册成功:",O.value.email),T.push("/auth/login")}catch(a){L.value=a instanceof Error?a.message:"注册失败，请重试"}finally{K.value=!1}};return(a,e)=>{const s=k("router-link");return x(),l("div",_,[e[17]||(e[17]=r("div",{class:"register-header"},[r("h1",null,"注册"),r("p",null,"创建您的账户，开始使用工具导航站")],-1)),r("form",{class:"register-form",onSubmit:h(X,["prevent"])},[r("div",V,[e[6]||(e[6]=r("label",{for:"fullName"},"姓名",-1)),o(r("input",{id:"fullName","onUpdate:modelValue":e[0]||(e[0]=a=>O.value.fullName=a),type:"text",placeholder:"请输入您的姓名",disabled:K.value},null,8,q),[[i,O.value.fullName]])]),r("div",N,[e[7]||(e[7]=r("label",{for:"email"},"邮箱地址 *",-1)),o(r("input",{id:"email","onUpdate:modelValue":e[1]||(e[1]=a=>O.value.email=a),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:K.value},null,8,U),[[i,O.value.email]])]),r("div",z,[e[8]||(e[8]=r("label",{for:"password"},"密码 *",-1)),r("div",A,[o(r("input",{id:"password","onUpdate:modelValue":e[2]||(e[2]=a=>O.value.password=a),type:M.value?"text":"password",required:"",placeholder:"请输入密码（至少8位）",disabled:K.value},null,8,j),[[d,O.value.password]]),r("button",{type:"button",class:"password-toggle",onClick:e[3]||(e[3]=a=>M.value=!M.value)},[M.value?(x(),u(n(v),{key:1,class:"icon"})):(x(),u(n(c),{key:0,class:"icon"}))])]),r("div",Z,[r("div",C,[r("div",{class:m(["strength-fill",Q.value.class]),style:p({width:Q.value.width})},null,6)]),r("span",D,f(Q.value.text),1)])]),r("div",E,[e[9]||(e[9]=r("label",{for:"confirmPassword"},"确认密码 *",-1)),o(r("input",{id:"confirmPassword","onUpdate:modelValue":e[4]||(e[4]=a=>O.value.confirmPassword=a),type:"password",required:"",placeholder:"请再次输入密码",disabled:K.value},null,8,H),[[i,O.value.confirmPassword]]),O.value.confirmPassword&&O.value.password!==O.value.confirmPassword?(x(),l("div",I," 密码不匹配 ")):t("",!0)]),r("div",R,[r("label",S,[o(r("input",{"onUpdate:modelValue":e[5]||(e[5]=a=>O.value.agreeToTerms=a),type:"checkbox",required:""},null,512),[[g,O.value.agreeToTerms]]),e[10]||(e[10]=r("span",{class:"checkmark"},null,-1)),e[11]||(e[11]=w(" 我已阅读并同意 ")),e[12]||(e[12]=r("a",{href:"#",class:"terms-link"},"服务条款",-1)),e[13]||(e[13]=w(" 和 ")),e[14]||(e[14]=r("a",{href:"#",class:"terms-link"},"隐私政策",-1))])]),r("button",{type:"submit",class:"register-btn",disabled:K.value||!W.value},[K.value?(x(),l("div",F)):t("",!0),r("span",null,f(K.value?"注册中...":"注册"),1)],8,B),L.value?(x(),l("div",G,f(L.value),1)):t("",!0)],32),r("div",J,[r("p",null,[e[16]||(e[16]=w(" 已有账户？ ")),b(s,{to:"/auth/login",class:"login-link"},{default:y(()=>e[15]||(e[15]=[w("立即登录")])),_:1,__:[15]})])])])}}}),[["__scopeId","data-v-1a5fc67c"]]);export{K as default};
