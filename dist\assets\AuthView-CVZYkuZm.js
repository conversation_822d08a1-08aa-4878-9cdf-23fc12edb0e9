import{d as a,m as s,q as t,y as o,a1 as e,a0 as i,G as l}from"./vendor-CSeT1gXp.js";import{_ as d}from"./index-BKFWLFbU.js";const r={class:"auth-view"},c={class:"auth-container"},n={class:"auth-header"},u={class:"auth-content"},v=d(a({__name:"AuthView",setup:a=>(a,d)=>{const v=i("router-link"),_=i("router-view");return l(),s("div",r,[t("div",c,[t("div",n,[o(v,{to:"/",class:"logo"},{default:e(()=>d[0]||(d[0]=[t("div",{class:"logo-icon"},"🚀",-1),t("div",{class:"logo-text"},"工具导航站",-1)])),_:1,__:[0]})]),t("div",u,[o(_)]),d[1]||(d[1]=t("div",{class:"auth-footer"},[t("p",null,"© 2024 工具导航站. 保留所有权利.")],-1))])])}}),[["__scopeId","data-v-7a2469ee"]]);export{v as default};
