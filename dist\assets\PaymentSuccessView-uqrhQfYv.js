import{c as s,_ as a}from"./index-BWTdsPe5.js";import{d as c,i as e,j as l,c as t,a as n,e as o,u as i,B as u,t as p,ag as d,f as r,al as v,w as m,h as f,r as _,P as h,o as b}from"./vendor-DhoxJlSg.js";const y={class:"payment-success-view"},w={class:"success-container"},x={class:"success-content"},g={class:"success-icon"},j={class:"order-info"},k={class:"info-item"},z={class:"value"},q={class:"info-item"},C={class:"value amount"},D={class:"info-item"},P={class:"value"},S={class:"info-item"},B={class:"value"},E={class:"next-steps"},F={class:"steps-list"},I={class:"step-item"},L={class:"step-item"},N={class:"step-item"},O={class:"step-content"},R={class:"action-buttons"},V={class:"support-info"},A={class:"contact-methods"},G={href:"mailto:<EMAIL>",class:"contact-item"},H={href:"tel:************",class:"contact-item"},J=a(c({__name:"PaymentSuccessView",setup(a){const c=s(),J=e("ORD-20241224-001"),K=e(299),M=e(""),Q=e("支付宝"),T=()=>{console.log("开始下载产品...");const s=document.createElement("a");s.href="/sample-product.zip",s.download="product.zip",s.click()};return l(()=>{(()=>{const s=c.query.order,a=c.query.amount;s&&(J.value=s),a&&(K.value=parseFloat(a)),M.value=(new Date).toLocaleString("zh-CN")})()}),(s,a)=>{const c=_("router-link");return b(),t("div",y,[n("div",w,[n("div",x,[n("div",g,[o(i(u),{class:"icon"})]),a[16]||(a[16]=n("h1",{class:"success-title"},"支付成功！",-1)),a[17]||(a[17]=n("p",{class:"success-message"},"您的订单已成功支付，感谢您的购买",-1)),n("div",j,[n("div",k,[a[0]||(a[0]=n("span",{class:"label"},"订单号:",-1)),n("span",z,p(J.value),1)]),n("div",q,[a[1]||(a[1]=n("span",{class:"label"},"支付金额:",-1)),n("span",C,"¥"+p(K.value),1)]),n("div",D,[a[2]||(a[2]=n("span",{class:"label"},"支付时间:",-1)),n("span",P,p(M.value),1)]),n("div",S,[a[3]||(a[3]=n("span",{class:"label"},"支付方式:",-1)),n("span",B,p(Q.value),1)])]),n("div",E,[a[10]||(a[10]=n("h3",null,"接下来您可以：",-1)),n("div",F,[n("div",I,[o(i(d),{class:"step-icon"}),n("div",{class:"step-content"},[a[4]||(a[4]=n("h4",null,"下载产品",-1)),a[5]||(a[5]=n("p",null,"立即下载您购买的数字产品",-1)),n("button",{class:"step-action",onClick:T}," 立即下载 ")])]),n("div",L,[o(i(r),{class:"step-icon"}),a[6]||(a[6]=n("div",{class:"step-content"},[n("h4",null,"查看邮件"),n("p",null,"我们已向您的邮箱发送了订单确认邮件")],-1))]),n("div",N,[o(i(v),{class:"step-icon"}),n("div",O,[a[8]||(a[8]=n("h4",null,"查看订单",-1)),a[9]||(a[9]=n("p",null,"在个人中心查看完整的订单详情",-1)),o(c,{to:"/user/orders",class:"step-action"},{default:m(()=>a[7]||(a[7]=[f(" 查看订单 ")])),_:1,__:[7]})])])])]),n("div",R,[o(c,{to:"/",class:"btn btn-secondary"},{default:m(()=>a[11]||(a[11]=[f(" 返回首页 ")])),_:1,__:[11]}),o(c,{to:"/products",class:"btn btn-primary"},{default:m(()=>a[12]||(a[12]=[f(" 继续购物 ")])),_:1,__:[12]})]),n("div",V,[a[15]||(a[15]=n("p",null,"如有任何问题，请联系我们的客服团队",-1)),n("div",A,[n("a",G,[o(i(r),{class:"contact-icon"}),a[13]||(a[13]=f(" <EMAIL> "))]),n("a",H,[o(i(h),{class:"contact-icon"}),a[14]||(a[14]=f(" ************ "))])])])])])])}}}),[["__scopeId","data-v-740529e0"]]);export{J as default};
