import{d as s,u as a,_ as c}from"./index-BKFWLFbU.js";import{d as t,r as l,o as e,m as n,q as o,t as i,y as p,u,U as d,C as r,aJ as v,Q as m,a1 as _,N as f,a0 as h,aO as b,ab as y,ac as x,G as C}from"./vendor-CSeT1gXp.js";const k={class:"payment-cancel-view"},w={class:"cancel-container"},g={class:"cancel-content"},j={class:"cancel-icon"},q={key:0,class:"order-info"},N={class:"info-item"},z={class:"value"},D={class:"info-item"},G={class:"value"},I={class:"next-steps"},J={class:"steps-list"},L={class:"step-item"},O={class:"step-item"},P={class:"step-content"},Q={class:"step-item"},S={class:"action-buttons"},U={class:"support-info"},V={class:"contact-methods"},$={href:"mailto:<EMAIL>",class:"contact-item"},A={href:"tel:************",class:"contact-item"},B=c(t({__name:"PaymentCancelView",setup(c){const t=s(),B=a(),E=l(""),F=l(""),H=()=>{E.value?B.push(`/payment?order=${E.value}`):B.push("/payment")};return e(()=>{(()=>{const s=t.query.order;s&&(E.value=s),F.value=(new Date).toLocaleString("zh-CN")})()}),(s,a)=>{const c=h("router-link");return C(),n("div",k,[o("div",w,[o("div",g,[o("div",j,[p(u(d),{class:"icon"})]),a[14]||(a[14]=o("h1",{class:"cancel-title"},"支付已取消",-1)),a[15]||(a[15]=o("p",{class:"cancel-message"},"您的支付已被取消，订单未完成",-1)),E.value?(C(),n("div",q,[o("div",N,[a[0]||(a[0]=o("span",{class:"label"},"订单号:",-1)),o("span",z,r(E.value),1)]),o("div",D,[a[1]||(a[1]=o("span",{class:"label"},"取消时间:",-1)),o("span",G,r(F.value),1)])])):i("",!0),o("div",I,[a[8]||(a[8]=o("h3",null,"接下来您可以：",-1)),o("div",J,[o("div",L,[p(u(v),{class:"step-icon"}),o("div",{class:"step-content"},[a[2]||(a[2]=o("h4",null,"重新支付",-1)),a[3]||(a[3]=o("p",null,"返回支付页面完成订单支付",-1)),o("button",{class:"step-action",onClick:H}," 重新支付 ")])]),o("div",O,[p(u(m),{class:"step-icon"}),o("div",P,[a[5]||(a[5]=o("h4",null,"继续购物",-1)),a[6]||(a[6]=o("p",null,"浏览更多优质产品",-1)),p(c,{to:"/products",class:"step-action"},{default:_(()=>a[4]||(a[4]=[f(" 继续购物 ")])),_:1,__:[4]})])]),o("div",Q,[p(u(b),{class:"step-icon"}),a[7]||(a[7]=o("div",{class:"step-content"},[o("h4",null,"联系客服"),o("p",null,"如有疑问，请联系我们的客服团队")],-1))])])]),o("div",S,[p(c,{to:"/",class:"btn btn-secondary"},{default:_(()=>a[9]||(a[9]=[f(" 返回首页 ")])),_:1,__:[9]}),p(c,{to:"/products",class:"btn btn-primary"},{default:_(()=>a[10]||(a[10]=[f(" 继续购物 ")])),_:1,__:[10]})]),o("div",U,[a[13]||(a[13]=o("p",null,"如需帮助，请联系我们的客服团队",-1)),o("div",V,[o("a",$,[p(u(y),{class:"contact-icon"}),a[11]||(a[11]=f(" <EMAIL> "))]),o("a",A,[p(u(x),{class:"contact-icon"}),a[12]||(a[12]=f(" ************ "))])])])])])])}}}),[["__scopeId","data-v-2c046c66"]]);export{B as default};
