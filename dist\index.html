<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ramusi 工具导航站 - 让工作更高效</title>
    <meta
      name="description"
      content="Ramusi 工具导航站，收录了开发、设计、AI、效率等各类优质工具，让您的工作更高效。"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      #app {
        min-height: 100vh;
      }

      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        color: white;
        font-size: 18px;
      }
    </style>
    <script type="module" crossorigin src="./assets/index-BBkjtX5T.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/vendor-gTrNmiX-.js">
    <link rel="stylesheet" crossorigin href="./assets/index-D4usXwVc.css">
  </head>
  <body>
    <div id="app">
      <div class="loading">正在加载工具导航站...</div>
    </div>
    <script>
      // 错误处理
      window.addEventListener('error', function (e) {
        console.error('页面加载错误:', e.error)
        const app = document.getElementById('app')
        if (app) {
          app.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>'
        }
      })

      // 检查资源加载
      setTimeout(function () {
        const app = document.getElementById('app')
        if (app && app.innerHTML.includes('正在加载')) {
          console.warn('Vue 应用可能未正确加载')
        }
      }, 3000)
    </script>
  </body>
</html>
