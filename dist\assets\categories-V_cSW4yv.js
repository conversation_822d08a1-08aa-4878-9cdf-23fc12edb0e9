import{e,r as a,c as r}from"./vendor-CSeT1gXp.js";import{C as t}from"./index-BKFWLFbU.js";const l=e("categories",()=>{const e=a(!1),l=a(null),o=a(!1),n=a([]),i=a(null),u=r(()=>n.value.filter(e=>e.isActive)),c=r(()=>n.value.map(e=>({...e}))),s=r(()=>{const e=n.value.filter(e=>!e.parentId);return e.map(e=>({...e,children:n.value.filter(a=>a.parentId===e.id)}))}),v=async()=>{try{e.value=!0,l.value=null;const a=await t.getCategoriesWithStats();n.value=a}catch(a){l.value=a instanceof Error?a.message:"加载分类失败",console.error("Error loading categories:",a)}finally{e.value=!1}};return{loading:e,error:l,initialized:o,categories:n,currentCategory:i,activeCategories:u,categoriesWithStats:c,categoryTree:s,initialize:async()=>{if(!o.value)try{await v(),o.value=!0}catch(e){console.error("Error initializing categories store:",e)}},loadCategories:v,loadCategory:async a=>{try{e.value=!0,l.value=null;const r=await t.getCategory(a);return i.value=r,r}catch(r){throw l.value=r instanceof Error?r.message:"加载分类详情失败",console.error("Error loading category:",r),r}finally{e.value=!1}},createCategory:async a=>{try{e.value=!0,l.value=null;const r=await t.createCategory(a);return n.value.unshift(r),r}catch(r){throw l.value=r instanceof Error?r.message:"创建分类失败",console.error("Error creating category:",r),r}finally{e.value=!1}},updateCategory:async(a,r)=>{var o;try{e.value=!0,l.value=null;const u=await t.updateCategory(a,r),c=n.value.findIndex(e=>e.id===a);return-1!==c&&(n.value[c]=u),(null==(o=i.value)?void 0:o.id)===a&&(i.value=u),u}catch(u){throw l.value=u instanceof Error?u.message:"更新分类失败",console.error("Error updating category:",u),u}finally{e.value=!1}},deleteCategory:async a=>{var r;try{e.value=!0,l.value=null,await t.deleteCategory(a),n.value=n.value.filter(e=>e.id!==a),(null==(r=i.value)?void 0:r.id)===a&&(i.value=null)}catch(o){throw l.value=o instanceof Error?o.message:"删除分类失败",console.error("Error deleting category:",o),o}finally{e.value=!1}},getCategoryById:e=>n.value.find(a=>a.id===e),getCategoriesByParent:e=>n.value.filter(a=>a.parentId===e),clearError:()=>{l.value=null},clearCurrentCategory:()=>{i.value=null}}});export{l as u};
