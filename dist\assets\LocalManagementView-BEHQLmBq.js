import{d as a,r as l,c as s,o as e,Z as n,m as t,q as o,t as c,H as i,u as d,C as u,y as r,ae as v,v as p,N as m,aH as g,aI as b,aJ as f,au as y,am as h,aC as k,ar as C,F as w,z as x,aF as L,aG as S,A as V,D as M,E as U,aw as D,aK as I,K as B,X as F,B as _,x as z,aL as E,G as T}from"./vendor-CSeT1gXp.js";import{e as j,_ as G}from"./index-BKFWLFbU.js";const K={class:"local-management-view"},N={class:"page-header"},O={class:"header-content"},P={class:"status-indicators"},H={key:0,class:"status-item pending"},q={class:"header-actions"},A=["disabled"],J=["disabled"],Z={class:"stats-grid"},$={class:"stat-card"},Q={class:"stat-icon"},R={class:"stat-content"},W={class:"stat-value"},X={class:"stat-card"},Y={class:"stat-icon"},aa={class:"stat-content"},la={class:"stat-value"},sa={class:"stat-card"},ea={class:"stat-icon"},na={class:"stat-content"},ta={class:"stat-value"},oa={class:"stat-card"},ca={class:"stat-icon"},ia={class:"stat-content"},da={class:"stat-value"},ua={class:"content-grid"},ra={class:"content-section"},va={class:"section-header"},pa={class:"tools-list"},ma={class:"tool-info"},ga={class:"tool-icon"},ba={class:"tool-details"},fa={class:"tool-meta"},ya={class:"modified-time"},ha={class:"tool-actions"},ka=["onClick"],Ca=["onClick"],wa={key:0,class:"empty-state"},xa={class:"content-section"},La={class:"preferences-form"},Sa={class:"form-group"},Va={class:"form-group"},Ma={class:"form-group"},Ua={class:"checkbox-label"},Da={class:"form-group"},Ia={class:"checkbox-label"},Ba={class:"content-section"},Fa={class:"data-actions"},_a={class:"storage-usage"},za={class:"usage-header"},Ea={class:"usage-bar"},Ta={class:"usage-details"},ja={class:"modal-header"},Ga={class:"modal-body"},Ka={class:"form-group"},Na={class:"form-group"},Oa={class:"form-group"},Pa={class:"form-group"},Ha={class:"modal-footer"},qa=["disabled"],Aa={key:1,class:"error-toast"},Ja=G(a({__name:"LocalManagementView",setup(a){const G=j(),Ja=l(!1),Za=l(),$a=l({name:"",description:"",url:"",icon:"🔧"}),{isOfflineMode:Qa,isSyncing:Ra,syncError:Wa,lastSyncTime:Xa,userPreferences:Ya,localTools:al,localCategories:ll,pendingSyncCount:sl,storageInfo:el,isOnline:nl}=G,tl=s({get:()=>Ya.value,set:a=>G.updateUserPreferences(a)}),ol=s(()=>{if(!Xa.value)return"从未同步";return new Date(Xa.value).toLocaleString("zh-CN")}),cl=async()=>{try{await G.forceSyncData()}catch(a){console.error("同步失败:",a)}},il=()=>{G.toggleOfflineMode()},dl=()=>{G.updateUserPreferences(tl.value)},ul=()=>{try{G.addLocalTool({name:$a.value.name,description:$a.value.description,url:$a.value.url,icon:$a.value.icon,categoryId:"",tags:[],isFeatured:!1,clickCount:0}),rl()}catch(a){console.error("添加工具失败:",a)}},rl=()=>{Ja.value=!1,$a.value={name:"",description:"",url:"",icon:"🔧"}},vl=()=>{G.exportLocalData()},pl=()=>{var a;null==(a=Za.value)||a.click()},ml=async a=>{var l;const s=null==(l=a.target.files)?void 0:l[0];if(s)try{await G.importLocalData(s)}catch(e){console.error("导入失败:",e)}},gl=()=>{confirm("确定要清空所有本地数据吗？此操作不可恢复。")&&G.clearLocalData()},bl=a=>{if(0===a)return"0 B";const l=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,l)).toFixed(2))+" "+["B","KB","MB","GB"][l]};return e(()=>{G.initialize()}),n(()=>{window.removeEventListener("online",()=>{}),window.removeEventListener("offline",()=>{})}),(a,l)=>(T(),t("div",K,[o("div",N,[o("div",O,[l[12]||(l[12]=o("h2",{class:"page-title"},"本地管理",-1)),o("div",P,[o("div",{class:i(["status-item",{online:d(nl),offline:!d(nl)}])},[l[11]||(l[11]=o("div",{class:"status-dot"},null,-1)),o("span",null,u(d(nl)?"在线":"离线"),1)],2),d(sl)>0?(T(),t("div",H,[r(d(v),{class:"icon"}),o("span",null,u(d(sl))+" 项待同步",1)])):c("",!0)])]),o("div",q,[o("button",{class:"btn secondary",disabled:d(Ra),onClick:il},[d(Qa)?(T(),p(d(b),{key:1,class:"icon"})):(T(),p(d(g),{key:0,class:"icon"})),m(" "+u(d(Qa)?"启用在线模式":"启用离线模式"),1)],8,A),o("button",{class:"btn primary",disabled:!d(nl)||d(Ra)||0===d(sl),onClick:cl},[r(d(f),{class:i(["icon",{spinning:d(Ra)}])},null,8,["class"]),m(" "+u(d(Ra)?"同步中...":"立即同步"),1)],8,J)])]),o("div",Z,[o("div",$,[o("div",Q,[r(d(y),{class:"icon"})]),o("div",R,[o("div",W,u(d(al).length),1),l[13]||(l[13]=o("div",{class:"stat-label"},"本地工具",-1))])]),o("div",X,[o("div",Y,[r(d(h),{class:"icon"})]),o("div",aa,[o("div",la,u(d(ll).length),1),l[14]||(l[14]=o("div",{class:"stat-label"},"本地分类",-1))])]),o("div",sa,[o("div",ea,[r(d(k),{class:"icon"})]),o("div",na,[o("div",ta,u(bl(d(el).used)),1),l[15]||(l[15]=o("div",{class:"stat-label"},"存储使用",-1))])]),o("div",oa,[o("div",ca,[r(d(v),{class:"icon"})]),o("div",ia,[o("div",da,u(ol.value),1),l[16]||(l[16]=o("div",{class:"stat-label"},"最后同步",-1))])])]),o("div",ua,[o("div",ra,[o("div",va,[l[18]||(l[18]=o("h3",null,"本地工具",-1)),o("button",{class:"btn small primary",onClick:l[0]||(l[0]=a=>Ja.value=!0)},[r(d(C),{class:"icon"}),l[17]||(l[17]=m(" 添加工具 "))])]),o("div",pa,[(T(!0),t(w,null,x(d(al),a=>{return T(),t("div",{key:a.localId||a.id,class:i(["tool-item",{pending:"pending"===a.syncStatus}])},[o("div",ma,[o("div",ga,u(a.icon||"🔧"),1),o("div",ba,[o("h4",null,u(a.name),1),o("p",null,u(a.description),1),o("div",fa,[o("span",{class:i(["sync-status",a.syncStatus])},u((s=a.syncStatus,{pending:"待同步",synced:"已同步",conflict:"冲突"}[s||"synced"]||"未知")),3),o("span",ya,u((l=a.lastModified,l?new Date(l).toLocaleString("zh-CN"):"")),1)])])]),o("div",ha,[o("button",{class:"btn small secondary",onClick:l=>(a=>{console.log("编辑工具:",a)})(a)},[r(d(L),{class:"icon"})],8,ka),o("button",{class:"btn small danger",onClick:l=>(a=>{confirm("确定要删除这个工具吗？")&&G.deleteLocalTool(a.id||a.localId)})(a)},[r(d(S),{class:"icon"})],8,Ca)])],2);var l,s}),128)),0===d(al).length?(T(),t("div",wa,[r(d(y),{class:"empty-icon"}),l[19]||(l[19]=o("p",null,"暂无本地工具",-1))])):c("",!0)])]),o("div",xa,[l[26]||(l[26]=o("div",{class:"section-header"},[o("h3",null,"偏好设置")],-1)),o("div",La,[o("div",Sa,[l[21]||(l[21]=o("label",null,"主题",-1)),V(o("select",{"onUpdate:modelValue":l[1]||(l[1]=a=>tl.value.theme=a),onChange:dl},l[20]||(l[20]=[o("option",{value:"auto"},"自动",-1),o("option",{value:"light"},"浅色",-1),o("option",{value:"dark"},"深色",-1)]),544),[[M,tl.value.theme]])]),o("div",Va,[l[23]||(l[23]=o("label",null,"默认视图",-1)),V(o("select",{"onUpdate:modelValue":l[2]||(l[2]=a=>tl.value.defaultView=a),onChange:dl},l[22]||(l[22]=[o("option",{value:"grid"},"网格",-1),o("option",{value:"list"},"列表",-1)]),544),[[M,tl.value.defaultView]])]),o("div",Ma,[o("label",Ua,[V(o("input",{"onUpdate:modelValue":l[3]||(l[3]=a=>tl.value.autoSync=a),type:"checkbox",onChange:dl},null,544),[[U,tl.value.autoSync]]),l[24]||(l[24]=o("span",null,"自动同步",-1))])]),o("div",Da,[o("label",Ia,[V(o("input",{"onUpdate:modelValue":l[4]||(l[4]=a=>tl.value.sidebarCollapsed=a),type:"checkbox",onChange:dl},null,544),[[U,tl.value.sidebarCollapsed]]),l[25]||(l[25]=o("span",null,"折叠侧边栏",-1))])])])]),o("div",Ba,[l[31]||(l[31]=o("div",{class:"section-header"},[o("h3",null,"数据管理")],-1)),o("div",Fa,[o("button",{class:"btn secondary full-width",onClick:vl},[r(d(D),{class:"icon"}),l[27]||(l[27]=m(" 导出本地数据 "))]),o("button",{class:"btn secondary full-width",onClick:pl},[r(d(I),{class:"icon"}),l[28]||(l[28]=m(" 导入本地数据 "))]),o("input",{ref_key:"fileInput",ref:Za,type:"file",accept:".json",style:{display:"none"},onChange:ml},null,544),o("button",{class:"btn danger full-width",onClick:gl},[r(d(S),{class:"icon"}),l[29]||(l[29]=m(" 清空本地数据 "))])]),o("div",_a,[o("div",za,[l[30]||(l[30]=o("span",null,"存储使用情况",-1)),o("span",null,u(d(el).percentage.toFixed(1))+"%",1)]),o("div",Ea,[o("div",{class:"usage-fill",style:B({width:`${Math.min(d(el).percentage,100)}%`})},null,4)]),o("div",Ta,[o("span",null,u(bl(d(el).used))+" / "+u(bl(d(el).total)),1)])])])]),Ja.value?(T(),t("div",{key:0,class:"modal-overlay",onClick:rl},[o("div",{class:"modal-content",onClick:l[9]||(l[9]=z(()=>{},["stop"]))},[o("div",ja,[l[32]||(l[32]=o("h3",null,"添加本地工具",-1)),o("button",{class:"close-btn",onClick:rl},[r(d(F),{class:"icon"})])]),o("div",Ga,[o("div",Ka,[l[33]||(l[33]=o("label",null,"工具名称",-1)),V(o("input",{"onUpdate:modelValue":l[5]||(l[5]=a=>$a.value.name=a),type:"text",placeholder:"输入工具名称"},null,512),[[_,$a.value.name]])]),o("div",Na,[l[34]||(l[34]=o("label",null,"工具描述",-1)),V(o("textarea",{"onUpdate:modelValue":l[6]||(l[6]=a=>$a.value.description=a),placeholder:"输入工具描述"},null,512),[[_,$a.value.description]])]),o("div",Oa,[l[35]||(l[35]=o("label",null,"工具链接",-1)),V(o("input",{"onUpdate:modelValue":l[7]||(l[7]=a=>$a.value.url=a),type:"url",placeholder:"https://example.com"},null,512),[[_,$a.value.url]])]),o("div",Pa,[l[36]||(l[36]=o("label",null,"工具图标",-1)),V(o("input",{"onUpdate:modelValue":l[8]||(l[8]=a=>$a.value.icon=a),type:"text",placeholder:"🔧"},null,512),[[_,$a.value.icon]])])]),o("div",Ha,[o("button",{class:"btn secondary",onClick:rl},"取消"),o("button",{class:"btn primary",disabled:!$a.value.name,onClick:ul}," 添加 ",8,qa)])])])):c("",!0),d(Wa)?(T(),t("div",Aa,[r(d(E),{class:"icon"}),o("span",null,u(d(Wa)),1),o("button",{onClick:l[10]||(l[10]=a=>Wa.value=null)},[r(d(F),{class:"icon"})])])):c("",!0)]))}}),[["__scopeId","data-v-68fb3c9a"]]);export{Ja as default};
