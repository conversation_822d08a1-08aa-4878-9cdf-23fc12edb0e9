import{d as a,r as e,c as s,m as l,q as r,t,A as o,B as u,ax as d,v as i,u as n,ap as c,ay as v,K as p,H as m,C as f,N as w,E as g,x as h,y as b,a1 as y,a0 as x,G as k}from"./vendor-CSeT1gXp.js";import{u as P,_ as T}from"./index-BKFWLFbU.js";const _={class:"register-view"},N={class:"form-group"},V=["disabled"],q={class:"form-group"},U=["disabled"],A={class:"form-group"},j={class:"password-input"},z=["type","disabled"],C={class:"password-strength"},E={class:"strength-bar"},Z={class:"strength-text"},B={class:"form-group"},G=["disabled"],H={key:0,class:"error-hint"},I={class:"form-group"},K={class:"checkbox-label"},R=["disabled"],S={key:0,class:"loading-spinner"},D={key:0,class:"error-message"},F={class:"register-footer"},J=T(a({__name:"RegisterView",setup(a){const T=P(),J=e(!1),L=e(null),M=e(!1),O=e({fullName:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1}),Q=s(()=>{const a=O.value.password;if(!a)return{class:"",width:"0%",text:""};let e=0;return a.length>=8&&e++,/[a-z]/.test(a)&&e++,/[A-Z]/.test(a)&&e++,/[0-9]/.test(a)&&e++,/[^A-Za-z0-9]/.test(a)&&e++,e<2?{class:"weak",width:"20%",text:"弱"}:e<3?{class:"fair",width:"40%",text:"一般"}:e<4?{class:"good",width:"60%",text:"良好"}:e<5?{class:"strong",width:"80%",text:"强"}:{class:"very-strong",width:"100%",text:"很强"}}),W=s(()=>O.value.email&&O.value.password&&O.value.password===O.value.confirmPassword&&O.value.agreeToTerms&&O.value.password.length>=8),X=async()=>{try{J.value=!0,L.value=null,await new Promise(a=>setTimeout(a,1500)),console.log("注册成功:",O.value.email),T.push("/auth/login")}catch(a){L.value=a instanceof Error?a.message:"注册失败，请重试"}finally{J.value=!1}};return(a,e)=>{const s=x("router-link");return k(),l("div",_,[e[17]||(e[17]=r("div",{class:"register-header"},[r("h1",null,"注册"),r("p",null,"创建您的账户，开始使用工具导航站")],-1)),r("form",{class:"register-form",onSubmit:h(X,["prevent"])},[r("div",N,[e[6]||(e[6]=r("label",{for:"fullName"},"姓名",-1)),o(r("input",{id:"fullName","onUpdate:modelValue":e[0]||(e[0]=a=>O.value.fullName=a),type:"text",placeholder:"请输入您的姓名",disabled:J.value},null,8,V),[[u,O.value.fullName]])]),r("div",q,[e[7]||(e[7]=r("label",{for:"email"},"邮箱地址 *",-1)),o(r("input",{id:"email","onUpdate:modelValue":e[1]||(e[1]=a=>O.value.email=a),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:J.value},null,8,U),[[u,O.value.email]])]),r("div",A,[e[8]||(e[8]=r("label",{for:"password"},"密码 *",-1)),r("div",j,[o(r("input",{id:"password","onUpdate:modelValue":e[2]||(e[2]=a=>O.value.password=a),type:M.value?"text":"password",required:"",placeholder:"请输入密码（至少8位）",disabled:J.value},null,8,z),[[d,O.value.password]]),r("button",{type:"button",class:"password-toggle",onClick:e[3]||(e[3]=a=>M.value=!M.value)},[M.value?(k(),i(n(v),{key:1,class:"icon"})):(k(),i(n(c),{key:0,class:"icon"}))])]),r("div",C,[r("div",E,[r("div",{class:m(["strength-fill",Q.value.class]),style:p({width:Q.value.width})},null,6)]),r("span",Z,f(Q.value.text),1)])]),r("div",B,[e[9]||(e[9]=r("label",{for:"confirmPassword"},"确认密码 *",-1)),o(r("input",{id:"confirmPassword","onUpdate:modelValue":e[4]||(e[4]=a=>O.value.confirmPassword=a),type:"password",required:"",placeholder:"请再次输入密码",disabled:J.value},null,8,G),[[u,O.value.confirmPassword]]),O.value.confirmPassword&&O.value.password!==O.value.confirmPassword?(k(),l("div",H," 密码不匹配 ")):t("",!0)]),r("div",I,[r("label",K,[o(r("input",{"onUpdate:modelValue":e[5]||(e[5]=a=>O.value.agreeToTerms=a),type:"checkbox",required:""},null,512),[[g,O.value.agreeToTerms]]),e[10]||(e[10]=r("span",{class:"checkmark"},null,-1)),e[11]||(e[11]=w(" 我已阅读并同意 ")),e[12]||(e[12]=r("a",{href:"#",class:"terms-link"},"服务条款",-1)),e[13]||(e[13]=w(" 和 ")),e[14]||(e[14]=r("a",{href:"#",class:"terms-link"},"隐私政策",-1))])]),r("button",{type:"submit",class:"register-btn",disabled:J.value||!W.value},[J.value?(k(),l("div",S)):t("",!0),r("span",null,f(J.value?"注册中...":"注册"),1)],8,R),L.value?(k(),l("div",D,f(L.value),1)):t("",!0)],32),r("div",F,[r("p",null,[e[16]||(e[16]=w(" 已有账户？ ")),b(s,{to:"/auth/login",class:"login-link"},{default:y(()=>e[15]||(e[15]=[w("立即登录")])),_:1,__:[15]})])])])}}}),[["__scopeId","data-v-cace2648"]]);export{J as default};
