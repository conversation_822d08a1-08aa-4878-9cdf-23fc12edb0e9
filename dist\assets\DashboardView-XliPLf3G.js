import{d as a,i as s,j as t,c as d,a as l,e as i,u as e,ae as c,t as o,af as v,ak as n,ap as r,b as u,x as h,y as b,Z as m,w as p,h as f,r as w,F as _,s as T,q as g,o as M}from"./vendor-DhoxJlSg.js";import{_ as k}from"./index-BLSkTtYG.js";const U={class:"dashboard-view"},y={class:"dashboard-content"},P={class:"stats-grid"},j={class:"stat-card"},x={class:"stat-icon tools"},A={class:"stat-info"},C={class:"stat-number"},F={class:"stat-change positive"},G={class:"stat-card"},N={class:"stat-icon products"},R={class:"stat-info"},V={class:"stat-number"},I={class:"stat-change positive"},q={class:"stat-card"},z={class:"stat-icon users"},D={class:"stat-info"},S={class:"stat-number"},Z={class:"stat-change positive"},B={class:"stat-card"},E={class:"stat-icon revenue"},H={class:"stat-info"},J={class:"stat-number"},K={class:"stat-change positive"},L={class:"dashboard-grid"},O={class:"dashboard-card"},Q={class:"card-header"},W={class:"card-content"},X={class:"chart-placeholder"},Y={class:"dashboard-card"},$={class:"card-header"},aa={class:"card-content"},sa={class:"popular-items"},ta={class:"item-icon"},da={class:"item-info"},la={class:"item-name"},ia={class:"item-stats"},ea={class:"item-trend"},ca={class:"dashboard-card"},oa={class:"card-header"},va={class:"card-content"},na={class:"recent-orders"},ra={class:"order-info"},ua={class:"order-id"},ha={class:"order-user"},ba={class:"order-amount"},ma=k(a({__name:"DashboardView",setup(a){const k=s("30d"),ma=s({totalTools:0,totalProducts:0,totalUsers:0,totalRevenue:0,newToolsThisMonth:0,newProductsThisMonth:0,newUsersThisMonth:0,revenueGrowth:0}),pa=s([{id:"1",name:"VS Code",icon:"💻",click_count:1250},{id:"2",name:"Figma",icon:"🎨",click_count:980},{id:"3",name:"Notion",icon:"📝",click_count:756}]),fa=s([{id:"order-1",user:{full_name:"张三"},totalAmount:299,status:"paid"},{id:"order-2",user:{full_name:"李四"},totalAmount:199,status:"pending"}]);return t(()=>{(async()=>{try{ma.value={totalTools:156,totalProducts:42,totalUsers:1284,totalRevenue:125680,newToolsThisMonth:12,newProductsThisMonth:5,newUsersThisMonth:89,revenueGrowth:15.6}}catch(a){console.error("加载仪表盘数据失败:",a)}})()}),(a,s)=>{const t=w("router-link");return M(),d("div",U,[s[13]||(s[13]=l("div",{class:"dashboard-header"},[l("h1",null,"仪表盘"),l("p",null,"系统概览和关键指标")],-1)),l("div",y,[l("div",P,[l("div",j,[l("div",x,[i(e(c),{class:"icon"})]),l("div",A,[l("div",C,o(ma.value.totalTools),1),s[1]||(s[1]=l("div",{class:"stat-label"},"工具总数",-1)),l("div",F," +"+o(ma.value.newToolsThisMonth)+" 本月新增 ",1)])]),l("div",G,[l("div",N,[i(e(v),{class:"icon"})]),l("div",R,[l("div",V,o(ma.value.totalProducts),1),s[2]||(s[2]=l("div",{class:"stat-label"},"产品总数",-1)),l("div",I," +"+o(ma.value.newProductsThisMonth)+" 本月新增 ",1)])]),l("div",q,[l("div",z,[i(e(n),{class:"icon"})]),l("div",D,[l("div",S,o(ma.value.totalUsers),1),s[3]||(s[3]=l("div",{class:"stat-label"},"用户总数",-1)),l("div",Z," +"+o(ma.value.newUsersThisMonth)+" 本月新增 ",1)])]),l("div",B,[l("div",E,[i(e(r),{class:"icon"})]),l("div",H,[l("div",J," ¥"+o((wa=ma.value.totalRevenue,new Intl.NumberFormat("zh-CN").format(wa))),1),s[4]||(s[4]=l("div",{class:"stat-label"},"总收入",-1)),l("div",K," +"+o(ma.value.revenueGrowth)+"% 本月增长 ",1)])])]),l("div",L,[l("div",O,[l("div",Q,[s[6]||(s[6]=l("h3",null,"访问趋势",-1)),h(l("select",{"onUpdate:modelValue":s[0]||(s[0]=a=>k.value=a),class:"period-select"},s[5]||(s[5]=[l("option",{value:"7d"},"最近7天",-1),l("option",{value:"30d"},"最近30天",-1),l("option",{value:"90d"},"最近90天",-1)]),512),[[b,k.value]])]),l("div",W,[l("div",X,[i(e(m),{class:"chart-icon"}),s[7]||(s[7]=l("p",null,"访问量图表",-1)),l("small",null,o("7d"===k.value?"7天":"30d"===k.value?"30天":"90天")+"内的访问趋势",1)])])]),l("div",Y,[l("div",$,[s[9]||(s[9]=l("h3",null,"热门工具",-1)),i(t,{to:"/admin/tools",class:"view-all"},{default:p(()=>s[8]||(s[8]=[f("查看全部")])),_:1,__:[8]})]),l("div",aa,[l("div",sa,[(M(!0),d(_,null,T(pa.value,a=>(M(),d("div",{key:a.id,class:"popular-item"},[l("div",ta,o(a.icon||"🔧"),1),l("div",da,[l("div",la,o(a.name),1),l("div",ia,o(a.click_count)+" 次访问",1)]),l("div",ea,[i(e(m),{class:"trend-icon"})])]))),128))])])]),l("div",ca,[l("div",oa,[s[11]||(s[11]=l("h3",null,"最新订单",-1)),i(t,{to:"/admin/orders",class:"view-all"},{default:p(()=>s[10]||(s[10]=[f("查看全部")])),_:1,__:[10]})]),l("div",va,[l("div",na,[(M(!0),d(_,null,T(fa.value,a=>{var s,t;return M(),d("div",{key:a.id,class:"order-item"},[l("div",ra,[l("div",ua," #"+o(a.id.slice(-8).toUpperCase()),1),l("div",ha,o((null==(s=a.user)?void 0:s.full_name)||"匿名用户"),1)]),l("div",ba,"¥"+o(a.totalAmount),1),l("div",{class:g(["order-status",a.status])},o((t=a.status,{pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[t]||t)),3)])}),128))])])]),s[12]||(s[12]=u('<div class="dashboard-card" data-v-d35b647a><div class="card-header" data-v-d35b647a><h3 data-v-d35b647a>系统状态</h3></div><div class="card-content" data-v-d35b647a><div class="system-status" data-v-d35b647a><div class="status-item" data-v-d35b647a><div class="status-indicator online" data-v-d35b647a></div><div class="status-info" data-v-d35b647a><div class="status-label" data-v-d35b647a>数据库</div><div class="status-value" data-v-d35b647a>正常</div></div></div><div class="status-item" data-v-d35b647a><div class="status-indicator online" data-v-d35b647a></div><div class="status-info" data-v-d35b647a><div class="status-label" data-v-d35b647a>存储服务</div><div class="status-value" data-v-d35b647a>正常</div></div></div><div class="status-item" data-v-d35b647a><div class="status-indicator online" data-v-d35b647a></div><div class="status-info" data-v-d35b647a><div class="status-label" data-v-d35b647a>支付服务</div><div class="status-value" data-v-d35b647a>正常</div></div></div></div></div></div>',1))])])]);var wa}}}),[["__scopeId","data-v-d35b647a"]]);export{ma as default};
