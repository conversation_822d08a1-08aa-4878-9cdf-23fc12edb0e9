import{u as a,_ as e}from"./index-BLSkTtYG.js";import{d as s,i as t,j as i,c,a as l,p as o,F as n,s as r,q as d,H as u,h as v,J as p,t as m,ae as y,af as k,e as _,u as g,X as b,a8 as h,w as f,r as C,a7 as w,ab as S,o as D}from"./vendor-DhoxJlSg.js";const j={class:"favorites-view"},I={class:"favorites-tabs"},P=["onClick"],O={class:"tab-count"},F={class:"favorites-content"},x={key:0,class:"tab-panel"},V={key:0,class:"favorites-grid"},$={class:"item-header"},q={class:"item-icon"},B=["onClick"],H={class:"item-content"},J={class:"item-name"},N={class:"item-description"},T={class:"item-meta"},Y={class:"category"},z={class:"clicks"},A={class:"item-actions"},E=["onClick"],G={key:1,class:"empty-state"},K={key:1,class:"tab-panel"},L={key:0,class:"favorites-grid"},M={class:"item-header"},Q={class:"item-image"},R=["src","alt"],U=["onClick"],W={class:"item-content"},X={class:"item-name"},Z={class:"item-description"},aa={class:"item-price"},ea={class:"current-price"},sa={key:0,class:"original-price"},ta={class:"item-actions"},ia=["onClick"],ca=["onClick"],la={key:1,class:"empty-state"},oa={key:0,class:"loading-state"},na=e(s({__name:"FavoritesView",setup(e){const s=a(),na=t(!0),ra=t("tools"),da=t([]),ua=t([]),va=[{key:"tools",label:"工具",icon:y},{key:"products",label:"产品",icon:k}],pa=async(a,e)=>{try{"tool"===a?da.value=da.value.filter(a=>a.id!==e):ua.value=ua.value.filter(a=>a.id!==e)}catch(s){console.error("取消收藏失败:",s)}};return i(()=>{(async()=>{try{na.value=!0,await new Promise(a=>setTimeout(a,1e3)),da.value=[{id:"1",name:"VS Code",description:"强大的代码编辑器",url:"https://code.visualstudio.com",icon:"💻",category:{id:"1",name:"开发工具",icon:"💻",color:"#0078d4",count:0,sort_order:0,is_active:!0,created_at:"",updated_at:""},tags:[],is_favorite:!0,click_count:150,isFeature:!0,status:"active",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),sort_order:0}],ua.value=[{id:"1",name:"高效办公套件",description:"提升办公效率的完整解决方案",shortDescription:"办公效率工具",price:299,originalPrice:399,currency:"CNY",category:{id:"1",name:"办公软件",icon:"📊",color:"#0078d4",count:0,sort_order:0,is_active:!0,created_at:"",updated_at:""},images:["/placeholder.jpg"],features:["文档处理","项目管理"],is_featured:!0,isDigital:!0,status:"active",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),createdBy:"user1",sort_order:0}]}catch(a){console.error("加载收藏失败:",a)}finally{na.value=!1}})()}),(a,e)=>{const t=C("router-link");return D(),c("div",j,[e[12]||(e[12]=l("div",{class:"favorites-header"},[l("h1",null,"我的收藏"),l("p",null,"管理您收藏的工具和产品")],-1)),l("div",I,[(D(),c(n,null,r(va,a=>{return l("button",{key:a.key,class:d(["tab",{active:ra.value===a.key}]),onClick:e=>ra.value=a.key},[(D(),u(p(a.icon),{class:"tab-icon"})),v(" "+m(a.label)+" ",1),l("span",O,m((e=a.key,"tools"===e?da.value.length:"products"===e?ua.value.length:0)),1)],10,P);var e}),64))]),l("div",F,["tools"===ra.value?(D(),c("div",x,[da.value.length>0?(D(),c("div",V,[(D(!0),c(n,null,r(da.value,a=>(D(),c("div",{key:a.id,class:"favorite-item tool-item"},[l("div",$,[l("div",q,m(a.icon||"🔧"),1),l("button",{class:"remove-btn",onClick:e=>pa("tool",a.id)},[_(g(b),{class:"icon"})],8,B)]),l("div",H,[l("h3",J,m(a.name),1),l("p",N,m(a.description),1),l("div",T,[l("span",Y,m(a.category.name),1),l("span",z,m(a.click_count)+" 次访问",1)])]),l("div",A,[l("button",{class:"action-btn primary",onClick:e=>(a=>{window.open(a.url,"_blank","noopener,noreferrer")})(a)},[_(g(h),{class:"icon"}),e[0]||(e[0]=v(" 打开工具 "))],8,E)])]))),128))])):(D(),c("div",G,[e[2]||(e[2]=l("div",{class:"empty-icon"},"🔧",-1)),e[3]||(e[3]=l("h3",null,"暂无收藏的工具",-1)),e[4]||(e[4]=l("p",null,"去发现一些有用的工具并收藏它们吧！",-1)),_(t,{to:"/tools",class:"empty-action"},{default:f(()=>e[1]||(e[1]=[v("浏览工具")])),_:1,__:[1]})]))])):o("",!0),"products"===ra.value?(D(),c("div",K,[ua.value.length>0?(D(),c("div",L,[(D(!0),c(n,null,r(ua.value,a=>(D(),c("div",{key:a.id,class:"favorite-item product-item"},[l("div",M,[l("div",Q,[l("img",{src:a.images[0]||"/placeholder.jpg",alt:a.name},null,8,R)]),l("button",{class:"remove-btn",onClick:e=>pa("product",a.id)},[_(g(b),{class:"icon"})],8,U)]),l("div",W,[l("h3",X,m(a.name),1),l("p",Z,m(a.shortDescription||a.description),1),l("div",aa,[l("span",ea,"¥"+m(a.price),1),a.originalPrice&&a.originalPrice>a.price?(D(),c("span",sa," ¥"+m(a.originalPrice),1)):o("",!0)])]),l("div",ta,[l("button",{class:"action-btn secondary",onClick:e=>(a=>{s.push(`/product/${a.id}`)})(a)},[_(g(w),{class:"icon"}),e[5]||(e[5]=v(" 查看详情 "))],8,ia),l("button",{class:"action-btn primary",onClick:e=>(a=>{s.push(`/payment?product=${a.id}`)})(a)},[_(g(S),{class:"icon"}),e[6]||(e[6]=v(" 立即购买 "))],8,ca)])]))),128))])):(D(),c("div",la,[e[8]||(e[8]=l("div",{class:"empty-icon"},"🛍️",-1)),e[9]||(e[9]=l("h3",null,"暂无收藏的产品",-1)),e[10]||(e[10]=l("p",null,"去发现一些优质产品并收藏它们吧！",-1)),_(t,{to:"/products",class:"empty-action"},{default:f(()=>e[7]||(e[7]=[v("浏览产品")])),_:1,__:[7]})]))])):o("",!0)]),na.value?(D(),c("div",oa,e[11]||(e[11]=[l("div",{class:"loading-spinner"},null,-1),l("p",null,"正在加载收藏...",-1)]))):o("",!0)])}}}),[["__scopeId","data-v-be666d38"]]);export{na as default};
