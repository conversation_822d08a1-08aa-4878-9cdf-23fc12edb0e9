var e=Object.defineProperty,a=(a,t,s)=>((a,t,s)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[t]=s)(a,"symbol"!=typeof t?t+"":t,s);import{d as t,r as s,c as l,o,n,w as i,m as c,H as r,q as u,t as d,y as v,A as p,u as g,a2 as h,B as m,D as y,X as f,aj as k,F as w,z as b,v as _,I as C,C as S,ae as x,a7 as T,N as q,K as $,ak as M,R as O,al as H,am as E,G as R,a1 as A,an as D,a0 as I,a6 as B,x as P,ao as U}from"./vendor-CSeT1gXp.js";import{s as F,T as V,u as G,a as j,_ as z,b as Q,c as N}from"./index-BKFWLFbU.js";import{u as J}from"./categories-V_cSW4yv.js";const K=new class{constructor(){a(this,"searchHistory",[]),a(this,"popularQueries",new Map)}async search(e){const a=Date.now(),{query:t,type:s="all",limit:l=20,offset:o=0}=e;try{let l=[],o=0,n={categories:[],tags:[],priceRanges:[]};switch(this.addToHistory(t,s),s){case"tools":const a=await this.searchTools(e);l=a.items,o=a.total,n=a.facets;break;case"products":const t=await this.searchProducts(e);l=t.items,o=t.total,n=t.facets;break;case"categories":const s=await this.searchCategories(e);l=s.items,o=s.total;break;default:const i=await this.searchAll(e);l=i.items,o=i.total,n=i.facets}const i=await this.generateSuggestions(t,s);return{items:l,total:o,query:t,suggestions:i,facets:n,searchTime:Date.now()-a}}catch(n){throw console.error("搜索失败:",n),n}}async searchTools(e){const{query:a,category:t,tags:s,sortBy:l="relevance",sortOrder:o="desc",limit:n=20,offset:i=0}=e;let c=F.from(V.TOOLS).select("\n        *,\n        categories!inner(name, icon, color),\n        tool_tags!inner(tags!inner(name, color))\n      ",{count:"exact"});a&&(c=c.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%,\n        meta_title.ilike.%${a}%,\n        meta_description.ilike.%${a}%\n      `)),t&&(c=c.eq("category_id",t)),s&&s.length>0&&(c=c.in("tool_tags.tag_id",s)),c=c.eq("status","active"),"relevance"===l&&a?(c=c.order("is_featured",{ascending:!1}),c=c.order("click_count",{ascending:!1})):c=c.order(l,{ascending:"asc"===o}),c=c.range(i,i+n-1);const{data:r,error:u,count:d}=await c;if(u)throw u;return{items:r||[],total:d||0,facets:await this.generateToolsFacets(a,t,s)}}async searchProducts(e){const{query:a,category:t,priceRange:s,sortBy:l="relevance",sortOrder:o="desc",limit:n=20,offset:i=0}=e;let c=F.from(V.PRODUCTS).select("\n        *,\n        product_categories!inner(name, icon, color)\n      ",{count:"exact"});a&&(c=c.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%,\n        short_description.ilike.%${a}%,\n        meta_title.ilike.%${a}%,\n        meta_description.ilike.%${a}%\n      `)),t&&(c=c.eq("category_id",t)),s&&(c=c.gte("price",s[0]).lte("price",s[1])),c=c.eq("status","active"),c="relevance"===l&&a?c.order("is_featured",{ascending:!1}):c.order(l,{ascending:"asc"===o}),c=c.range(i,i+n-1);const{data:r,error:u,count:d}=await c;if(u)throw u;return{items:r||[],total:d||0,facets:await this.generateProductsFacets(a,t,s)}}async searchCategories(e){const{query:a,limit:t=20,offset:s=0}=e;let l=F.from(V.CATEGORIES).select("*",{count:"exact"});a&&(l=l.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%\n      `)),l=l.eq("is_active",!0).order("sort_order",{ascending:!0}).range(s,s+t-1);const{data:o,error:n,count:i}=await l;if(n)throw n;return{items:o||[],total:i||0}}async searchAll(e){const{limit:a=20}=e,[t,s,l]=await Promise.all([this.searchTools({...e,limit:Math.ceil(a/3)}),this.searchProducts({...e,limit:Math.ceil(a/3)}),this.searchCategories({...e,limit:Math.ceil(a/3)})]);return{items:[...t.items.map(e=>({...e,_type:"tool"})),...s.items.map(e=>({...e,_type:"product"})),...l.items.map(e=>({...e,_type:"category"}))],total:t.total+s.total+l.total,facets:{categories:[...t.facets.categories,...s.facets.categories],tags:t.facets.tags,priceRanges:s.facets.priceRanges}}}async generateToolsFacets(e,a,t){const s=F.from(V.CATEGORIES).select("\n        id, name,\n        tools!inner(id)\n      ",{count:"exact"}).eq("is_active",!0);e&&s.or(`\n        tools.name.ilike.%${e}%,\n        tools.description.ilike.%${e}%\n      `);const{data:l}=await s,o=(l||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.tools)?void 0:a.length)||0}}),n=F.from(V.TAGS).select("\n        id, name,\n        tool_tags!inner(tools!inner(id))\n      ",{count:"exact"});e&&n.or(`\n        tool_tags.tools.name.ilike.%${e}%,\n        tool_tags.tools.description.ilike.%${e}%\n      `);const{data:i}=await n;return{categories:o,tags:(i||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.tool_tags)?void 0:a.length)||0}}),priceRanges:[]}}async generateProductsFacets(e,a,t){const s=F.from(V.PRODUCT_CATEGORIES).select("\n        id, name,\n        products!inner(id)\n      ",{count:"exact"});e&&s.or(`\n        products.name.ilike.%${e}%,\n        products.description.ilike.%${e}%\n      `);const{data:l}=await s;return{categories:(l||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.products)?void 0:a.length)||0}}),tags:[],priceRanges:[{range:"0-50",count:0},{range:"50-100",count:0},{range:"100-500",count:0},{range:"500+",count:0}]}}async generateSuggestions(e,a){if(!e||e.length<2)return[];const t=[];try{const{data:a}=await F.from(V.TOOLS).select("name").ilike("name",`%${e}%`).eq("status","active").limit(5);null==a||a.forEach(e=>{t.includes(e.name)||t.push(e.name)});const{data:s}=await F.from(V.PRODUCTS).select("name").ilike("name",`%${e}%`).eq("status","active").limit(5);null==s||s.forEach(e=>{t.includes(e.name)||t.push(e.name)});const{data:l}=await F.from(V.CATEGORIES).select("name").ilike("name",`%${e}%`).eq("is_active",!0).limit(3);null==l||l.forEach(e=>{t.includes(e.name)||t.push(e.name)})}catch(s){console.error("生成搜索建议失败:",s)}return t.slice(0,8)}async getPopularSearches(e=10){return Array.from(this.popularQueries.entries()).sort((e,a)=>a[1]-e[1]).slice(0,e).map(([e])=>e)}getSearchHistory(e=10){return this.searchHistory.sort((e,a)=>a.timestamp.getTime()-e.timestamp.getTime()).slice(0,e)}clearSearchHistory(){this.searchHistory=[],localStorage.removeItem("search_history")}saveHistoryToStorage(){try{localStorage.setItem("search_history",JSON.stringify(this.searchHistory))}catch(e){console.error("保存搜索历史失败:",e)}}loadHistoryFromStorage(){try{const e=localStorage.getItem("search_history");if(e){const a=JSON.parse(e);this.searchHistory=a.map(e=>({...e,timestamp:new Date(e.timestamp)}))}}catch(e){console.error("加载搜索历史失败:",e),this.searchHistory=[]}}addToHistory(e,a){const t={id:Date.now().toString(),query:e,type:a,timestamp:new Date,results_count:0};this.searchHistory=this.searchHistory.filter(t=>t.query!==e||t.type!==a),this.searchHistory.unshift(t),this.searchHistory.length>50&&(this.searchHistory=this.searchHistory.slice(0,50));const s=this.popularQueries.get(e)||0;this.popularQueries.set(e,s+1)}async getSmartSuggestions(e){const a=[];if(!e||e.length<2){return(await this.getPopularSearches(5)).map(e=>({text:e,type:"query"}))}try{(await this.generateSuggestions(e,"all")).forEach(e=>{a.push({text:e,type:"query"})});const{data:t}=await F.from(V.CATEGORIES).select("name").ilike("name",`%${e}%`).eq("is_active",!0).limit(3);null==t||t.forEach(e=>{a.push({text:e.name,type:"category"})});const{data:s}=await F.from(V.TAGS).select("name").ilike("name",`%${e}%`).limit(3);null==s||s.forEach(e=>{a.push({text:e.name,type:"tag"})})}catch(t){console.error("获取智能建议失败:",t)}return a.slice(0,10)}},L={class:"search-input-container"},W={class:"search-input-wrapper"},X=["placeholder"],Y={class:"search-actions"},Z={key:0,class:"suggestions-dropdown"},ee={key:0,class:"suggestions-section"},ae=["onClick","onMouseenter"],te={class:"suggestion-text"},se={class:"suggestion-type"},le={key:1,class:"suggestions-section"},oe=["onClick","onMouseenter"],ne={class:"suggestion-text"},ie={class:"suggestion-meta"},ce={key:2,class:"suggestions-section"},re={class:"popular-searches"},ue=["onClick"],de={key:0,class:"advanced-search-panel"},ve={class:"advanced-search-content"},pe={class:"advanced-row"},ge={class:"advanced-group"},he=["value"],me={class:"advanced-group"},ye={class:"advanced-group"},fe={key:0,class:"advanced-row"},ke={class:"advanced-group"},we={class:"price-range"},be={class:"advanced-row"},_e={class:"advanced-group full-width"},Ce={class:"tags-input"},Se={class:"selected-tags"},xe=["onClick"],Te={key:0,class:"tags-dropdown"},qe=["onClick"],$e={key:1,class:"search-status"},Me={key:2,class:"search-stats"},Oe=z(t({__name:"EnhancedSearchBox",props:{placeholder:{default:"搜索工具、产品、分类..."},autoFocus:{type:Boolean,default:!1},showAdvanced:{type:Boolean,default:!1},defaultType:{default:"all"}},emits:["search","clear","focus","blur"],setup(e,{expose:a,emit:t}){const A=e,D=t,I=G(),B=J(),P=s(),U=s(""),F=s(A.defaultType),V=s(!1),z=s(!1),Q=s(!1),N=s(A.showAdvanced),Oe=s(!1),He=s(-1),Ee=s([]),Re=s([]),Ae=s([]),De=s({category:"",sortBy:"relevance",sortOrder:"desc",priceMin:null,priceMax:null}),Ie=s([]),Be=s(""),Pe=s([]),Ue=s(null),Fe=l(()=>B.categories),Ve=l(()=>[...Ee.value,...Re.value.map(e=>({text:e.query,type:"query"}))]),Ge=()=>{V.value=!0,z.value=!0,Q.value=!0,D("focus"),Ne()},je=()=>{setTimeout(()=>{V.value=!1,Q.value=!1,He.value=-1},200),D("blur")},ze=()=>{He.value=-1,Je()},Qe=e=>{var a;switch(e.key){case"ArrowDown":e.preventDefault(),He.value=Math.min(He.value+1,Ve.value.length-1);break;case"ArrowUp":e.preventDefault(),He.value=Math.max(He.value-1,-1);break;case"Enter":e.preventDefault(),He.value>=0?Ke(Ve.value[He.value].text):Le();break;case"Escape":null==(a=P.value)||a.blur()}},Ne=async()=>{try{U.value?Ee.value=await K.getSmartSuggestions(U.value):(Ee.value=[],Re.value=K.getSearchHistory(5),Ae.value=await K.getPopularSearches(8))}catch(e){console.error("加载搜索建议失败:",e)}},Je=j(Ne,300),Ke=e=>{U.value=e,Q.value=!1,Le()},Le=async()=>{if(U.value.trim())try{Oe.value=!0;const e={query:U.value.trim(),type:F.value,category:De.value.category||void 0,tags:Ie.value.map(e=>e.id),priceRange:De.value.priceMin&&De.value.priceMax?[De.value.priceMin,De.value.priceMax]:void 0,sortBy:De.value.sortBy,sortOrder:De.value.sortOrder,limit:20},a=await K.search(e);Ue.value=a,D("search",a),I.push({name:"SearchResults",query:{q:U.value,type:F.value,...De.value}})}catch(e){console.error("搜索失败:",e)}finally{Oe.value=!1}},We=()=>{var e;U.value="",Ue.value=null,D("clear"),null==(e=P.value)||e.focus()},Xe=()=>{K.clearSearchHistory(),Re.value=[]},Ye=()=>{N.value=!N.value,z.value=N.value},Ze=()=>{De.value={category:"",sortBy:"relevance",sortOrder:"desc",priceMin:null,priceMax:null},Ie.value=[]},ea=()=>{U.value&&Le()},aa=async()=>{Be.value||(Pe.value=[])},ta=e=>{switch(e){case"category":return E;case"tag":return H;case"tool":case"product":return O;default:return M}},sa=e=>{switch(e){case"category":return"分类";case"tag":return"标签";case"tool":return"工具";case"product":return"产品";default:return"搜索"}},la=e=>{const a=(new Date).getTime()-e.getTime(),t=Math.floor(a/6e4),s=Math.floor(a/36e5),l=Math.floor(a/864e5);return t<1?"刚刚":t<60?`${t}分钟前`:s<24?`${s}小时前`:`${l}天前`};return o(async()=>{var e;A.autoFocus&&(await n(),null==(e=P.value)||e.focus()),0===B.categories.length&&await B.fetchCategories()}),i(U,()=>{U.value||(Ue.value=null)}),a({focus:()=>{var e;return null==(e=P.value)?void 0:e.focus()},clear:We,search:Le}),(e,a)=>(R(),c("div",{class:r(["enhanced-search-box",{"is-focused":V.value,"is-expanded":z.value}])},[u("div",L,[u("div",W,[v(g(h),{class:"search-icon"}),p(u("input",{ref_key:"searchInput",ref:P,"onUpdate:modelValue":a[0]||(a[0]=e=>U.value=e),type:"text",class:"search-input",placeholder:e.placeholder,onFocus:Ge,onBlur:je,onKeydown:Qe,onInput:ze},null,40,X),[[m,U.value]]),u("div",Y,[p(u("select",{"onUpdate:modelValue":a[1]||(a[1]=e=>F.value=e),class:"search-type-select"},a[8]||(a[8]=[u("option",{value:"all"},"全部",-1),u("option",{value:"tools"},"工具",-1),u("option",{value:"products"},"产品",-1),u("option",{value:"categories"},"分类",-1)]),512),[[y,F.value]]),U.value?(R(),c("button",{key:0,onClick:We,class:"clear-button",type:"button"},[v(g(f),{class:"icon"})])):d("",!0),u("button",{onClick:Ye,class:r(["advanced-button",{active:N.value}]),type:"button"},[v(g(k),{class:"icon"})],2)])]),Q.value&&(Ee.value.length>0||Re.value.length>0)?(R(),c("div",Z,[Ee.value.length>0?(R(),c("div",ee,[a[9]||(a[9]=u("div",{class:"suggestions-header"},"搜索建议",-1)),(R(!0),c(w,null,b(Ee.value,(e,a)=>(R(),c("div",{key:`suggestion-${a}`,class:r(["suggestion-item",{active:He.value===a}]),onClick:a=>Ke(e.text),onMouseenter:e=>He.value=a},[(R(),_(C(ta(e.type)),{class:"suggestion-icon"})),u("span",te,S(e.text),1),u("span",se,S(sa(e.type)),1)],42,ae))),128))])):d("",!0),Re.value.length>0&&!U.value?(R(),c("div",le,[u("div",{class:"suggestions-header"},[a[10]||(a[10]=u("span",null,"最近搜索",-1)),u("button",{onClick:Xe,class:"clear-history-button"},"清除")]),(R(!0),c(w,null,b(Re.value,(e,a)=>(R(),c("div",{key:`history-${e.id}`,class:r(["suggestion-item history-item",{active:He.value===Ee.value.length+a}]),onClick:a=>Ke(e.query),onMouseenter:e=>He.value=Ee.value.length+a},[v(g(x),{class:"suggestion-icon"}),u("span",ne,S(e.query),1),u("span",ie,S(la(e.timestamp)),1)],42,oe))),128))])):d("",!0),Ae.value.length>0&&!U.value?(R(),c("div",ce,[a[11]||(a[11]=u("div",{class:"suggestions-header"},"热门搜索",-1)),u("div",re,[(R(!0),c(w,null,b(Ae.value,e=>(R(),c("button",{key:e,onClick:a=>Ke(e),class:"popular-search-tag"},S(e),9,ue))),128))])])):d("",!0)])):d("",!0)]),N.value?(R(),c("div",de,[u("div",ve,[u("div",pe,[u("div",ge,[a[13]||(a[13]=u("label",{class:"advanced-label"},"分类",-1)),p(u("select",{"onUpdate:modelValue":a[2]||(a[2]=e=>De.value.category=e),class:"advanced-select"},[a[12]||(a[12]=u("option",{value:""},"所有分类",-1)),(R(!0),c(w,null,b(Fe.value,e=>(R(),c("option",{key:e.id,value:e.id},S(e.name),9,he))),128))],512),[[y,De.value.category]])]),u("div",me,[a[15]||(a[15]=u("label",{class:"advanced-label"},"排序",-1)),p(u("select",{"onUpdate:modelValue":a[3]||(a[3]=e=>De.value.sortBy=e),class:"advanced-select"},a[14]||(a[14]=[T('<option value="relevance" data-v-b8ed8899>相关性</option><option value="name" data-v-b8ed8899>名称</option><option value="created_at" data-v-b8ed8899>创建时间</option><option value="click_count" data-v-b8ed8899>热度</option><option value="price" data-v-b8ed8899>价格</option>',5)]),512),[[y,De.value.sortBy]])]),u("div",ye,[a[17]||(a[17]=u("label",{class:"advanced-label"},"顺序",-1)),p(u("select",{"onUpdate:modelValue":a[4]||(a[4]=e=>De.value.sortOrder=e),class:"advanced-select"},a[16]||(a[16]=[u("option",{value:"desc"},"降序",-1),u("option",{value:"asc"},"升序",-1)]),512),[[y,De.value.sortOrder]])])]),"products"===F.value?(R(),c("div",fe,[u("div",ke,[a[19]||(a[19]=u("label",{class:"advanced-label"},"价格范围",-1)),u("div",we,[p(u("input",{"onUpdate:modelValue":a[5]||(a[5]=e=>De.value.priceMin=e),type:"number",placeholder:"最低价",class:"price-input",min:"0"},null,512),[[m,De.value.priceMin,void 0,{number:!0}]]),a[18]||(a[18]=u("span",{class:"price-separator"},"-",-1)),p(u("input",{"onUpdate:modelValue":a[6]||(a[6]=e=>De.value.priceMax=e),type:"number",placeholder:"最高价",class:"price-input",min:"0"},null,512),[[m,De.value.priceMax,void 0,{number:!0}]])])])])):d("",!0),u("div",be,[u("div",_e,[a[20]||(a[20]=u("label",{class:"advanced-label"},"标签",-1)),u("div",Ce,[u("div",Se,[(R(!0),c(w,null,b(Ie.value,e=>(R(),c("span",{key:e.id,class:"selected-tag"},[q(S(e.name)+" ",1),u("button",{onClick:a=>(e=>{Ie.value=Ie.value.filter(a=>a.id!==e.id)})(e),class:"remove-tag"},[v(g(f),{class:"icon"})],8,xe)]))),128))]),p(u("input",{"onUpdate:modelValue":a[7]||(a[7]=e=>Be.value=e),type:"text",placeholder:"输入标签名称...",class:"tag-input",onInput:aa},null,544),[[m,Be.value]]),Pe.value.length>0?(R(),c("div",Te,[(R(!0),c(w,null,b(Pe.value,e=>(R(),c("div",{key:e.id,class:"tag-option",onClick:a=>(e=>{Ie.value.find(a=>a.id===e.id)||Ie.value.push(e),Be.value="",Pe.value=[]})(e)},[u("span",{class:"tag-color",style:$({backgroundColor:e.color})},null,4),q(" "+S(e.name),1)],8,qe))),128))])):d("",!0)])])]),u("div",{class:"advanced-actions"},[u("button",{onClick:Ze,class:"reset-button"},"重置"),u("button",{onClick:ea,class:"apply-button"},"应用筛选")])])])):d("",!0),Oe.value?(R(),c("div",$e,a[21]||(a[21]=[u("div",{class:"search-loading"},[u("div",{class:"loading-spinner"}),u("span",null,"搜索中...")],-1)]))):d("",!0),Ue.value?(R(),c("div",Me," 找到 "+S(Ue.value.total)+" 个结果，用时 "+S(Ue.value.searchTime)+"ms ",1)):d("",!0)],2))}}),[["__scopeId","data-v-b8ed8899"]]),He={class:"enhanced-home-view"},Ee={class:"hero-section"},Re={class:"container"},Ae={class:"hero-content"},De={class:"search-section"},Ie={class:"main-content"},Be={class:"container"},Pe={class:"content-layout"},Ue={class:"sidebar"},Fe={class:"sidebar-section"},Ve={class:"nav-links"},Ge={class:"sidebar-section"},je={class:"category-filters"},ze=["onClick"],Qe={class:"tools-grid"},Ne=["onClick"],Je=["onClick"],Ke={class:"tool-header"},Le={class:"tool-icon"},We={class:"tool-title"},Xe={class:"tool-subtitle"},Ye={class:"tool-body"},Ze={class:"tool-description"},ea={class:"tool-tags"},aa={key:0,class:"tool-tag more"},ta={key:0,class:"empty-state"},sa=z(t({__name:"EnhancedHomeView",setup(e){const a=Q(),t=J(),n=N(),i=G(),p=s(null),h=s(null),m=l(()=>t.categories),y=l(()=>{if(h.value)return h.value.items||[];let e=a.tools;return p.value&&(e=e.filter(e=>e.category_id===p.value)),e}),f=e=>{h.value=e},k=()=>{h.value=null},_=()=>{h.value=null,p.value=null};return o(async()=>{a.initialized||await a.initialize(),t.initialized||await t.initialize()}),(e,t)=>{const s=I("router-link");return R(),c("div",He,[u("header",Ee,[u("div",Re,[u("div",Ae,[t[0]||(t[0]=u("div",{class:"logo-section"},[u("div",{class:"logo-icon"},"🚀"),u("h1",{class:"main-title"},"高效工具导航站")],-1)),t[1]||(t[1]=u("p",{class:"tagline"},"精心挑选的优质工具，让您的工作效率倍增",-1)),u("div",De,[v(Oe,{placeholder:"搜索工具、分类或功能...","auto-focus":!1,onSearch:f,onClear:k})])])])]),u("main",Ie,[u("div",Be,[u("div",Pe,[u("aside",Ue,[u("div",Fe,[t[5]||(t[5]=u("h3",null,"导航",-1)),u("nav",Ve,[v(s,{to:"/tools",class:"nav-link active"},{default:A(()=>[v(g(D),{class:"icon"}),t[2]||(t[2]=q(" 全部工具 "))]),_:1,__:[2]}),v(s,{to:"/favorites",class:"nav-link"},{default:A(()=>[v(g(B),{class:"icon"}),t[3]||(t[3]=q(" 我的收藏 "))]),_:1,__:[3]}),v(s,{to:"/products",class:"nav-link"},{default:A(()=>[v(g(O),{class:"icon"}),t[4]||(t[4]=q(" 我的产品 "))]),_:1,__:[4]})])]),u("div",Ge,[t[6]||(t[6]=u("h3",null,"分类",-1)),u("div",je,[(R(!0),c(w,null,b(m.value,e=>(R(),c("button",{key:e.id,class:r(["category-tag",{active:p.value===e.id}]),onClick:a=>{return t=e.id,void(p.value=p.value===t?null:t);var t}},S(e.name),11,ze))),128))])])]),u("section",Qe,[(R(!0),c(w,null,b(y.value,(e,t)=>(R(),c("div",{key:e.id,class:"tool-card",style:$({"--index":t}),onClick:t=>(async e=>{await a.incrementClickCount(e.id),window.open(e.url,"_blank","noopener,noreferrer")})(e)},[u("button",{class:r(["favorite-btn",{favorited:e.isFavorite}]),onClick:P(t=>(async e=>{n.isAuthenticated?await a.toggleFavorite(e.id):i.push("/auth/login")})(e),["stop"])},[v(g(U),{class:"icon"})],10,Je),u("div",Ke,[u("div",Le,S(e.icon||"🔧"),1),u("h3",We,S(e.name),1),u("p",Xe,S(e.short_description||e.description.slice(0,50)+"..."),1)]),u("div",Ye,[u("p",Ze,S(e.description),1),u("div",ea,[(R(!0),c(w,null,b(e.tags.slice(0,3),e=>(R(),c("span",{key:e.id,class:"tool-tag"},S(e.name),1))),128)),e.tags.length>3?(R(),c("span",aa," +"+S(e.tags.length-3),1)):d("",!0)])])],12,Ne))),128)),0===y.value.length?(R(),c("div",ta,[t[7]||(t[7]=u("div",{class:"empty-icon"},"🔍",-1)),t[8]||(t[8]=u("h3",null,"未找到相关工具",-1)),t[9]||(t[9]=u("p",null,"尝试使用其他关键词搜索，或浏览其他分类",-1)),u("button",{class:"empty-action",onClick:_}," 清除搜索条件 ")])):d("",!0)])])])]),t[10]||(t[10]=u("footer",{class:"footer"},[u("div",{class:"container"},[u("p",null,"© 2024 高效工具导航站 | 让您的工作更智能、更高效")])],-1))])}}}),[["__scopeId","data-v-81436b60"]]);export{sa as default};
