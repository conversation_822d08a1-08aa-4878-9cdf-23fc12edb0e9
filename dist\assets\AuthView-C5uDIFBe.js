import{d as a,c as s,a as t,e as o,w as e,r as i,o as l}from"./vendor-DhoxJlSg.js";import{_ as r}from"./index-BLSkTtYG.js";const c={class:"auth-view"},d={class:"auth-container"},n={class:"auth-header"},u={class:"auth-content"},v=r(a({__name:"AuthView",setup:a=>(a,r)=>{const v=i("router-link"),_=i("router-view");return l(),s("div",c,[t("div",d,[t("div",n,[o(v,{to:"/",class:"logo"},{default:e(()=>r[0]||(r[0]=[t("div",{class:"logo-icon"},"🚀",-1),t("div",{class:"logo-text"},"工具导航站",-1)])),_:1,__:[0]})]),t("div",u,[o(_)]),r[1]||(r[1]=t("div",{class:"auth-footer"},[t("p",null,"© 2024 工具导航站. 保留所有权利.")],-1))])])}}),[["__scopeId","data-v-7a2469ee"]]);export{v as default};
